# 数据库配置
DATABASE_URL=mysql+pymysql://root:123456@localhost:3306/focusee_db

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
APP_NAME=FocuSee Registration System
APP_VERSION=1.0.0
DEBUG=True

# API 文档配置
# 设置为 False 可以在生产环境中禁用 API 文档
ENABLE_DOCS=True

# 文件存储配置
DOWNLOADS_DIR=./downloads
MAX_FILE_SIZE=100000000

# 管理员默认账户
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# 支付宝配置
# 应用ID（从支付宝开放平台获取）
ALIPAY_APP_ID=2021000000000000

# 应用私钥（RSA2格式，去掉头尾和换行符）
ALIPAY_APP_PRIVATE_KEY=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...

# 支付宝公钥（RSA2格式，去掉头尾和换行符）
ALIPAY_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...

# 异步通知地址（支付宝回调地址）
ALIPAY_NOTIFY_URL=https://yourdomain.com/api/payment/alipay/notify

# 同步跳转地址（支付完成后跳转地址）
ALIPAY_RETURN_URL=https://yourdomain.com/user/payment

# 生产环境示例配置
# DEBUG=False
# ENABLE_DOCS=False
# SECRET_KEY=your-very-secure-secret-key-for-production
# DATABASE_URL=mysql+pymysql://username:password@production-host:3306/focusee_db
