from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models import Product
from app.schemas.product import ProductCreate, ProductUpdate
import logging

logger = logging.getLogger(__name__)

class ProductService:
    """产品管理服务"""
    
    @staticmethod
    def create_product(db: Session, product_data: ProductCreate) -> Optional[Product]:
        """创建产品"""
        try:
            # 检查产品代码是否已存在
            existing_product = db.query(Product).filter(Product.code == product_data.code).first()
            if existing_product:
                logger.error(f"Product code {product_data.code} already exists")
                return None
            
            product = Product(
                name=product_data.name,
                code=product_data.code,
                description=product_data.description,
                version=product_data.version,
                price=product_data.price,
                is_active=product_data.is_active
            )
            
            db.add(product)
            db.commit()
            db.refresh(product)
            
            logger.info(f"Product created: {product.name} ({product.code})")
            return product
            
        except Exception as e:
            logger.error(f"Error creating product: {str(e)}")
            db.rollback()
            return None
    
    @staticmethod
    def get_product_by_id(db: Session, product_id: int) -> Optional[Product]:
        """根据ID获取产品"""
        try:
            return db.query(Product).filter(Product.id == product_id).first()
        except Exception as e:
            logger.error(f"Error getting product by id {product_id}: {str(e)}")
            return None
    
    @staticmethod
    def get_product_by_code(db: Session, code: str) -> Optional[Product]:
        """根据代码获取产品"""
        try:
            return db.query(Product).filter(Product.code == code).first()
        except Exception as e:
            logger.error(f"Error getting product by code {code}: {str(e)}")
            return None
    
    @staticmethod
    def get_products(db: Session, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None) -> List[Product]:
        """获取产品列表"""
        try:
            query = db.query(Product)
            
            if is_active is not None:
                query = query.filter(Product.is_active == is_active)
            
            return query.offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error getting products: {str(e)}")
            return []
    
    @staticmethod
    def update_product(db: Session, product_id: int, product_data: ProductUpdate) -> Optional[Product]:
        """更新产品"""
        try:
            product = db.query(Product).filter(Product.id == product_id).first()
            if not product:
                logger.error(f"Product {product_id} not found")
                return None
            
            # 检查代码是否与其他产品冲突
            if product_data.code and product_data.code != product.code:
                existing_product = db.query(Product).filter(
                    and_(Product.code == product_data.code, Product.id != product_id)
                ).first()
                if existing_product:
                    logger.error(f"Product code {product_data.code} already exists")
                    return None
            
            # 更新字段
            update_data = product_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(product, field, value)
            
            db.commit()
            db.refresh(product)
            
            logger.info(f"Product updated: {product.name} ({product.code})")
            return product
            
        except Exception as e:
            logger.error(f"Error updating product {product_id}: {str(e)}")
            db.rollback()
            return None
    
    @staticmethod
    def delete_product(db: Session, product_id: int) -> bool:
        """删除产品（软删除）"""
        try:
            product = db.query(Product).filter(Product.id == product_id).first()
            if not product:
                logger.error(f"Product {product_id} not found")
                return False
            
            # 软删除：设置为不激活
            product.is_active = False
            db.commit()
            
            logger.info(f"Product deleted: {product.name} ({product.code})")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting product {product_id}: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def search_products(db: Session, keyword: str, skip: int = 0, limit: int = 100) -> List[Product]:
        """搜索产品"""
        try:
            query = db.query(Product).filter(
                and_(
                    Product.is_active == True,
                    (Product.name.contains(keyword) | 
                     Product.code.contains(keyword) | 
                     Product.description.contains(keyword))
                )
            )
            
            return query.offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error searching products with keyword {keyword}: {str(e)}")
            return []
    
    @staticmethod
    def get_product_count(db: Session, is_active: Optional[bool] = None) -> int:
        """获取产品总数"""
        try:
            query = db.query(Product)
            
            if is_active is not None:
                query = query.filter(Product.is_active == is_active)
            
            return query.count()
            
        except Exception as e:
            logger.error(f"Error getting product count: {str(e)}")
            return 0
