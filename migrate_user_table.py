#!/usr/bin/env python3
"""
用户表结构迁移脚本
添加 username 和 full_name 字段
"""

import pymysql
from sqlalchemy import create_engine, text
from app.config import settings
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_user_table():
    """迁移用户表结构"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        with engine.connect() as connection:
            # 检查 username 字段是否存在
            result = connection.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'username'
            """))
            
            username_exists = result.fetchone() is not None
            
            if not username_exists:
                logger.info("添加 username 字段...")
                connection.execute(text("""
                    ALTER TABLE users 
                    ADD COLUMN username VARCHAR(50) UNIQUE AFTER user_id,
                    ADD INDEX idx_users_username (username)
                """))
                logger.info("username 字段添加成功")
            else:
                logger.info("username 字段已存在，跳过")
            
            # 检查 full_name 字段是否存在
            result = connection.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'full_name'
            """))
            
            full_name_exists = result.fetchone() is not None
            
            if not full_name_exists:
                logger.info("添加 full_name 字段...")
                connection.execute(text("""
                    ALTER TABLE users 
                    ADD COLUMN full_name VARCHAR(100) AFTER phone
                """))
                logger.info("full_name 字段添加成功")
            else:
                logger.info("full_name 字段已存在，跳过")
            
            # 为现有用户设置 username（如果为空）
            logger.info("更新现有用户的 username 字段...")
            connection.execute(text("""
                UPDATE users 
                SET username = user_id 
                WHERE username IS NULL OR username = ''
            """))
            
            # 提交事务
            connection.commit()
            logger.info("用户表迁移完成")
            
    except Exception as e:
        logger.error(f"用户表迁移失败: {str(e)}")
        raise

if __name__ == "__main__":
    migrate_user_table()
    print("用户表迁移完成！")
