<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FocuSee 管理系统 - 登录</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .focusee-gradient {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .input-focus:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .login-button {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            bottom: 10%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="min-h-screen focusee-gradient relative overflow-hidden">
    <!-- 浮动装饰元素 -->
    <div class="floating-shapes">
        <div class="shape w-32 h-32 bg-white rounded-full"></div>
        <div class="shape w-24 h-24 bg-white rounded-lg"></div>
        <div class="shape w-16 h-16 bg-white rounded-full"></div>
    </div>

    <div class="min-h-screen flex items-center justify-center p-4 relative z-10">
        <div class="w-full max-w-md">
            <!-- 登录卡片 -->
            <div class="glass-effect rounded-2xl p-8 shadow-2xl">
                <!-- Logo 和标题 -->
                <div class="text-center mb-8">
                    <div class="mb-6">
                        <img src="https://www.gemoo-resource.com/focusee/img/<EMAIL>"
                             alt="FocuSee"
                             class="h-12 mx-auto mb-4">
                    </div>
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">FocuSee 管理系统</h1>
                    <p class="text-gray-600">智能屏幕录制管理平台</p>
                </div>

                <!-- 登录表单 -->
                <form method="POST" action="/admin/login-form" class="space-y-6">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-indigo-600"></i>用户名
                        </label>
                        <input type="text"
                               id="username"
                               name="username"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition duration-300"
                               placeholder="请输入管理员用户名">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-2 text-indigo-600"></i>密码
                        </label>
                        <input type="password"
                               id="password"
                               name="password"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition duration-300"
                               placeholder="请输入密码">
                    </div>

                    {% if error %}
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                        <i class="fas fa-exclamation-circle mr-2"></i>{{ error }}
                    </div>
                    {% endif %}

                    <button type="submit"
                            class="w-full login-button text-white py-3 px-4 rounded-lg font-semibold text-lg">
                        <i class="fas fa-sign-in-alt mr-2"></i>登录管理系统
                    </button>
                </form>

                <!-- 底部链接 -->
                <div class="mt-8 text-center">
                    <div class="flex justify-center space-x-6 text-sm text-gray-500">
                        <a href="/" class="hover:text-indigo-600 transition duration-300">
                            <i class="fas fa-home mr-1"></i>返回首页
                        </a>
                        <a href="/agent/login" class="hover:text-indigo-600 transition duration-300">
                            <i class="fas fa-users mr-1"></i>代理商登录
                        </a>
                        <a href="/contact" class="hover:text-indigo-600 transition duration-300">
                            <i class="fas fa-envelope mr-1"></i>联系我们
                        </a>
                    </div>
                </div>
            </div>

            <!-- 版权信息 -->
            <div class="text-center mt-8 text-white text-opacity-80">
                <p class="text-sm">
                    <i class="fas fa-copyright mr-1"></i>2024 FocuSee. 保留所有权利.
                </p>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 输入框焦点效果
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('transform', 'scale-105');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('transform', 'scale-105');
                });
            });

            // 登录表单提交处理
            const loginForm = document.querySelector('form');
            const loginButton = document.querySelector('button[type="submit"]');

            loginForm.addEventListener('submit', function(e) {
                // 显示加载状态
                loginButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>登录中...';
                loginButton.disabled = true;

                // 允许表单正常提交，不阻止默认行为
                // 如果登录失败，页面会重新加载并显示错误信息
                // 如果登录成功，会重定向到dashboard
            });
        });
    </script>
</body>
</html>
