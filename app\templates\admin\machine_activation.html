{% extends "admin/base.html" %}

{% block title %}机器码激活码管理 - FocuSee 管理系统{% endblock %}
{% block page_title %}机器码激活码管理{% endblock %}

{% block content %}
<div class="content-card">
    <!-- 顶部操作栏 -->
    <div style="margin-bottom: 20px;">
        <el-row :gutter="20">
            <el-col :span="6">
                <el-input
                    v-model="searchForm.machine_id"
                    placeholder="搜索机器码"
                    clearable
                    @clear="searchRecords"
                    @keyup.enter="searchRecords">
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
            </el-col>
            <el-col :span="6">
                <el-button type="primary" @click="searchRecords">
                    <el-icon><Search /></el-icon>
                    查询
                </el-button>
                <el-button @click="resetSearch">
                    <el-icon><Refresh /></el-icon>
                    重置
                </el-button>
            </el-col>
            <el-col :span="8">
                <div class="stats-summary">
                    <el-tag type="info">总计: {% raw %}{{ stats.total_count }}{% endraw %}</el-tag>
                    <el-tag type="success">活跃: {% raw %}{{ stats.active_count }}{% endraw %}</el-tag>
                    <el-tag type="warning">有效: {% raw %}{{ stats.valid_count }}{% endraw %}</el-tag>
                    <el-tag type="danger">过期: {% raw %}{{ stats.expired_count }}{% endraw %}</el-tag>
                    <el-button @click="loadStats" :loading="statsLoading" size="small" text>
                        <el-icon><Refresh /></el-icon>
                    </el-button>
                </div>
            </el-col>
            <el-col :span="4" style="text-align: right;">
                <el-button type="primary" @click="openGenerateDialog">
                    <el-icon><Plus /></el-icon>
                    生成激活码
                </el-button>
            </el-col>
        </el-row>
    </div>

    <!-- 记录表格 -->
    <el-table
        :data="records"
        v-loading="recordsLoading"
        style="width: 100%"
        :default-sort="{prop: 'created_at', order: 'descending'}">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="machine_id" label="机器码" min-width="120"></el-table-column>
        <el-table-column prop="activation_code" label="激活码" min-width="200" show-overflow-tooltip>
            <template #default="scope">
                <div class="activation-code-cell">
                    <span>{% raw %}{{ scope.row.activation_code }}{% endraw %}</span>
                    <el-button
                        @click="copyText(scope.row.activation_code)"
                        :icon="DocumentCopy"
                        size="small"
                        text
                        class="copy-btn">
                    </el-button>
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="days" label="有效期" width="80">
            <template #default="scope">
                {% raw %}{{ scope.row.days }}{% endraw %}天
            </template>
        </el-table-column>
        <el-table-column prop="expire_timestamp" label="过期时间" width="160">
            <template #default="scope">
                {% raw %}{{ formatTimestamp(scope.row.expire_timestamp) }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="80">
            <template #default="scope">
                <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                    {% raw %}{{ scope.row.is_active ? '活跃' : '禁用' }}{% endraw %}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="scope">
                {% raw %}{{ formatDateTime(scope.row.created_at) }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
            <template #default="scope">
                <el-button
                    @click="toggleRecordStatus(scope.row)"
                    :type="scope.row.is_active ? 'warning' : 'success'"
                    size="small">
                    {% raw %}{{ scope.row.is_active ? '禁用' : '启用' }}{% endraw %}
                </el-button>
                <el-button
                    @click="deleteRecord(scope.row)"
                    type="danger"
                    size="small">
                    删除
                </el-button>
            </template>
        </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div style="margin-top: 20px; text-align: right;">
        <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
        </el-pagination>
    </div>
</div>

<!-- 生成激活码对话框 -->
<el-dialog
    title="生成机器码激活码"
    v-model="dialogVisible"
    width="600px">
    <el-form :model="generateForm" :rules="generateRules" ref="generateFormRef" label-width="80px">
        <el-form-item label="机器码" prop="machine_id">
            <el-input
                v-model="generateForm.machine_id"
                placeholder="请输入机器码"
                clearable
                maxlength="100">
            </el-input>
        </el-form-item>

        <!-- 算法选择功能暂时禁用 -->
        <!-- <el-form-item label="生成算法">
            <el-select
                v-model="generateForm.algorithm_id"
                placeholder="请选择算法（留空使用默认算法）"
                clearable
                style="width: 100%">
                <el-option
                    v-for="algorithm in availableAlgorithms"
                    :key="algorithm.id"
                    :label="`${algorithm.name} v${algorithm.version}${algorithm.is_default ? ' (默认)' : ''}`"
                    :value="algorithm.id">
                    <div class="algorithm-option">
                        <span>{% raw %}{{ algorithm.name }}{% endraw %} v{% raw %}{{ algorithm.version }}{% endraw %}</span>
                        <el-tag v-if="algorithm.is_default" type="success" size="small">默认</el-tag>
                    </div>
                </el-option>
            </el-select>
        </el-form-item> -->

        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="有效期" prop="days">
                    <el-input-number
                        v-model="generateForm.days"
                        :min="1"
                        :max="3650"
                        controls-position="right"
                        style="width: 100%">
                    </el-input-number>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="快捷设置">
                    <el-button-group class="quick-days">
                        <el-button @click="setDays(7)" size="small" plain>7天</el-button>
                        <el-button @click="setDays(30)" size="small" plain>30天</el-button>
                        <el-button @click="setDays(90)" size="small" plain>90天</el-button>
                        <el-button @click="setDays(365)" size="small" plain>1年</el-button>
                    </el-button-group>
                </el-form-item>
            </el-col>
        </el-row>

        <el-form-item label="备注">
            <el-input
                v-model="generateForm.notes"
                type="textarea"
                :rows="3"
                placeholder="可选，添加备注信息"
                maxlength="200"
                show-word-limit>
            </el-input>
        </el-form-item>
    </el-form>

    <!-- 生成结果显示 -->
    <div v-if="generatedCode" class="result-section">
        <el-divider content-position="left">
            <el-icon><SuccessFilled /></el-icon>
            生成成功
        </el-divider>

        <div class="result-content">
            <el-form label-width="80px">
                <el-form-item label="激活码">
                    <el-input
                        v-model="generatedCode.activation_code"
                        readonly
                        class="activation-code-input">
                        <template #append>
                            <el-button @click="copyActivationCode" :icon="DocumentCopy" type="primary">
                                复制
                            </el-button>
                        </template>
                    </el-input>
                </el-form-item>

                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="机器码">
                            <el-input v-model="generatedCode.machine_id" readonly></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="有效期">
                            <el-input :value="generatedCode.days + ' 天'" readonly></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="过期时间">
                            <el-input :value="formatTimestamp(generatedCode.expire_timestamp)" readonly></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item v-if="generatedCode.notes" label="备注">
                    <el-input v-model="generatedCode.notes" type="textarea" readonly :rows="2"></el-input>
                </el-form-item>
            </el-form>
        </div>
    </div>

    <template #footer>
        <span class="dialog-footer">
            <el-button @click="closeDialog">取消</el-button>
            <el-button
                type="primary"
                @click="generateActivationCode"
                :loading="generating">
                <el-icon><Plus /></el-icon>
                生成激活码
            </el-button>
        </span>
    </template>
</el-dialog>
{% endblock %}

{% block data %}
// 对话框控制
dialogVisible: false,

// 生成表单数据
generateForm: {
    machine_id: '',
    days: 30,
    notes: '',
    algorithm_id: null
},
generateRules: {
    machine_id: [
        { required: true, message: '请输入机器码', trigger: 'blur' },
        { min: 1, max: 100, message: '机器码长度应在1-100个字符之间', trigger: 'blur' }
    ],
    days: [
        { required: true, message: '请输入有效期', trigger: 'blur' },
        { type: 'number', min: 1, max: 3650, message: '有效期应在1-3650天之间', trigger: 'blur' }
    ]
},
generating: false,
generatedCode: null,

// 可用算法列表（暂时禁用）
// availableAlgorithms: [],

// 搜索表单
searchForm: {
    machine_id: ''
},

// 统计数据
stats: {
    total_count: 0,
    active_count: 0,
    valid_count: 0,
    expired_count: 0
},
statsLoading: false,

// 记录列表
records: [],
recordsLoading: false,

// 分页
pagination: {
    currentPage: 1,
    pageSize: 20,
    total: 0
}
{% endblock %}

{% block methods %}
// 打开生成对话框
openGenerateDialog() {
    this.dialogVisible = true;
    this.generatedCode = null;
    this.resetGenerateForm();
},

// 关闭对话框
closeDialog() {
    this.dialogVisible = false;
    this.generatedCode = null;
    this.resetGenerateForm();
},

// 设置有效期天数
setDays(days) {
    this.generateForm.days = days;
},

// 生成激活码
async generateActivationCode() {
    try {
        const valid = await this.$refs.generateFormRef.validate();
        if (!valid) return;

        this.generating = true;

        const response = await fetch('/api/machine-activation/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.generateForm)
        });

        if (response.ok) {
            const data = await response.json();
            this.generatedCode = data;
            ElMessage.success('激活码生成成功！');

            // 刷新统计和记录
            this.loadStats();
            this.loadRecords();
        } else {
            const error = await response.json().catch(() => ({ detail: '未知错误' }));
            ElMessage.error(`生成失败: ${error.detail || '服务器错误'}`);
        }
    } catch (error) {
        console.error('Generate activation code error:', error);
        ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
    } finally {
        this.generating = false;
    }
},

// 重置生成表单
resetGenerateForm() {
    this.generateForm = {
        machine_id: '',
        days: 30,
        notes: '',
        algorithm_id: null
    };
    if (this.$refs.generateFormRef) {
        this.$refs.generateFormRef.clearValidate();
    }
},

// 加载可用算法列表（暂时禁用）
// async loadAvailableAlgorithms() {
//     try {
//         const response = await fetch('/api/activation-algorithm/active');
//         if (response.ok) {
//             this.availableAlgorithms = await response.json();
//         } else {
//             console.error('Failed to load algorithms');
//         }
//     } catch (error) {
//         console.error('Load algorithms error:', error);
//     }
// },

// 复制激活码
copyActivationCode() {
    if (this.generatedCode && this.generatedCode.activation_code) {
        this.copyText(this.generatedCode.activation_code);
    }
},

// 复制文本到剪贴板
async copyText(text) {
    try {
        await navigator.clipboard.writeText(text);
        ElMessage.success('复制成功！');
    } catch (error) {
        console.error('Copy failed:', error);
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            ElMessage.success('复制成功！');
        } catch (err) {
            ElMessage.error('复制失败，请手动复制');
        }
        document.body.removeChild(textArea);
    }
},

// 加载统计信息
async loadStats() {
    try {
        this.statsLoading = true;

        const response = await fetch('/api/machine-activation/stats');
        if (response.ok) {
            const data = await response.json();
            this.stats = data;
        } else {
            ElMessage.error('加载统计信息失败');
        }
    } catch (error) {
        console.error('Load stats error:', error);
        ElMessage.error('网络连接失败');
    } finally {
        this.statsLoading = false;
    }
},

// 加载记录列表
async loadRecords() {
    try {
        this.recordsLoading = true;

        const params = new URLSearchParams({
            skip: (this.pagination.currentPage - 1) * this.pagination.pageSize,
            limit: this.pagination.pageSize
        });

        if (this.searchForm.machine_id) {
            params.append('machine_id', this.searchForm.machine_id);
        }

        const response = await fetch(`/api/machine-activation/?${params}`);
        if (response.ok) {
            const data = await response.json();
            this.records = data.items;
            this.pagination.total = data.total;
        } else {
            ElMessage.error('加载记录失败');
        }
    } catch (error) {
        console.error('Load records error:', error);
        ElMessage.error('网络连接失败');
    } finally {
        this.recordsLoading = false;
    }
},

// 搜索记录
searchRecords() {
    this.pagination.currentPage = 1;
    this.loadRecords();
},

// 重置搜索
resetSearch() {
    this.searchForm.machine_id = '';
    this.pagination.currentPage = 1;
    this.loadRecords();
},

// 切换记录状态
async toggleRecordStatus(record) {
    try {
        const newStatus = !record.is_active;
        const action = newStatus ? '启用' : '禁用';

        await ElMessageBox.confirm(
            `确定要${action}这条记录吗？`,
            '确认操作',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        const response = await fetch(`/api/machine-activation/${record.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: newStatus
            })
        });

        if (response.ok) {
            record.is_active = newStatus;
            ElMessage.success(`${action}成功！`);
            this.loadStats();
        } else {
            const error = await response.json().catch(() => ({ detail: '未知错误' }));
            ElMessage.error(`${action}失败: ${error.detail || '服务器错误'}`);
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('Toggle record status error:', error);
            ElMessage.error('操作失败');
        }
    }
},

// 删除记录
async deleteRecord(record) {
    try {
        await ElMessageBox.confirm(
            '确定要删除这条记录吗？删除后无法恢复。',
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        const response = await fetch(`/api/machine-activation/${record.id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            ElMessage.success('删除成功！');
            this.loadRecords();
            this.loadStats();
        } else {
            const error = await response.json().catch(() => ({ detail: '未知错误' }));
            ElMessage.error(`删除失败: ${error.detail || '服务器错误'}`);
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('Delete record error:', error);
            ElMessage.error('删除失败');
        }
    }
},

// 分页大小改变
handleSizeChange(size) {
    this.pagination.pageSize = size;
    this.pagination.currentPage = 1;
    this.loadRecords();
},

// 当前页改变
handleCurrentChange(page) {
    this.pagination.currentPage = page;
    this.loadRecords();
},

// 格式化时间戳
formatTimestamp(timestamp) {
    if (!timestamp) return '-';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('zh-CN');
},

// 格式化日期时间
formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}
{% endblock %}

{% block mounted %}
// 页面加载时初始化数据
this.loadStats();
this.loadRecords();
// this.loadAvailableAlgorithms();
{% endblock %}

{% block style %}
<style scoped>
/* 统计信息样式 */
.stats-summary {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

/* 算法选择样式 */
.algorithm-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

/* 对话框内的快捷设置按钮 */
.quick-days {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

/* 对话框内的生成结果样式 */
.result-section {
    margin-top: 20px;
    padding: 16px;
    background: #f0f9ff;
    border-radius: 8px;
    border: 1px solid #bfdbfe;
}

.result-content {
    margin-bottom: 0;
}

.activation-code-input {
    font-family: 'Courier New', 'Monaco', monospace;
    font-size: 14px;
}

/* 表格样式 */
.activation-code-cell {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 200px;
}

.activation-code-cell span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
}

.copy-btn {
    opacity: 0.6;
    transition: opacity 0.3s;
    flex-shrink: 0;
}

.copy-btn:hover {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .stats-summary {
        width: 100%;
        justify-content: flex-start;
    }

    .quick-days {
        justify-content: flex-start;
    }

    .result-details .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .activation-code-cell {
        max-width: none;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .activation-code-cell span {
        margin-right: 0;
        word-break: break-all;
    }
}

@media (max-width: 480px) {
    .quick-days .el-button {
        flex: 1;
        min-width: 0;
    }

    .stats-summary {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
</style>
{% endblock %}
