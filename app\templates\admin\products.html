{% extends "admin/base.html" %}

{% block title %}产品管理 - FocuSee 管理系统{% endblock %}
{% block page_title %}产品管理{% endblock %}

{% block data %}
    products: [
        {% for product in products %}
        {
            id: {{ product.id }},
            name: "{{ product.name }}",
            code: "{{ product.code or '' }}",
            description: "{{ product.description or '' }}",
            version: "{{ product.version or '' }}",
            price: {{ product.price or 'null' }},
            is_active: {{ 'true' if product.is_active else 'false' }},
            created_at: "{{ product.created_at.isoformat() if product.created_at else '' }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    dialogVisible: false,
    editMode: false,
    currentProduct: {
        id: null,
        name: '',
        code: '',
        description: '',
        version: '',
        price: null,
        is_active: true
    },
    productForm: {
        name: '',
        code: '',
        description: '',
        version: '',
        price: null,
        is_active: true
    },
    rules: {
        name: [
            { required: true, message: '请输入产品名称', trigger: 'blur' }
        ],
        code: [
            { required: true, message: '请输入产品代码', trigger: 'blur' }
        ],
        version: [
            { required: true, message: '请输入产品版本', trigger: 'blur' }
        ]
    },
    loading: false,
    uploadUrl: '/api/products/upload',
    uploadHeaders: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
    },
    fileList: []
{% endblock %}

{% block content %}
<div class="content-card">
    <!-- 操作栏 -->
    <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="openAddDialog">
            <el-icon><Plus /></el-icon>
            添加产品
        </el-button>
        <el-button @click="refreshProducts">
            <el-icon><Refresh /></el-icon>
            刷新
        </el-button>
    </div>

    <!-- 产品表格 -->
    <el-table :data="products" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="产品名称" min-width="150"></el-table-column>
        <el-table-column prop="code" label="产品代码" width="120"></el-table-column>
        <el-table-column prop="version" label="版本" width="120"></el-table-column>
        <el-table-column prop="price" label="价格" width="120">
            <template #default="scope">
                <span v-if="scope.row.price !== null">¥{% raw %}{{ scope.row.price }}{% endraw %}</span>
                <span v-else>-</span>
            </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
            <template #default="scope">
                <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                    {% raw %}{{ scope.row.is_active ? '启用' : '禁用' }}{% endraw %}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="scope">
                {% raw %}{{ formatTime(scope.row.created_at) }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
            <template #default="scope">
                <el-button size="small" @click="editProduct(scope.row)">编辑</el-button>
                <el-button 
                    size="small" 
                    :type="scope.row.is_active ? 'danger' : 'success'"
                    @click="toggleStatus(scope.row)">
                    {% raw %}{{ scope.row.is_active ? '禁用' : '启用' }}{% endraw %}
                </el-button>
                <el-button size="small" type="primary" @click="manageDownloads(scope.row)">
                    管理下载
                </el-button>
            </template>
        </el-table-column>
    </el-table>
</div>

<!-- 添加/编辑产品对话框 -->
<el-dialog
    :title="editMode ? '编辑产品' : '添加产品'"
    v-model="dialogVisible"
    width="700px">
    <el-form ref="productFormRef" :model="productForm" :rules="rules" label-width="120px">
        <el-form-item label="产品名称" prop="name">
            <el-input v-model="productForm.name" placeholder="请输入产品名称"></el-input>
        </el-form-item>

        <el-form-item label="产品代码" prop="code">
            <el-input v-model="productForm.code" placeholder="请输入产品代码（唯一标识）"></el-input>
        </el-form-item>

        <el-form-item label="产品版本" prop="version">
            <el-input v-model="productForm.version" placeholder="请输入产品版本"></el-input>
        </el-form-item>
        
        <el-form-item label="产品价格">
            <el-input-number v-model="productForm.price" :precision="2" :min="0" style="width: 100%"></el-input-number>
        </el-form-item>
        
        <el-form-item label="产品描述">
            <el-input type="textarea" v-model="productForm.description" :rows="3" placeholder="请输入产品描述"></el-input>
        </el-form-item>
        
        <el-form-item label="状态">
            <el-switch v-model="productForm.is_active" active-text="启用" inactive-text="禁用"></el-switch>
        </el-form-item>
    </el-form>

    <template #footer>
        <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveProduct">确定</el-button>
        </span>
    </template>
</el-dialog>
{% endblock %}

{% block methods %}
formatTime(timeStr) {
    if (!timeStr) return '-';
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
},
openAddDialog() {
    this.dialogVisible = true;
    this.editMode = false;
    this.productForm = {
        name: '',
        code: '',
        description: '',
        version: '',
        price: null,
        is_active: true
    };
    if (this.$refs.productFormRef) {
        this.$refs.productFormRef.resetFields();
    }
},
editProduct(product) {
    this.dialogVisible = true;
    this.editMode = true;
    this.currentProduct = { ...product };
    this.productForm = {
        name: product.name,
        code: product.code || '',
        description: product.description || '',
        version: product.version || '',
        price: product.price,
        is_active: product.is_active
    };
},
saveProduct() {
    if (this.$refs.productFormRef) {
        this.$refs.productFormRef.validate(async (valid) => {
            if (valid) {
                this.loading = true;
                try {
                    const url = this.editMode ? `/api/products/${this.currentProduct.id}` : '/api/products';
                    const method = this.editMode ? 'PUT' : 'POST';
                    
                    const response = await fetch(url, {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(this.productForm)
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.detail || '操作失败');
                    }
                    
                    ElMessage.success(this.editMode ? '产品更新成功' : '产品添加成功');
                    this.dialogVisible = false;
                    this.refreshProducts();
                } catch (error) {
                    ElMessage.error(error.message || '操作失败');
                } finally {
                    this.loading = false;
                }
            }
        });
    }
},
toggleStatus(product) {
    ElMessageBox.confirm(
        `确定要${product.is_active ? '禁用' : '启用'}该产品吗？`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(async () => {
        try {
            const response = await fetch(`/api/products/${product.id}/toggle-status`, {
                method: 'POST'
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || '操作失败');
            }
            
            ElMessage.success(`产品已${product.is_active ? '禁用' : '启用'}`);
            this.refreshProducts();
        } catch (error) {
            ElMessage.error(error.message || '操作失败');
        }
    }).catch(() => {});
},
manageDownloads(product) {
    window.location.href = `/admin/products/${product.id}/downloads`;
},
refreshProducts() {
    this.loading = true;
    fetch('/api/products')
        .then(response => response.json())
        .then(data => {
            this.products = data.items || data;
            this.loading = false;
        })
        .catch(error => {
            console.error('Error loading products:', error);
            ElMessage.error('加载产品列表失败');
            this.loading = false;
        });
}
{% endblock %}

{% block mounted %}
this.refreshProducts();
{% endblock %} 