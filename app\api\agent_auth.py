from fastapi import API<PERSON>outer, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.database import get_db
from app.schemas.agent import AgentLogin
from app.services.agent_service import AgentService
from app.services.admin_service import AdminService
from app.models.agent import Agent
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()

async def get_current_agent_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Agent:
    """获取当前代理商用户"""
    try:
        # 验证token
        payload = AdminService.verify_access_token(credentials.credentials)
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        username = payload.get("sub")
        if not username or not username.startswith("agent:"):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 获取代理商用户名
        agent_username = username.replace("agent:", "")
        agent = db.query(Agent).filter(Agent.username == agent_username).first()
        if not agent or not agent.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Agent not found or inactive"
            )

        return agent

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_current_agent_user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

@router.post("/login")
async def agent_login(
    login_data: AgentLogin,
    db: Session = Depends(get_db)
):
    """代理商登录"""
    try:
        # 验证代理商凭据
        agent = AgentService.authenticate_agent(db, login_data.username, login_data.password)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password"
            )
        
        # 生成JWT token
        from app.config import settings
        from datetime import timedelta

        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        token = AdminService.create_access_token(
            data={"sub": f"agent:{agent.username}", "agent_id": agent.id},
            secret_key=settings.secret_key,
            algorithm=settings.algorithm,
            expires_delta=access_token_expires
        )
        
        logger.info(f"Agent {agent.username} logged in successfully")
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "agent_info": {
                "id": agent.id,
                "username": agent.username,
                "company_name": agent.company_name,
                "contact_name": agent.contact_name,
                "contact_email": agent.contact_email
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during agent login: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/logout")
async def agent_logout():
    """代理商登出"""
    # 由于使用JWT，登出主要在客户端处理（删除token）
    return {"message": "Logged out successfully"}

@router.get("/me")
async def get_current_agent(
    agent: Agent = Depends(get_current_agent_user)
):
    """获取当前代理商信息"""
    return {
        "id": agent.id,
        "username": agent.username,
        "company_name": agent.company_name,
        "contact_name": agent.contact_name,
        "contact_email": agent.contact_email,
        "contact_phone": agent.contact_phone,
        "address": agent.address,
        "description": agent.description,
        "is_active": agent.is_active,
        "created_at": agent.created_at
    }
