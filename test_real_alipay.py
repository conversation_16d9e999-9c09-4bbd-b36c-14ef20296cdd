#!/usr/bin/env python3
"""
测试真实支付宝业务（无模拟）
"""

import requests
import json

def test_real_alipay_service():
    """测试真实支付宝服务"""
    print("🔍 测试真实支付宝服务（无模拟）")
    print("=" * 60)
    
    try:
        from app.services.alipay_service import AlipayService
        
        service = AlipayService()
        print("✅ 支付宝服务初始化成功")
        
        print(f"配置信息:")
        print(f"  APP_ID: {service.app_id}")
        print(f"  网关地址: {service.gateway_url}")
        print(f"  回调地址: {service.notify_url}")
        print(f"  私钥: {'已配置' if service.app_private_key else '未配置'}")
        print(f"  公钥: {'已配置' if service.alipay_public_key else '未配置'}")
        
        # 测试创建支付订单
        print(f"\n创建真实支付订单...")
        result = service.create_face_to_face_payment(
            out_trade_no="REAL_TEST_001",
            total_amount=0.01,
            subject="真实测试订单",
            body="测试真实支付宝业务"
        )
        
        print(f"创建结果: {result.get('success')}")
        if result.get('success'):
            qr_code = result.get('qr_code')
            print(f"二维码: {qr_code}")
            
            # 检查是否是真实的支付宝二维码
            if qr_code and qr_code.startswith('https://qr.alipay.com/'):
                print("✅ 获得真实支付宝二维码")
                return True
            else:
                print("❌ 未获得真实支付宝二维码")
                return False
        else:
            print(f"❌ 创建失败: {result.get('error')}")
            return False
            
    except ImportError as e:
        print(f"❌ 支付宝SDK导入失败: {str(e)}")
        print("请安装支付宝SDK: pip install alipay-sdk-python")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_real_payment_api():
    """测试真实支付API"""
    print(f"\n🔌 测试真实支付API...")
    
    try:
        # 创建支付订单
        response = requests.post(
            "http://localhost:8008/api/payment/face-to-face",
            json={
                "user_id": "real_test_user",
                "product_id": 1,
                "timeout_minutes": 30
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                qr_code = data.get("qr_code")
                order_no = data.get("order_no")
                
                print(f"✅ API调用成功")
                print(f"   订单号: {order_no}")
                print(f"   二维码: {qr_code}")
                
                # 检查是否是真实的支付宝二维码
                if qr_code and qr_code.startswith('https://qr.alipay.com/'):
                    print("✅ API返回真实支付宝二维码")
                    return True
                else:
                    print("❌ API未返回真实支付宝二维码")
                    return False
            else:
                print(f"❌ API调用失败: {data.get('error')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_no_mock_fallback():
    """测试没有模拟回退"""
    print(f"\n🚫 测试无模拟回退...")
    
    try:
        from app.services.alipay_service import AlipayService
        
        # 检查是否还有模拟相关的方法
        service = AlipayService()
        
        mock_methods = []
        for attr_name in dir(service):
            if 'mock' in attr_name.lower():
                mock_methods.append(attr_name)
        
        if mock_methods:
            print(f"❌ 仍然存在模拟方法: {mock_methods}")
            return False
        else:
            print("✅ 已删除所有模拟方法")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 真实支付宝业务测试")
    print("此测试验证所有模拟代码已删除，只使用真实支付宝业务")
    
    # 测试结果
    results = []
    
    # 1. 测试真实支付宝服务
    results.append(("真实支付宝服务", test_real_alipay_service()))
    
    # 2. 测试真实支付API
    results.append(("真实支付API", test_real_payment_api()))
    
    # 3. 测试无模拟回退
    results.append(("无模拟回退", test_no_mock_fallback()))
    
    # 显示测试结果
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("-" * 40)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} - {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    # 给出建议
    print(f"\n💡 测试总结:")
    if success_count == len(results):
        print("🎉 真实支付宝业务正常！")
        print("\n✨ 确认事项:")
        print("- ✅ 所有模拟代码已删除")
        print("- ✅ 只使用真实支付宝SDK")
        print("- ✅ 获得真实支付宝二维码")
        print("- ✅ 支付宝配置正确")
        
        print(f"\n🎯 现在系统:")
        print("1. 只支持真实支付宝业务")
        print("2. 需要正确的支付宝配置")
        print("3. 生成真实的支付二维码")
        print("4. 接收真实的支付回调")
        
    else:
        print("⚠️  部分测试失败，可能原因:")
        print("- 支付宝SDK未正确安装")
        print("- 支付宝配置不正确")
        print("- 网络连接问题")
        print("- 服务器未运行")
    
    print(f"\n📋 注意事项:")
    print("- 系统现在只支持真实支付宝业务")
    print("- 需要正确配置支付宝APP_ID、密钥等")
    print("- 测试环境请使用支付宝沙箱")
    print("- 生产环境需要真实的支付宝商户配置")
    
    return success_count == len(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
