#!/usr/bin/env python3
"""
调试支付宝API参数
"""

def test_alipay_service():
    """测试支付宝服务参数"""
    print("🔍 调试支付宝API参数")
    print("=" * 60)
    
    try:
        from app.services.alipay_service import AlipayService
        
        service = AlipayService()
        
        print(f"配置信息:")
        print(f"  APP_ID: {service.app_id}")
        print(f"  网关地址: {service.gateway_url}")
        print(f"  回调地址: {service.notify_url}")
        print(f"  跳转地址: {service.return_url}")
        print(f"  私钥: {'已配置' if service.app_private_key else '未配置'}")
        print(f"  公钥: {'已配置' if service.alipay_public_key else '未配置'}")
        
        # 测试创建支付订单
        print(f"\n创建测试订单...")
        result = service.create_face_to_face_payment(
            out_trade_no="DEBUG_TEST_001",
            total_amount=0.01,
            subject="调试测试",
            body="调试支付宝参数传递"
        )
        
        print(f"创建结果: {result.get('success')}")
        if result.get('success'):
            print(f"二维码: {result.get('qr_code')}")
            
            # 检查是否是真实的支付宝二维码
            qr_code = result.get('qr_code', '')
            if qr_code.startswith('https://qr.alipay.com/'):
                print("✅ 获得真实支付宝二维码，说明API调用成功")
                print("📋 请检查服务器日志中的参数设置信息")
            else:
                print("⚠️  使用模拟二维码")
        else:
            print(f"创建失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def check_sdk_methods():
    """检查SDK方法"""
    print(f"\n🔍 检查SDK方法...")
    
    try:
        from alipay.aop.api.request.AlipayTradePrecreateRequest import AlipayTradePrecreateRequest
        from alipay.aop.api.domain.AlipayTradePrecreateModel import AlipayTradePrecreateModel
        
        # 检查请求对象的方法
        request = AlipayTradePrecreateRequest()
        model = AlipayTradePrecreateModel()
        
        print("请求对象方法:")
        request_methods = [method for method in dir(request) if not method.startswith('_')]
        for method in request_methods:
            if 'notify' in method.lower() or 'return' in method.lower():
                print(f"  - {method}")
        
        print("\n模型对象属性:")
        model_attrs = [attr for attr in dir(model) if not attr.startswith('_')]
        for attr in model_attrs:
            if 'notify' in attr.lower() or 'return' in attr.lower():
                print(f"  - {attr}")
                
        # 测试设置notify_url
        print(f"\n测试设置notify_url...")
        test_url = "http://c57d4f98.natappfree.cc/api/payment/alipay/notify"
        
        if hasattr(model, 'notify_url'):
            model.notify_url = test_url
            print(f"✅ 模型支持notify_url属性")
        else:
            print(f"❌ 模型不支持notify_url属性")
            
        if hasattr(request, 'set_notify_url'):
            request.set_notify_url(test_url)
            print(f"✅ 请求支持set_notify_url方法")
        elif hasattr(request, 'notify_url'):
            request.notify_url = test_url
            print(f"✅ 请求支持notify_url属性")
        else:
            print(f"❌ 请求不支持notify_url设置")
            
    except ImportError as e:
        print(f"❌ SDK导入失败: {str(e)}")
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 支付宝参数调试")
    
    # 测试服务配置
    test_alipay_service()
    
    # 检查SDK方法
    check_sdk_methods()
    
    print(f"\n" + "=" * 60)
    print("📋 调试建议:")
    print("1. 检查服务器日志中的参数设置信息")
    print("2. 确认支付宝开放平台的应用网关地址配置")
    print("3. 验证内网穿透地址是否可访问")
    print("4. 检查支付宝SDK版本和文档")
    
    print(f"\n🔧 可能的解决方案:")
    print("1. 在支付宝开放平台重新保存应用网关地址")
    print("2. 检查沙箱环境的回调设置")
    print("3. 尝试使用不同的SDK版本")
    print("4. 查看支付宝开放平台的回调日志")

if __name__ == "__main__":
    main()
