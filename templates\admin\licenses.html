{% extends "admin/base.html" %}

{% block title %}授权码管理 - FocuSee 管理系统{% endblock %}
{% block page_title %}授权码管理{% endblock %}

{% block data %}
    licenses: [
        {% for license in licenses %}
        {
            id: {{ license.id }},
            license_code: "{{ license.license_code }}",
            product_id: {{ license.product_id }},
            product_name: "{{ license.product.name if license.product else '' }}",
            user_id: "{{ license.user_id or '' }}",
            order_id: {{ license.order_id or 'null' }},
            agent_id: {{ license.agent_id or 'null' }},
            agent_name: "{{ license.agent.company_name if license.agent else '' }}",
            max_api_calls: {{ license.max_api_calls }},
            used_api_calls: {{ license.used_api_calls }},
            status: "{{ license.status.value if license.status else 'inactive' }}",
            expire_date: "{{ license.expire_date.isoformat() if license.expire_date else '' }}",
            notes: "{{ license.notes or '' }}",
            device_info: "{{ license.device_info or '' }}",
            activated_at: "{{ license.activated_at.isoformat() if license.activated_at else '' }}",
            created_at: "{{ license.created_at.isoformat() if license.created_at else '' }}",
            updated_at: "{{ license.updated_at.isoformat() if license.updated_at else '' }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    searchForm: {
        keyword: '',
        status: '',
        product_id: null,
        agent_id: null
    },
    currentPage: 1,
    pageSize: 20,
    total: 0,
    allLicenses: [], // 保存所有授权码数据
    sortField: 'created_at', // 默认按创建时间排序
    sortOrder: 'desc', // 默认降序
    dialogVisible: false,
    editMode: false,
    currentLicense: {},
    licenseForm: {
        license_code: '',
        product_id: null,
        agent_id: null,
        user_id: '',
        max_api_calls: 1000,
        expire_date: '',
        status: 'inactive',
        notes: ''
    },
    products: [
        {% for product in products %}
        {
            id: {{ product.id }},
            name: "{{ product.name }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    agents: [
        {% for agent in agents %}
        {
            id: {{ agent.id }},
            company_name: "{{ agent.company_name }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    rules: {
        product_id: [
            { required: true, message: '请选择产品', trigger: 'change' }
        ],
        max_api_calls: [
            { required: true, message: '请输入最大API调用次数', trigger: 'blur' },
            { type: 'number', min: -1, message: '最大API调用次数不能小于-1', trigger: 'blur' }
        ]
    },
    loading: false
{% endblock %}

{% block content %}
<div class="content-card">
    <!-- 查询栏 -->
    <div style="margin-bottom: 20px;">
        <el-row :gutter="20">
            <el-col :span="6">
                <el-input
                    v-model="searchForm.keyword"
                    placeholder="搜索授权码、用户ID"
                    clearable
                    @keyup.enter="searchLicenses">
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
            </el-col>
            <el-col :span="4">
                <el-select v-model="searchForm.status" placeholder="授权状态" clearable>
                    <el-option label="未激活" value="inactive"></el-option>
                    <el-option label="激活" value="active"></el-option>
                    <el-option label="过期" value="expired"></el-option>
                    <el-option label="暂停" value="suspended"></el-option>
                    <el-option label="撤销" value="revoked"></el-option>
                </el-select>
            </el-col>
            <el-col :span="4">
                <el-select v-model="searchForm.product_id" placeholder="产品" clearable>
                    <el-option
                        v-for="product in products"
                        :key="product.id"
                        :label="product.name"
                        :value="product.id">
                    </el-option>
                </el-select>
            </el-col>
            <el-col :span="4">
                <el-select v-model="searchForm.agent_id" placeholder="代理商" clearable>
                    <el-option label="管理员创建" :value="null"></el-option>
                    <el-option
                        v-for="agent in agents"
                        :key="agent.id"
                        :label="agent.company_name"
                        :value="agent.id">
                    </el-option>
                </el-select>
            </el-col>
            <el-col :span="6">
                <el-button type="primary" @click="searchLicenses">
                    <el-icon><Search /></el-icon>
                    查询
                </el-button>
                <el-button @click="resetSearch">
                    <el-icon><Refresh /></el-icon>
                    重置
                </el-button>
                <el-button type="primary" @click="openAddDialog">
                    <el-icon><Plus /></el-icon>
                    添加授权码
                </el-button>
            </el-col>
        </el-row>
    </div>

    <!-- 授权码表格 -->
    <el-table
        :data="licenses"
        style="width: 100%"
        v-loading="loading"
        @sort-change="handleSortChange"
        :default-sort="{prop: 'created_at', order: 'descending'}">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="license_code" label="授权码" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="product_name" label="产品" width="120"></el-table-column>
        <el-table-column prop="user_id" label="用户ID" width="120"></el-table-column>
        <el-table-column prop="agent_name" label="代理商" width="120">
            <template #default="scope">
                {% raw %}{{ scope.row.agent_name || '管理员直接创建' }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column label="API调用" width="120">
            <template #default="scope">
                {% raw %}{{ scope.row.used_api_calls }}/{{ scope.row.max_api_calls === -1 ? '无限' : scope.row.max_api_calls }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                    {% raw %}{{ getStatusText(scope.row.status) }}{% endraw %}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="expire_date" label="过期时间" width="180" sortable="custom" sort-by="expire_date">
            <template #default="scope">
                {% raw %}{{ formatTime(scope.row.expire_date) }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom" sort-by="created_at">
            <template #default="scope">
                {% raw %}{{ formatTime(scope.row.created_at) }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
            <template #default="scope">
                <el-button size="small" @click="editLicense(scope.row)">编辑</el-button>
                <el-button
                    size="small"
                    type="warning"
                    @click="suspendLicense(scope.row)"
                    v-if="scope.row.status === 'active'">
                    暂停
                </el-button>
                <el-button
                    size="small"
                    type="success"
                    @click="resumeLicense(scope.row)"
                    v-if="scope.row.status === 'suspended'">
                    恢复
                </el-button>
                <el-button size="small" type="danger" @click="deleteLicense(scope.row)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div style="margin-top: 20px; text-align: right;">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
        </el-pagination>
    </div>
</div>

<!-- 添加/编辑授权码对话框 -->
<el-dialog
    :title="editMode ? '编辑授权码' : '添加授权码'"
    v-model="dialogVisible"
    width="500px">
    <el-form ref="licenseFormRef" :model="licenseForm" :rules="rules" label-width="120px">
        <el-form-item label="授权码" prop="license_code">
            <el-input v-model="licenseForm.license_code" placeholder="留空自动生成" :disabled="editMode"></el-input>
        </el-form-item>
        <el-form-item label="产品" prop="product_id">
            <el-select v-model="licenseForm.product_id" placeholder="请选择产品" style="width: 100%">
                <el-option
                    v-for="product in products"
                    :key="product.id"
                    :label="product.name"
                    :value="product.id">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="代理商">
            <el-select v-model="licenseForm.agent_id" placeholder="选择代理商（可选）" style="width: 100%" clearable>
                <el-option
                    v-for="agent in agents"
                    :key="agent.id"
                    :label="agent.company_name"
                    :value="agent.id">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="用户ID">
            <el-input v-model="licenseForm.user_id" placeholder="可选，绑定特定用户"></el-input>
        </el-form-item>
        <el-form-item label="最大API调用">
            <el-input-number v-model="licenseForm.max_api_calls" :min="-1" placeholder="-1表示无限制" style="width: 100%"></el-input-number>
        </el-form-item>
        <el-form-item label="过期时间">
            <el-date-picker
                v-model="licenseForm.expire_date"
                type="datetime"
                placeholder="选择过期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="状态">
            <el-select v-model="licenseForm.status" style="width: 100%">
                <el-option label="未激活" value="inactive"></el-option>
                <el-option label="激活" value="active"></el-option>
                <el-option label="暂停" value="suspended"></el-option>
                <el-option label="过期" value="expired"></el-option>
                <el-option label="撤销" value="revoked"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="备注">
            <el-input type="textarea" v-model="licenseForm.notes" placeholder="请输入备注" :rows="2"></el-input>
        </el-form-item>
    </el-form>

    <template #footer>
        <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveLicense">确定</el-button>
        </span>
    </template>
</el-dialog>
{% endblock %}

{% block methods %}
formatTime(timeStr) {
    if (!timeStr) return '-';
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
},
getStatusText(status) {
    const statusMap = {
        'active': '激活',
        'inactive': '未激活',
        'expired': '已过期',
        'suspended': '已暂停',
        'revoked': '已撤销'
    };
    return statusMap[status] || status;
},
getStatusType(status) {
    const typeMap = {
        'active': 'success',
        'inactive': 'info',
        'expired': 'warning',
        'suspended': 'warning',
        'revoked': 'danger'
    };
    return typeMap[status] || 'info';
},
openAddDialog() {
    this.editMode = false;
    this.licenseForm = {
        license_code: '',
        product_id: null,
        agent_id: null,
        user_id: '',
        max_api_calls: 1000,
        expire_date: '',
        status: 'inactive',
        notes: ''
    };
    this.dialogVisible = true;
},
editLicense(license) {
    this.editMode = true;
    this.currentLicense = license;
    this.licenseForm = {
        license_code: license.license_code,
        product_id: license.product_id,
        agent_id: license.agent_id,
        user_id: license.user_id || '',
        max_api_calls: license.max_api_calls,
        expire_date: license.expire_date ? license.expire_date : '',
        status: license.status,
        notes: license.notes || ''
    };
    this.dialogVisible = true;
},
saveLicense() {
    this.$refs.licenseFormRef.validate((valid) => {
        if (valid) {
            const url = this.editMode ? `/api/licenses/${this.currentLicense.id}` : '/api/licenses';
            const method = this.editMode ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.licenseForm)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => Promise.reject(err));
                }
                return response.json();
            })
            .then(data => {
                ElMessage.success(this.editMode ? '授权码更新成功' : '授权码创建成功');
                this.dialogVisible = false;
                this.refreshLicenses();
            })
            .catch(error => {
                console.error('Error saving license:', error);
                ElMessage.error('操作失败：' + (error.detail || error.message || '未知错误'));
            });
        }
    });
},
suspendLicense(license) {
    ElMessageBox.confirm(`确定要暂停授权码 ${license.license_code} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        fetch(`/api/licenses/${license.id}/suspend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (!response.ok) {
                // 尝试解析错误响应
                return response.text().then(text => {
                    try {
                        const error = JSON.parse(text);
                        return Promise.reject(error);
                    } catch {
                        return Promise.reject({ detail: text || '请求失败' });
                    }
                });
            }
            // 204 No Content 响应，直接返回成功
            return Promise.resolve();
        })
        .then(() => {
            ElMessage.success('授权码暂停成功');
            this.refreshLicenses();
        })
        .catch(error => {
            console.error('Error suspending license:', error);
            ElMessage.error('暂停失败：' + (error.detail || error.message || '未知错误'));
        });
    });
},
resumeLicense(license) {
    ElMessageBox.confirm(`确定要恢复授权码 ${license.license_code} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        fetch(`/api/licenses/${license.id}/resume`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (!response.ok) {
                // 尝试解析错误响应
                return response.text().then(text => {
                    try {
                        const error = JSON.parse(text);
                        return Promise.reject(error);
                    } catch {
                        return Promise.reject({ detail: text || '请求失败' });
                    }
                });
            }
            // 204 No Content 响应，直接返回成功
            return Promise.resolve();
        })
        .then(() => {
            ElMessage.success('授权码恢复成功');
            this.refreshLicenses();
        })
        .catch(error => {
            console.error('Error resuming license:', error);
            ElMessage.error('恢复失败：' + (error.detail || error.message || '未知错误'));
        });
    });
},
deleteLicense(license) {
    ElMessageBox.confirm(`确定要删除授权码 ${license.license_code} 吗？此操作不可恢复！`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        fetch(`/api/licenses/${license.id}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            return response.status === 204 ? {} : response.json();
        })
        .then(data => {
            ElMessage.success('授权码删除成功');
            this.refreshLicenses();
        })
        .catch(error => {
            console.error('Error deleting license:', error);
            ElMessage.error('删除失败：' + (error.detail || error.message || '未知错误'));
        });
    });
},
// 搜索和分页相关方法
initializeData() {
    // 将模板中的授权码数据保存到allLicenses
    this.allLicenses = [...this.licenses];
    this.total = this.allLicenses.length;
    this.applyFiltersAndPagination();
},
applyFiltersAndPagination() {
    let filteredLicenses = [...this.allLicenses];

    // 应用搜索过滤
    if (this.searchForm.keyword) {
        const keyword = this.searchForm.keyword.toLowerCase();
        filteredLicenses = filteredLicenses.filter(license =>
            license.license_code.toLowerCase().includes(keyword) ||
            (license.user_id && license.user_id.toLowerCase().includes(keyword)) ||
            (license.notes && license.notes.toLowerCase().includes(keyword))
        );
    }

    // 应用状态过滤
    if (this.searchForm.status) {
        filteredLicenses = filteredLicenses.filter(license => license.status === this.searchForm.status);
    }

    // 应用产品过滤
    if (this.searchForm.product_id !== null && this.searchForm.product_id !== '') {
        filteredLicenses = filteredLicenses.filter(license => license.product_id === this.searchForm.product_id);
    }

    // 应用代理商过滤
    if (this.searchForm.agent_id !== null && this.searchForm.agent_id !== '') {
        filteredLicenses = filteredLicenses.filter(license => license.agent_id === this.searchForm.agent_id);
    }

    // 应用排序
    filteredLicenses.sort((a, b) => {
        let aValue = a[this.sortField];
        let bValue = b[this.sortField];

        // 处理时间字段
        if (this.sortField === 'created_at' || this.sortField === 'expire_date') {
            aValue = aValue ? new Date(aValue).getTime() : 0;
            bValue = bValue ? new Date(bValue).getTime() : 0;
        }

        // 处理空值
        if (!aValue && !bValue) return 0;
        if (!aValue) return this.sortOrder === 'asc' ? -1 : 1;
        if (!bValue) return this.sortOrder === 'asc' ? 1 : -1;

        // 比较值
        if (aValue < bValue) return this.sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return this.sortOrder === 'asc' ? 1 : -1;
        return 0;
    });

    // 更新总数
    this.total = filteredLicenses.length;

    // 应用分页
    const start = (this.currentPage - 1) * this.pageSize;
    const end = start + this.pageSize;
    this.licenses = filteredLicenses.slice(start, end);
},
searchLicenses() {
    this.currentPage = 1;
    this.applyFiltersAndPagination();
},
resetSearch() {
    this.searchForm = {
        keyword: '',
        status: '',
        product_id: null,
        agent_id: null
    };
    this.currentPage = 1;
    this.sortField = 'created_at';
    this.sortOrder = 'desc';
    this.applyFiltersAndPagination();
},
handleSizeChange(newSize) {
    this.pageSize = newSize;
    this.currentPage = 1;
    this.applyFiltersAndPagination();
},
handleCurrentChange(newPage) {
    this.currentPage = newPage;
    this.applyFiltersAndPagination();
},
handleSortChange({ column, prop, order }) {
    if (prop && (prop === 'created_at' || prop === 'expire_date')) {
        this.sortField = prop;
        this.sortOrder = order === 'ascending' ? 'asc' : 'desc';
        this.applyFiltersAndPagination();
    }
},
refreshLicenses() {
    this.loading = true;
    window.location.reload();
}
{% endblock %}

{% block mounted %}
// 初始化数据
this.initializeData();
{% endblock %}
