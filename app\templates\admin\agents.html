{% extends "admin/base.html" %}

{% block title %}代理商管理 - FocuSee 管理系统{% endblock %}
{% block page_title %}代理商管理{% endblock %}

{% block data %}
    agents: [
        {% for agent in agents %}
        {
            id: {{ agent.id }},
            username: "{{ agent.username }}",
            company_name: "{{ agent.company_name }}",
            contact_name: "{{ agent.contact_name }}",
            contact_email: "{{ agent.contact_email }}",
            contact_phone: "{{ agent.contact_phone }}",
            address: "{{ agent.address or '' }}",
            description: "{{ agent.description or '' }}",
            is_active: {{ 'true' if agent.is_active else 'false' }},
            created_at: "{{ agent.created_at.isoformat() if agent.created_at else '' }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],

    // 搜索表单
    searchForm: {
        keyword: '',
        status: ''
    },

    // 分页
    pagination: {
        currentPage: 1,
        pageSize: 20,
        total: {{ total }}
    },

    // 状态
    loading: false,

    // 对话框
    dialogVisible: false,
    dialogTitle: "添加代理商",
    isEdit: false,
    submitting: false,

    // 表单数据
    agentForm: {
        username: "",
        password: "",
        company_name: "",
        contact_name: "",
        contact_email: "",
        contact_phone: "",
        address: "",
        description: "",
        is_active: true
    },

    // 表单验证规则
    formRules: {
        username: [
            { required: true, message: "请输入用户名", trigger: "blur" },
            { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" }
        ],
        password: [
            { required: true, message: "请输入密码", trigger: "blur" },
            { min: 6, message: "密码长度不能小于 6 个字符", trigger: "blur" }
        ],
        company_name: [
            { required: true, message: "请输入公司名称", trigger: "blur" }
        ],
        contact_name: [
            { required: true, message: "请输入联系人姓名", trigger: "blur" }
        ],
        contact_email: [
            { required: true, message: "请输入联系邮箱", trigger: "blur" },
            { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
        ],
        contact_phone: [
            { required: true, message: "请输入联系电话", trigger: "blur" }
        ]
    }
{% endblock %}

{% block content %}
<div class="content-card">
    <!-- 顶部操作栏 -->
    <div style="margin-bottom: 20px;">
        <el-row :gutter="20">
            <el-col :span="6">
                <el-input
                    v-model="searchForm.keyword"
                    placeholder="搜索用户名、公司名称或联系人"
                    @keyup.enter="handleSearch"
                    clearable>
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
            </el-col>
            <el-col :span="4">
                <el-select
                    v-model="searchForm.status"
                    placeholder="状态筛选"
                    @change="handleSearch"
                    clearable>
                    <el-option label="激活" value="true"></el-option>
                    <el-option label="禁用" value="false"></el-option>
                </el-select>
            </el-col>
            <el-col :span="6">
                <el-button @click="handleSearch">
                    <el-icon><Search /></el-icon>
                    查询
                </el-button>
                <el-button @click="resetSearch">
                    <el-icon><Refresh /></el-icon>
                    重置
                </el-button>
            </el-col>
            <el-col :span="8" style="text-align: right;">
                <el-button type="primary" @click="showCreateDialog">
                    <el-icon><Plus /></el-icon>
                    添加代理商
                </el-button>
            </el-col>
        </el-row>
    </div>

    <!-- 代理商列表 -->
    <div class="table-container">
        <el-table :data="filteredAgents" v-loading="loading" stripe>
            <el-table-column prop="id" label="ID" width="80" sortable></el-table-column>
            <el-table-column prop="username" label="用户名" width="120" sortable></el-table-column>
            <el-table-column prop="company_name" label="公司名称" min-width="150" sortable></el-table-column>
            <el-table-column prop="contact_name" label="联系人" width="100"></el-table-column>
            <el-table-column prop="contact_email" label="邮箱" min-width="150"></el-table-column>
            <el-table-column prop="contact_phone" label="电话" width="120"></el-table-column>
            <el-table-column prop="is_active" label="状态" width="100">
                <template #default="scope">
                    <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                        {% raw %}{{ scope.row.is_active ? '激活' : '禁用' }}{% endraw %}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180" sortable>
                <template #default="scope">
                    {% raw %}{{ formatDateTime(scope.row.created_at) }}{% endraw %}
                </template>
            </el-table-column>
            <el-table-column label="操作" width="250" fixed="right">
                <template #default="scope">
                    <el-button size="small" @click="showEditDialog(scope.row)">编辑</el-button>
                    <el-button size="small" type="warning" @click="showAuthDialog(scope.row)">产品授权</el-button>
                    <el-button
                        size="small"
                        :type="scope.row.is_active ? 'danger' : 'success'"
                        @click="toggleStatus(scope.row)">
                        {% raw %}{{ scope.row.is_active ? '禁用' : '启用' }}{% endraw %}
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pagination.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total">
            </el-pagination>
        </div>
    </div>
</div>

<!-- 创建/编辑代理商对话框 -->
<el-dialog v-model="dialogVisible" :title="dialogTitle" width="700px">
    <el-form :model="agentForm" :rules="formRules" ref="agentFormRef" label-width="120px">
        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="agentForm.username" placeholder="请输入用户名" :disabled="isEdit"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="密码" prop="password" v-if="!isEdit">
                    <el-input v-model="agentForm.password" type="password" placeholder="请输入密码"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        
        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="公司名称" prop="company_name">
                    <el-input v-model="agentForm.company_name" placeholder="请输入公司名称"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="联系人" prop="contact_name">
                    <el-input v-model="agentForm.contact_name" placeholder="请输入联系人姓名"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        
        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="联系邮箱" prop="contact_email">
                    <el-input v-model="agentForm.contact_email" placeholder="请输入邮箱"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="联系电话" prop="contact_phone">
                    <el-input v-model="agentForm.contact_phone" placeholder="请输入电话"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        
        <el-form-item label="公司地址" prop="address">
            <el-input v-model="agentForm.address" placeholder="请输入公司地址"></el-input>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
            <el-input type="textarea" v-model="agentForm.description" placeholder="请输入描述" :rows="3"></el-input>
        </el-form-item>
        
        <el-form-item label="状态" prop="is_active">
            <el-switch v-model="agentForm.is_active" active-text="激活" inactive-text="禁用"></el-switch>
        </el-form-item>
    </el-form>
    <template #footer>
        <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </div>
    </template>
</el-dialog>
{% endblock %}

{% block methods %}
// 格式化日期时间
formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
},

// 搜索处理
handleSearch() {
    this.pagination.currentPage = 1;
    this.loadAgents();
},

// 重置搜索
resetSearch() {
    this.searchForm = {
        keyword: '',
        status: ''
    };
    this.pagination.currentPage = 1;
    this.loadAgents();
},

// 分页处理
handleSizeChange(val) {
    this.pagination.pageSize = val;
    this.pagination.currentPage = 1;
    this.loadAgents();
},

handleCurrentChange(val) {
    this.pagination.currentPage = val;
    this.loadAgents();
},
showCreateDialog() {
    this.dialogVisible = true;
    this.dialogTitle = "添加代理商";
    this.isEdit = false;
    this.agentForm = {
        username: "",
        password: "",
        company_name: "",
        contact_name: "",
        contact_email: "",
        contact_phone: "",
        address: "",
        description: "",
        is_active: true
    };
    if (this.$refs.agentFormRef) {
        this.$refs.agentFormRef.resetFields();
    }
},
showEditDialog(agent) {
    this.dialogVisible = true;
    this.dialogTitle = "编辑代理商";
    this.isEdit = true;
    this.agentForm = {
        id: agent.id,
        username: agent.username,
        company_name: agent.company_name,
        contact_name: agent.contact_name,
        contact_email: agent.contact_email,
        contact_phone: agent.contact_phone,
        address: agent.address || '',
        description: agent.description || '',
        is_active: agent.is_active
    };
},
submitForm() {
    if (this.$refs.agentFormRef) {
        this.$refs.agentFormRef.validate(async (valid) => {
            if (valid) {
                this.submitting = true;
                try {
                    const url = this.isEdit ? `/api/agents/${this.agentForm.id}` : '/api/agents';
                    const method = this.isEdit ? 'PUT' : 'POST';
                    
                    const response = await fetch(url, {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(this.agentForm)
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.detail || '操作失败');
                    }
                    
                    ElMessage.success(this.isEdit ? '代理商更新成功' : '代理商创建成功');
                    this.dialogVisible = false;
                    this.loadAgents();
                } catch (error) {
                    ElMessage.error(error.message || '操作失败');
                } finally {
                    this.submitting = false;
                }
            }
        });
    }
},
toggleStatus(agent) {
    ElMessageBox.confirm(
        `确定要${agent.is_active ? '禁用' : '启用'}该代理商吗？`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(async () => {
        try {
            const response = await fetch(`/api/agents/${agent.id}/toggle-status`, {
                method: 'POST'
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || '操作失败');
            }
            
            ElMessage.success(`代理商已${agent.is_active ? '禁用' : '启用'}`);
            this.loadAgents();
        } catch (error) {
            ElMessage.error(error.message || '操作失败');
        }
    }).catch(() => {});
},
showAuthDialog(agent) {
    // 跳转到产品授权页面
    window.location.href = `/admin/agents/${agent.id}/products`;
},
// 加载代理商列表
loadAgents() {
    this.loading = true;
    const skip = (this.pagination.currentPage - 1) * this.pagination.pageSize;
    let url = `/api/agents?skip=${skip}&limit=${this.pagination.pageSize}`;

    if (this.searchForm.keyword) {
        url += `&keyword=${encodeURIComponent(this.searchForm.keyword)}`;
    }

    if (this.searchForm.status !== "") {
        url += `&is_active=${this.searchForm.status}`;
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            this.agents = Array.isArray(data) ? data : data.items || [];
            this.pagination.total = Array.isArray(data) ? data.length : data.total || 0;
            this.loading = false;
        })
        .catch(error => {
            console.error('Error loading agents:', error);
            ElMessage.error('加载代理商列表失败');
            this.loading = false;
        });
}
{% endblock %}

{% block computed %}
// 过滤后的代理商列表
filteredAgents() {
    return this.agents;
}
{% endblock %}

{% block mounted %}
this.loadAgents();
{% endblock %}

{% block style %}
<style scoped>
.content-card {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-container {
    margin-top: 20px;
}

.pagination-container {
    margin-top: 20px;
    text-align: right;
}

.el-table {
    border-radius: 8px;
    overflow: hidden;
}

.el-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
}

.el-table td {
    padding: 12px 0;
}

.el-tag {
    font-weight: 500;
}

.dialog-footer {
    text-align: right;
}

.el-form-item {
    margin-bottom: 20px;
}

.el-input, .el-select, .el-textarea {
    width: 100%;
}

@media (max-width: 768px) {
    .content-card {
        padding: 16px;
    }

    .el-col {
        margin-bottom: 10px;
    }
}
</style>
{% endblock %}
