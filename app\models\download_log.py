from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.sql import func
from app.database import Base

class DownloadLog(Base):
    __tablename__ = "download_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(100), index=True, nullable=False, comment="用户ID")
    license_key = Column(String(255), nullable=False, comment="授权码")
    system_id = Column(String(255), comment="系统序列号")
    computer_name = Column(String(255), comment="计算机名称")
    ip_address = Column(String(45), comment="IP地址")
    file_name = Column(String(255), comment="下载文件名")
    download_time = Column(DateTime(timezone=True), server_default=func.now(), comment="下载时间")
    user_agent = Column(Text, comment="用户代理")
    
    def __repr__(self):
        return f"<DownloadLog(user_id='{self.user_id}', file_name='{self.file_name}', download_time='{self.download_time}')>"
