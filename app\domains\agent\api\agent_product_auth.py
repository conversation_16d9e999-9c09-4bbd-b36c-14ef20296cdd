from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel
from app.database import get_db
from app.services.agent_product_auth_service import AgentProductAuthService
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

class AgentProductAuthCreate(BaseModel):
    agent_id: int
    product_id: int
    max_licenses: int
    expire_date: Optional[datetime] = None

class AgentProductAuthUpdate(BaseModel):
    max_licenses: Optional[int] = None
    expire_date: Optional[datetime] = None
    is_active: Optional[bool] = None

class AgentProductAuthResponse(BaseModel):
    id: int
    agent_id: int
    product_id: int
    product_name: Optional[str] = None
    max_licenses: int
    used_licenses: int
    expire_date: Optional[datetime] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

@router.post("/", response_model=AgentProductAuthResponse, status_code=status.HTTP_201_CREATED)
async def create_agent_product_auth(
    auth_data: AgentProductAuthCreate,
    db: Session = Depends(get_db)
):
    """创建代理商产品授权（管理员功能）"""
    auth = AgentProductAuthService.create_agent_product_auth(
        db, 
        auth_data.agent_id, 
        auth_data.product_id, 
        auth_data.max_licenses,
        auth_data.expire_date
    )
    
    if not auth:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to create agent product authorization. Agent or product may not exist, or authorization already exists."
        )
    
    return auth

@router.get("/", response_model=List[AgentProductAuthResponse])
async def get_all_agent_product_auths(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取所有代理商产品授权（管理员功能）"""
    auths = AgentProductAuthService.get_all_agent_product_auths(db, skip, limit)
    return auths

@router.get("/agent/{agent_id}", response_model=List[AgentProductAuthResponse])
async def get_agent_product_auths(
    agent_id: int,
    db: Session = Depends(get_db)
):
    """获取指定代理商的产品授权列表"""
    auths = AgentProductAuthService.get_agent_product_auths(db, agent_id)

    # 添加产品名称
    result = []
    for auth in auths:
        auth_dict = {
            "id": auth.id,
            "agent_id": auth.agent_id,
            "product_id": auth.product_id,
            "product_name": auth.product.name if auth.product else "",
            "max_licenses": auth.max_licenses,
            "used_licenses": auth.used_licenses,
            "expire_date": auth.expire_date,
            "is_active": auth.is_active,
            "created_at": auth.created_at,
            "updated_at": auth.updated_at
        }
        result.append(auth_dict)

    return result

@router.get("/agent/{agent_id}/product/{product_id}", response_model=AgentProductAuthResponse)
async def get_agent_product_auth(
    agent_id: int,
    product_id: int,
    db: Session = Depends(get_db)
):
    """获取代理商特定产品的授权"""
    auth = AgentProductAuthService.get_agent_product_auth(db, agent_id, product_id)
    if not auth:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent product authorization not found"
        )
    
    return auth

@router.put("/{auth_id}", response_model=AgentProductAuthResponse)
async def update_agent_product_auth(
    auth_id: int,
    auth_data: AgentProductAuthUpdate,
    db: Session = Depends(get_db)
):
    """更新代理商产品授权"""
    auth = AgentProductAuthService.update_agent_product_auth(
        db, auth_id, auth_data.max_licenses, auth_data.expire_date, auth_data.is_active
    )

    if not auth:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent product authorization not found"
        )

    return auth

@router.delete("/{auth_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_agent_product_auth(
    auth_id: int,
    db: Session = Depends(get_db)
):
    """删除代理商产品授权"""
    success = AgentProductAuthService.delete_agent_product_auth(db, auth_id)
    if not success:
        # 检查是否是因为有相关授权码而无法删除
        from app.models.license import License
        from app.models.agent_product_auth import AgentProductAuth

        auth = db.query(AgentProductAuth).filter(AgentProductAuth.id == auth_id).first()
        if auth:
            active_licenses = db.query(License).filter(
                License.agent_id == auth.agent_id,
                License.product_id == auth.product_id,
                License.status.in_(['active', 'inactive'])
            ).count()

            if active_licenses > 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无法删除该产品授权，因为还有 {active_licenses} 个相关的授权码。请先删除或停用相关授权码。"
                )

        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent product authorization not found"
        )

@router.get("/agent/{agent_id}/product/{product_id}/check")
async def check_license_availability(
    agent_id: int,
    product_id: int,
    db: Session = Depends(get_db)
):
    """检查代理商产品授权的可用性"""
    available = AgentProductAuthService.check_license_availability(db, agent_id, product_id)
    
    return {
        "available": available,
        "agent_id": agent_id,
        "product_id": product_id
    }
