#!/usr/bin/env python3
"""
简化的签名验证测试
"""

def test_signature_verification():
    """测试签名验证"""
    print("🔐 测试支付宝签名验证")
    print("=" * 50)
    
    try:
        from app.services.alipay_service import AlipayService
        
        service = AlipayService()
        print(f"支付宝公钥配置: {'已配置' if service.alipay_public_key else '未配置'}")
        
        # 使用真实的回调数据
        test_data = {
            'app_id': '****************',
            'auth_app_id': '****************',
            'body': '用户4购买FocuSee Pro',
            'buyer_id': '****************',
            'buyer_logon_id': '<EMAIL>',
            'buyer_pay_amount': '98.99',
            'charset': 'utf-8',
            'fund_bill_list': '[{"amount":"98.99","fundChannel":"ALIPAYACCOUNT"}]',
            'gmt_create': '2025-08-07 15:43:43',
            'gmt_payment': '2025-08-07 15:43:50',
            'invoice_amount': '98.99',
            'notify_id': '2025080701222154351188780506968604',
            'notify_time': '2025-08-07 15:43:51',
            'notify_type': 'trade_status_sync',
            'out_trade_no': 'PAY17545526179196',
            'point_amount': '0.00',
            'receipt_amount': '98.99',
            'seller_email': '<EMAIL>',
            'seller_id': '****************',
            'subject': '购买FocuSee Pro',
            'total_amount': '98.99',
            'trade_no': '2025080722001488780508293873',
            'trade_status': 'TRADE_SUCCESS',
            'version': '1.0',
            'sign': 'PM0lVeXarWGiB3ZiIo3MDN7oqF4fV05lKWszQD7kr+x2ZxCBdFxFdIhJMOlQEbVWneSQXL5vML7bqMFV/pAkU8A95BeRZ+tN55ObjaMgN6kkxpx01YfJSZEP2B1PczYGAwFhYFJltv+XV+aVV3SyBhM073omI0vMlHfrE9NgmHXo3NWoMpqAYZZtmWCory/zoGSGXsYr+Oq+Hwi7Mw1t4ZkLcf6v+81Sy6wI0EKVSxvgVmRruduhF0iNh2or2wiMsFvDY1VkJtQ/HNW6aHqlp4szjX1uGvjJIfqXNzT0akjtU9I9DmYy+WF4518J1BhonFhZ83ZbL+U5aKRbkkdTZw==',
            'sign_type': 'RSA2'
        }
        
        print("测试签名验证...")
        result = service.verify_notify(test_data)
        
        if result:
            print("✅ 签名验证成功")
        else:
            print("❌ 签名验证失败")
            
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_verification():
    """手动测试签名验证步骤"""
    print(f"\n🔧 手动测试签名验证步骤")
    print("=" * 50)
    
    try:
        # 测试数据（移除sign和sign_type）
        test_data = {
            'app_id': '****************',
            'auth_app_id': '****************',
            'body': '用户4购买FocuSee Pro',
            'buyer_id': '****************',
            'buyer_logon_id': '<EMAIL>',
            'buyer_pay_amount': '98.99',
            'charset': 'utf-8',
            'fund_bill_list': '[{"amount":"98.99","fundChannel":"ALIPAYACCOUNT"}]',
            'gmt_create': '2025-08-07 15:43:43',
            'gmt_payment': '2025-08-07 15:43:50',
            'invoice_amount': '98.99',
            'notify_id': '2025080701222154351188780506968604',
            'notify_time': '2025-08-07 15:43:51',
            'notify_type': 'trade_status_sync',
            'out_trade_no': 'PAY17545526179196',
            'point_amount': '0.00',
            'receipt_amount': '98.99',
            'seller_email': '<EMAIL>',
            'seller_id': '****************',
            'subject': '购买FocuSee Pro',
            'total_amount': '98.99',
            'trade_no': '2025080722001488780508293873',
            'trade_status': 'TRADE_SUCCESS',
            'version': '1.0'
        }
        
        sign = 'PM0lVeXarWGiB3ZiIo3MDN7oqF4fV05lKWszQD7kr+x2ZxCBdFxFdIhJMOlQEbVWneSQXL5vML7bqMFV/pAkU8A95BeRZ+tN55ObjaMgN6kkxpx01YfJSZEP2B1PczYGAwFhYFJltv+XV+aVV3SyBhM073omI0vMlHfrE9NgmHXo3NWoMpqAYZZtmWCory/zoGSGXsYr+Oq+Hwi7Mw1t4ZkLcf6v+81Sy6wI0EKVSxvgVmRruduhF0iNh2or2wiMsFvDY1VkJtQ/HNW6aHqlp4szjX1uGvjJIfqXNzT0akjtU9I9DmYy+WF4518J1BhonFhZ83ZbL+U5aKRbkkdTZw=='
        
        # 1. 排序参数
        sorted_items = sorted(test_data.items())
        print(f"1. 排序后的参数: {len(sorted_items)} 个")
        
        # 2. 拼接字符串
        query_string = "&".join([f"{k}={v}" for k, v in sorted_items])
        print(f"2. 待验证字符串长度: {len(query_string)}")
        print(f"   前100字符: {query_string[:100]}...")
        
        # 3. 检查签名
        print(f"3. 签名长度: {len(sign)}")
        print(f"   签名前50字符: {sign[:50]}...")
        
        # 4. 尝试验证
        try:
            import base64
            from Crypto.PublicKey import RSA
            from Crypto.Signature import PKCS1_v1_5
            from Crypto.Hash import SHA256
            
            # 获取公钥
            from app.services.alipay_service import AlipayService
            service = AlipayService()
            
            public_key_str = service.alipay_public_key
            if not public_key_str.startswith('-----BEGIN PUBLIC KEY-----'):
                public_key_pem = f"-----BEGIN PUBLIC KEY-----\n{public_key_str}\n-----END PUBLIC KEY-----"
            else:
                public_key_pem = public_key_str
            
            print(f"4. 公钥长度: {len(public_key_str)}")
            
            # 导入公钥
            public_key = RSA.import_key(public_key_pem)
            print(f"5. 公钥导入成功")
            
            # 创建验证器
            verifier = PKCS1_v1_5.new(public_key)
            
            # 计算哈希
            digest = SHA256.new(query_string.encode('utf-8'))
            print(f"6. 哈希计算完成")
            
            # 解码签名
            signature = base64.b64decode(sign)
            print(f"7. 签名解码完成，长度: {len(signature)}")
            
            # 验证签名
            result = verifier.verify(digest, signature)
            
            if result:
                print("✅ 手动验证成功")
            else:
                print("❌ 手动验证失败")
                
            return result
            
        except Exception as e:
            print(f"❌ 手动验证异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 手动测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 简化签名验证测试")
    
    # 测试结果
    results = []
    
    # 1. 测试服务验证
    results.append(("服务验证", test_signature_verification()))
    
    # 2. 测试手动验证
    results.append(("手动验证", test_manual_verification()))
    
    # 显示结果
    print(f"\n" + "=" * 50)
    print("📊 测试结果:")
    print("-" * 30)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} - {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count > 0:
        print("🎉 签名验证有进展！")
    else:
        print("⚠️  签名验证仍有问题")
        print("可能原因:")
        print("- 公钥格式不正确")
        print("- 参数拼接方式不对")
        print("- 签名算法不匹配")
    
    return success_count > 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
