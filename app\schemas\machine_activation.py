from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class MachineActivationBase(BaseModel):
    machine_id: str
    days: int = 30
    notes: Optional[str] = None
    algorithm_id: Optional[int] = None

class MachineActivationCreate(MachineActivationBase):
    pass

class MachineActivationUpdate(BaseModel):
    notes: Optional[str] = None
    is_active: Optional[bool] = None

class MachineActivationResponse(BaseModel):
    id: int
    machine_id: str
    activation_code: str
    days: int
    expire_timestamp: int
    generation_timestamp: int
    is_active: bool
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class MachineActivationListResponse(BaseModel):
    items: list[MachineActivationResponse]
    total: int
    skip: int
    limit: int

class MachineActivationStatsResponse(BaseModel):
    total_count: int
    active_count: int
    valid_count: int
    expired_count: int
