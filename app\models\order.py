from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Boolean, Enum, Text, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum

class OrderStatus(enum.Enum):
    PENDING = "pending"      # 待处理
    CONFIRMED = "confirmed"  # 已确认
    COMPLETED = "completed"  # 已完成
    CANCELLED = "cancelled"  # 已取消
    EXPIRED = "expired"      # 已过期

class Order(Base):
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_number = Column(String(50), unique=True, nullable=False, comment="订单号")
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True, comment="代理商ID（可为空，表示管理员创建）")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="产品ID")
    quantity = Column(Integer, default=1, comment="授权数量")
    unit_price = Column(Numeric(10, 2), comment="单价")
    total_price = Column(Numeric(10, 2), comment="总价")
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING, comment="订单状态")
    expire_date = Column(DateTime(timezone=True), comment="授权过期时间")
    customer_info = Column(Text, comment="客户信息（JSON格式）")
    notes = Column(Text, comment="备注")
    created_by_admin = Column(Boolean, default=False, comment="是否由管理员创建")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    agent = relationship("Agent", backref="orders")
    product = relationship("Product", backref="orders")
    
    def __repr__(self):
        return f"<Order(order_number='{self.order_number}', status='{self.status.value}', quantity={self.quantity})>"
