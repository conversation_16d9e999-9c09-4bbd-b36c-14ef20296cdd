from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.schemas.license import LicenseCreate, LicenseUpdate, LicenseResponse, LicenseVerifyRequest, LicenseActivateRequest
from app.models.license import LicenseStatus
from app.services.license_service import LicenseService
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=LicenseResponse, status_code=status.HTTP_201_CREATED)
async def create_license(
    license_data: LicenseCreate,
    db: Session = Depends(get_db)
):
    """创建授权码"""
    license = LicenseService.create_license(db, license_data)
    if not license:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to create license. Please check the provided data."
        )
    
    return license

@router.get("/", response_model=List[LicenseResponse])
async def get_licenses(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    agent_id: Optional[int] = Query(None),
    product_id: Optional[int] = Query(None),
    status: Optional[LicenseStatus] = Query(None),
    user_id: Optional[str] = Query(None),
    keyword: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取授权码列表"""
    if keyword:
        licenses = LicenseService.search_licenses(db, keyword, skip, limit)
    else:
        licenses = LicenseService.get_licenses(db, skip, limit, agent_id, product_id, status, user_id)
    
    return licenses

@router.get("/{license_id}", response_model=LicenseResponse)
async def get_license(
    license_id: int,
    db: Session = Depends(get_db)
):
    """获取单个授权码"""
    license = LicenseService.get_license_by_id(db, license_id)
    if not license:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="License not found"
        )
    
    return license

@router.put("/{license_id}", response_model=LicenseResponse)
async def update_license(
    license_id: int,
    license_data: LicenseUpdate,
    db: Session = Depends(get_db)
):
    """更新授权码"""
    license = LicenseService.update_license(db, license_id, license_data)
    if not license:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update license. License may not exist or data is invalid."
        )
    
    return license

@router.delete("/{license_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_license(
    license_id: int,
    db: Session = Depends(get_db)
):
    """删除授权码"""
    success = LicenseService.delete_license(db, license_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="License not found"
        )



@router.get("/stats/count")
async def get_license_count(
    agent_id: Optional[int] = Query(None),
    product_id: Optional[int] = Query(None),
    status: Optional[LicenseStatus] = Query(None),
    user_id: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取授权码统计"""
    count = LicenseService.get_license_count(db, agent_id, product_id, status, user_id)
    return {"count": count}

@router.post("/verify")
async def verify_license(
    verify_data: LicenseVerifyRequest,
    db: Session = Depends(get_db)
):
    """验证授权码（公开接口，用于客户端验证）"""
    license = LicenseService.verify_license(db, verify_data)
    if not license:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="License not found or invalid"
        )
    
    # 增加API使用次数
    LicenseService.increment_api_usage(db, verify_data.license_code)
    
    return {
        "valid": True,
        "license_code": license.license_code,
        "product_id": license.product_id,
        "user_id": license.user_id,
        "max_api_calls": license.max_api_calls,
        "used_api_calls": license.used_api_calls,
        "expire_date": license.expire_date,
        "status": license.status.value
    }

@router.post("/activate")
async def activate_license(
    activate_data: LicenseActivateRequest,
    db: Session = Depends(get_db)
):
    """激活授权码（公开接口，用于客户端激活）"""
    license = LicenseService.activate_license(db, activate_data)
    if not license:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to activate license. License may not exist, be expired, or already activated."
        )
    
    return {
        "activated": True,
        "license_code": license.license_code,
        "product_id": license.product_id,
        "user_id": license.user_id,
        "max_api_calls": license.max_api_calls,
        "expire_date": license.expire_date,
        "activated_at": license.activated_at
    }

@router.get("/by-code/{license_code}", response_model=LicenseResponse)
async def get_license_by_code(
    license_code: str,
    db: Session = Depends(get_db)
):
    """根据授权码获取授权信息"""
    license = LicenseService.get_license_by_code(db, license_code)
    if not license:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="License not found"
        )
    
    return license

@router.get("/by-code/{license_code}/info")
async def get_license_info(
    license_code: str,
    db: Session = Depends(get_db)
):
    """获取授权码信息（公开接口，用于客户端查询）"""
    license = LicenseService.get_license_by_code(db, license_code)
    if not license:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="License not found"
        )
    
    return {
        "license_code": license.license_code,
        "product_id": license.product_id,
        "status": license.status.value,
        "max_api_calls": license.max_api_calls,
        "used_api_calls": license.used_api_calls,
        "expire_date": license.expire_date,
        "is_active": license.status == LicenseStatus.ACTIVE,
        "is_expired": license.expire_date and license.expire_date < datetime.now() if license.expire_date else False
    }

@router.post("/{license_id}/suspend", status_code=status.HTTP_204_NO_CONTENT)
async def suspend_license(
    license_id: int,
    db: Session = Depends(get_db)
):
    """暂停授权码"""
    license = LicenseService.get_license_by_id(db, license_id)
    if not license:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="License not found"
        )
    
    if license.status != LicenseStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only active licenses can be suspended"
        )
    
    license.status = LicenseStatus.SUSPENDED
    db.commit()

@router.post("/{license_id}/resume", status_code=status.HTTP_204_NO_CONTENT)
async def resume_license(
    license_id: int,
    db: Session = Depends(get_db)
):
    """恢复授权码"""
    license = LicenseService.get_license_by_id(db, license_id)
    if not license:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="License not found"
        )
    
    if license.status != LicenseStatus.SUSPENDED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only suspended licenses can be resumed"
        )
    
    license.status = LicenseStatus.ACTIVE
    db.commit()
