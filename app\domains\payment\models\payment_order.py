from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Boolean, Enum, Text, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum

class PaymentMethod(enum.Enum):
    ALIPAY_FACE_TO_FACE = "alipay_face_to_face"  # 支付宝当面付
    ALIPAY_ORDER_CODE = "alipay_order_code"      # 支付宝订单码支付

class PaymentStatus(enum.Enum):
    PENDING = "pending"          # 待支付
    PAID = "paid"               # 已支付
    CANCELLED = "cancelled"      # 已取消
    EXPIRED = "expired"         # 已过期
    REFUNDED = "refunded"       # 已退款
    FAILED = "failed"           # 支付失败

class PaymentOrder(Base):
    __tablename__ = "payment_orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_no = Column(String(64), unique=True, nullable=False, comment="支付订单号")
    user_id = Column(String(100), nullable=False, comment="用户ID")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="产品ID")
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True, comment="代理商ID（可为空）")
    
    # 支付信息
    payment_method = Column(Enum(PaymentMethod), nullable=False, comment="支付方式")
    amount = Column(Numeric(10, 2), nullable=False, comment="支付金额")
    currency = Column(String(3), default="CNY", comment="货币类型")
    status = Column(Enum(PaymentStatus), default=PaymentStatus.PENDING, comment="支付状态")
    
    # 支付宝相关字段
    alipay_trade_no = Column(String(64), comment="支付宝交易号")
    alipay_out_trade_no = Column(String(64), comment="商户订单号")
    alipay_qr_code = Column(Text, comment="支付二维码内容")
    alipay_order_code = Column(String(32), comment="支付宝订单码")
    
    # 订单信息
    subject = Column(String(256), nullable=False, comment="订单标题")
    body = Column(Text, comment="订单描述")
    
    # 时间字段
    expire_time = Column(DateTime(timezone=True), comment="订单过期时间")
    paid_at = Column(DateTime(timezone=True), comment="支付完成时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 扩展字段
    extra_data = Column(Text, comment="扩展数据（JSON格式）")
    notify_url = Column(String(512), comment="异步通知地址")
    return_url = Column(String(512), comment="同步跳转地址")
    
    # 关联关系
    product = relationship("Product", backref="payment_orders")
    agent = relationship("Agent", backref="payment_orders")
    
    def __repr__(self):
        return f"<PaymentOrder(order_no='{self.order_no}', status='{self.status.value}', amount={self.amount})>"
