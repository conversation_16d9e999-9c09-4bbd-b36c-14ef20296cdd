#!/usr/bin/env python3
"""
测试支付调试功能
"""

import requests
import json

def test_payment_debug_api():
    """测试支付调试API"""
    print("🔧 测试支付调试API")
    print("=" * 50)
    
    base_url = "http://localhost:8008"
    
    # 1. 测试获取配置列表
    print("1. 测试获取配置列表...")
    try:
        response = requests.get(f"{base_url}/api/payment-debug/config/list")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                configs = data.get("data", [])
                print(f"✅ 获取配置列表成功，共 {len(configs)} 个配置")
                for config in configs:
                    print(f"   - {config['config_name']} ({config['environment']})")
            else:
                print("❌ 获取配置列表失败")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取配置列表异常: {str(e)}")
        return False
    
    # 2. 测试创建调试支付
    print(f"\n2. 测试创建调试支付...")
    debug_data = {
        "debug_name": "API测试支付",
        "payment_mode": "face_to_face",
        "user_id": "test_debug_user",
        "product_id": 1,
        "amount": 0.01,
        "subject": "测试支付调试功能",
        "body": "这是一个API测试支付",
        "timeout_minutes": 30
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/payment-debug/debug/create",
            json=debug_data
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                debug_record = data.get("data")
                payment_result = data.get("payment_result")
                
                print("✅ 调试支付创建成功")
                print(f"   调试ID: {debug_record['id']}")
                print(f"   订单号: {debug_record['order_no']}")
                print(f"   状态: {debug_record['debug_status']}")
                
                if payment_result and payment_result.get("qr_code"):
                    print(f"   二维码: {payment_result['qr_code'][:50]}...")
                
                debug_id = debug_record['id']
                
                # 3. 测试查询支付状态
                print(f"\n3. 测试查询支付状态...")
                try:
                    response = requests.get(f"{base_url}/api/payment-debug/debug/{debug_id}/query")
                    if response.status_code == 200:
                        data = response.json()
                        if data.get("success"):
                            print("✅ 查询支付状态成功")
                            query_result = data.get("query_result")
                            if query_result:
                                print(f"   支付状态: {query_result.get('status')}")
                        else:
                            print(f"❌ 查询失败: {data.get('error')}")
                    else:
                        print(f"❌ 查询请求失败: {response.status_code}")
                except Exception as e:
                    print(f"❌ 查询异常: {str(e)}")
                
                return True
            else:
                print(f"❌ 创建失败: {data.get('error')}")
                return False
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 创建调试支付异常: {str(e)}")
        return False

def test_payment_debug_page():
    """测试支付调试页面"""
    print(f"\n🌐 测试支付调试页面")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:8008/admin/payment-debug")
        if response.status_code == 200:
            print("✅ 支付调试页面访问成功")
            print(f"   页面大小: {len(response.text)} 字符")
            
            # 检查页面内容
            content = response.text
            if "支付调试" in content:
                print("✅ 页面标题正确")
            if "新建调试" in content:
                print("✅ 包含新建调试按钮")
            if "配置管理" in content:
                print("✅ 包含配置管理功能")
            
            return True
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 页面访问异常: {str(e)}")
        return False

def test_debug_list():
    """测试调试记录列表"""
    print(f"\n📋 测试调试记录列表")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:8008/api/payment-debug/debug/list")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                records = data.get("data", {}).get("records", [])
                total = data.get("data", {}).get("total", 0)
                
                print(f"✅ 获取调试记录成功，共 {total} 条记录")
                
                for record in records[:3]:  # 显示前3条
                    print(f"   - {record['debug_name']} ({record['debug_status']})")
                
                return True
            else:
                print("❌ 获取调试记录失败")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取调试记录异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 支付调试功能测试")
    print("此测试将验证支付调试功能是否正常工作")
    
    # 测试结果
    results = []
    
    # 1. 测试API功能
    results.append(("API功能", test_payment_debug_api()))
    
    # 2. 测试页面访问
    results.append(("页面访问", test_payment_debug_page()))
    
    # 3. 测试记录列表
    results.append(("记录列表", test_debug_list()))
    
    # 显示测试结果
    print(f"\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("-" * 30)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} - {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    # 给出建议
    print(f"\n💡 测试总结:")
    if success_count == len(results):
        print("🎉 支付调试功能完全正常！")
        print("\n✨ 功能特点:")
        print("- ✅ 支付调试API正常工作")
        print("- ✅ 调试页面可以正常访问")
        print("- ✅ 记录列表功能正常")
        print("- ✅ 支付创建和查询功能正常")
        
        print(f"\n🎯 现在可以:")
        print("1. 访问 http://localhost:8008/admin/payment-debug")
        print("2. 配置支付宝参数")
        print("3. 创建调试支付")
        print("4. 实时查询支付状态")
        print("5. 查看支付二维码")
        
    elif success_count >= 2:
        print("🎉 支付调试功能基本正常！")
        print("\n✨ 部分功能正常:")
        print("- 大部分功能可以正常使用")
        print("- 可能个别功能需要调整")
        
    else:
        print("⚠️  支付调试功能有问题:")
        print("- 检查服务器是否正在运行")
        print("- 确认数据库表已创建")
        print("- 查看服务器日志详细错误")
    
    print(f"\n📋 使用指南:")
    print("1. 🔧 配置管理:")
    print("   - 设置支付宝APP_ID、密钥等参数")
    print("   - 选择沙箱或生产环境")
    print("   - 激活要使用的配置")
    
    print("2. 🧪 调试支付:")
    print("   - 创建测试支付订单")
    print("   - 获取支付二维码")
    print("   - 实时查询支付状态")
    
    print("3. 📊 状态监控:")
    print("   - 查看支付流程各个阶段")
    print("   - 监控回调接收情况")
    print("   - 分析错误信息")
    
    print(f"\n🔧 调试建议:")
    print("- 先在沙箱环境测试")
    print("- 确保回调地址可访问")
    print("- 检查支付宝配置正确性")
    print("- 查看详细的调试日志")
    
    return success_count >= 2

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
