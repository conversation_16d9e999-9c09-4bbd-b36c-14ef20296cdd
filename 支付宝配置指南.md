# 支付宝配置指南

## 🔧 配置步骤

### 1. 支付宝开放平台配置

#### 1.1 注册账号
1. 访问 [支付宝开放平台](https://open.alipay.com/)
2. 注册并完成企业认证（个人开发者可使用沙箱环境）

#### 1.2 创建应用
1. 登录开放平台控制台
2. 点击"创建应用" → 选择"网页&移动应用"
3. 填写应用信息：
   - 应用名称：FocuSee授权系统
   - 应用类型：网页应用
   - 应用描述：软件授权管理系统

#### 1.3 配置密钥
1. **生成RSA2密钥对**：
   ```bash
   # 使用支付宝提供的密钥生成工具
   # 下载地址：https://opendocs.alipay.com/common/02kipl
   ```

2. **上传应用公钥**：
   - 在应用详情页面，点击"设置应用公钥"
   - 粘贴生成的应用公钥内容

3. **获取支付宝公钥**：
   - 上传应用公钥后，系统会生成支付宝公钥
   - 复制支付宝公钥备用

#### 1.4 签约产品
1. 在应用详情页面，点击"产品签约"
2. 签约以下产品：
   - **当面付**（必需）
   - **手机网站支付**（可选）

### 2. 系统配置

#### 2.1 修改 .env 文件
```env
# 支付宝配置
ALIPAY_APP_ID=你的应用ID
ALIPAY_APP_PRIVATE_KEY=你的应用私钥（去掉头尾和换行符）
ALIPAY_PUBLIC_KEY=支付宝公钥（去掉头尾和换行符）
ALIPAY_NOTIFY_URL=https://yourdomain.com/api/payment/alipay/notify
ALIPAY_RETURN_URL=https://yourdomain.com/user/payment
```

#### 2.2 密钥格式说明

**应用私钥格式**（去掉头尾和换行符）：
```
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
```

**支付宝公钥格式**（去掉头尾和换行符）：
```
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
```

### 3. 沙箱环境测试

#### 3.1 沙箱配置
```env
# 沙箱环境配置
DEBUG=True
ALIPAY_APP_ID=沙箱应用ID
ALIPAY_APP_PRIVATE_KEY=沙箱应用私钥
ALIPAY_PUBLIC_KEY=沙箱支付宝公钥
```

#### 3.2 测试账号
- 支付宝提供沙箱测试账号
- 可以模拟真实支付流程
- 不会产生真实扣款

### 4. 生产环境配置

#### 4.1 安全配置
```env
# 生产环境配置
DEBUG=False
ALIPAY_APP_ID=正式应用ID
ALIPAY_APP_PRIVATE_KEY=正式应用私钥
ALIPAY_PUBLIC_KEY=正式支付宝公钥
ALIPAY_NOTIFY_URL=https://yourdomain.com/api/payment/alipay/notify
ALIPAY_RETURN_URL=https://yourdomain.com/user/payment
```

#### 4.2 域名配置
1. 确保域名已备案
2. 配置HTTPS证书
3. 在支付宝应用中配置授权回调地址

### 5. 功能验证

#### 5.1 测试支付功能
1. 启动应用：`python run.py`
2. 访问用户支付页面：http://localhost:8008/user/payment
3. 选择产品进行测试支付

#### 5.2 检查日志
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## 🚨 常见问题

### Q1: 支付宝通知验签失败？
**解决方案**：
1. 检查支付宝公钥配置是否正确
2. 确保密钥格式正确（去掉头尾和换行符）
3. 验证应用私钥和公钥是否匹配

### Q2: 二维码生成失败？
**解决方案**：
1. 检查应用私钥配置
2. 确认已在支付宝平台上传对应的公钥
3. 验证APP_ID是否正确

### Q3: 订单码支付失败？
**解决方案**：
1. 确认付款码格式正确（18位数字）
2. 检查商户是否有权限使用当面付功能
3. 验证签约状态

### Q4: 异步通知未收到？
**解决方案**：
1. 检查通知地址是否可访问
2. 确保服务器防火墙允许支付宝IP访问
3. 验证HTTPS配置

## 📋 配置检查清单

- [ ] 支付宝开放平台账号已注册
- [ ] 应用已创建并获得APP_ID
- [ ] RSA2密钥对已生成
- [ ] 应用公钥已上传到支付宝平台
- [ ] 支付宝公钥已获取
- [ ] 当面付产品已签约
- [ ] .env文件已正确配置
- [ ] 密钥格式已验证
- [ ] 回调地址已配置
- [ ] 沙箱环境测试通过
- [ ] 生产环境配置完成

## 🔒 安全建议

1. **密钥安全**：
   - 应用私钥严格保密
   - 定期更换密钥
   - 使用环境变量存储敏感信息

2. **网络安全**：
   - 使用HTTPS协议
   - 配置防火墙规则
   - 限制API访问频率

3. **监控告警**：
   - 监控支付异常
   - 设置告警机制
   - 记录详细日志

## 📞 技术支持

如有问题，请检查：
1. 支付宝开放平台文档
2. 系统日志文件
3. API响应错误信息
4. 网络连接状态
