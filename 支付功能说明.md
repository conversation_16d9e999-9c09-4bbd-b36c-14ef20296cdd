# FocuSee 支付功能说明

## 功能概述

FocuSee 系统已集成支付宝支付功能，支持两种支付方式：
1. **当面付（扫码支付）**: 生成二维码，用户使用支付宝扫码支付
2. **订单码支付**: 用户提供支付宝付款码，系统直接扣款

## 功能特性

### 支付方式
- ✅ 支付宝当面付（扫码支付）
- ✅ 支付宝订单码支付
- ✅ 支付状态实时查询
- ✅ 支付异步通知处理
- ✅ 订单管理和取消

### 安全特性
- ✅ RSA2签名验证
- ✅ 支付宝官方SDK集成
- ✅ 完整的支付日志记录
- ✅ 订单状态管理
- ✅ 支付超时处理

## 配置说明

### 1. 支付宝开放平台配置

1. **注册支付宝开放平台账号**
   - 访问：https://open.alipay.com/
   - 注册并完成企业认证

2. **创建应用**
   - 选择"网页&移动应用"
   - 填写应用信息
   - 获取 `APP_ID`

3. **配置密钥**
   - 生成RSA2密钥对
   - 上传应用公钥到支付宝平台
   - 获取支付宝公钥

4. **签约产品**
   - 当面付产品
   - 手机网站支付（可选）

### 2. 系统配置

在 `.env` 文件中配置以下参数：

```env
# 支付宝配置
ALIPAY_APP_ID=你的应用ID
ALIPAY_APP_PRIVATE_KEY=你的应用私钥
ALIPAY_PUBLIC_KEY=支付宝公钥
ALIPAY_NOTIFY_URL=https://yourdomain.com/api/payment/alipay/notify
ALIPAY_RETURN_URL=https://yourdomain.com/user/payment
```

### 3. 密钥格式说明

**应用私钥格式**（去掉头尾和换行符）：
```
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
```

**支付宝公钥格式**（去掉头尾和换行符）：
```
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
```

## 使用说明

### 1. 用户端操作

1. **访问支付页面**
   - 登录用户中心
   - 点击"支付中心"菜单

2. **选择产品**
   - 浏览可用产品列表
   - 点击选择要购买的产品

3. **选择支付方式**
   - 扫码支付：生成二维码，使用支付宝扫码
   - 订单码支付：输入支付宝付款码

4. **完成支付**
   - 扫码支付：扫码后在支付宝中确认支付
   - 订单码支付：系统自动扣款

5. **查看订单**
   - 在"我的订单"中查看支付状态
   - 支持订单取消（仅限待支付状态）

### 2. API接口

#### 创建当面付支付
```http
POST /api/payment/face-to-face
Content-Type: application/json

{
  "user_id": "用户ID",
  "product_id": 1,
  "timeout_minutes": 30
}
```

#### 创建订单码支付
```http
POST /api/payment/order-code
Content-Type: application/json

{
  "user_id": "用户ID",
  "product_id": 1,
  "auth_code": "支付宝付款码"
}
```

#### 查询支付状态
```http
POST /api/payment/query
Content-Type: application/json

{
  "order_no": "订单号"
}
```

#### 获取用户订单列表
```http
GET /api/payment/orders/{user_id}
```

## 数据库表结构

### payment_orders（支付订单表）
- `id`: 主键
- `order_no`: 订单号
- `user_id`: 用户ID
- `product_id`: 产品ID
- `payment_method`: 支付方式
- `amount`: 支付金额
- `status`: 支付状态
- `alipay_trade_no`: 支付宝交易号
- `alipay_qr_code`: 支付二维码
- `expire_time`: 过期时间
- `paid_at`: 支付时间

### payment_logs（支付日志表）
- `id`: 主键
- `payment_order_id`: 支付订单ID
- `log_type`: 日志类型
- `request_data`: 请求数据
- `response_data`: 响应数据
- `is_success`: 是否成功

## 支付状态说明

- `pending`: 待支付
- `paid`: 已支付
- `cancelled`: 已取消
- `expired`: 已过期
- `failed`: 支付失败
- `refunded`: 已退款

## 测试说明

### 沙箱环境测试

1. **配置沙箱环境**
   - 在配置中设置 `DEBUG=True`
   - 使用沙箱环境的APP_ID和密钥

2. **测试账号**
   - 支付宝提供沙箱测试账号
   - 可以模拟真实支付流程

3. **测试流程**
   - 创建测试产品
   - 使用测试用户ID进行支付
   - 验证支付流程和状态更新

## 常见问题

### Q: 支付宝通知验签失败？
A: 检查支付宝公钥配置是否正确，确保格式正确（去掉头尾和换行符）

### Q: 二维码生成失败？
A: 检查应用私钥配置，确保已在支付宝平台上传对应的公钥

### Q: 订单码支付失败？
A: 确认付款码格式正确，检查商户是否有权限使用当面付功能

### Q: 异步通知未收到？
A: 检查通知地址是否可访问，确保服务器防火墙允许支付宝IP访问

## 安全建议

1. **生产环境配置**
   - 使用HTTPS协议
   - 设置强密码和密钥
   - 定期更换密钥

2. **监控和日志**
   - 监控支付异常
   - 记录详细日志
   - 设置告警机制

3. **数据保护**
   - 敏感数据加密存储
   - 定期备份数据
   - 限制API访问频率

## 技术支持

如有问题，请检查：
1. 系统日志 (`logs/app.log`)
2. 支付日志 (`payment_logs` 表)
3. 支付宝开放平台文档
4. API响应错误信息
