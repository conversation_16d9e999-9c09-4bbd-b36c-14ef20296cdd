{% extends "user/base_new.html" %}

{% block title %}API使用统计 - FocuSee 用户中心{% endblock %}

{% block content %}
<div class="page-title">API使用统计</div>

<div class="content-container">
    <!-- 统计概览 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
            <el-card class="stats-card">
                <div class="stats-content">
                    <div class="stats-icon">
                        <el-icon color="#409eff" size="24"><DataAnalysis /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{{ "{{ apiStats.totalCalls }}" }}</div>
                        <div class="stats-label">总调用次数</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card class="stats-card">
                <div class="stats-content">
                    <div class="stats-icon">
                        <el-icon color="#67c23a" size="24"><CircleCheck /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{{ "{{ apiStats.successCalls }}" }}</div>
                        <div class="stats-label">成功调用</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card class="stats-card">
                <div class="stats-content">
                    <div class="stats-icon">
                        <el-icon color="#f56c6c" size="24"><CircleClose /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{{ "{{ apiStats.failedCalls }}" }}</div>
                        <div class="stats-label">失败调用</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card class="stats-card">
                <div class="stats-content">
                    <div class="stats-icon">
                        <el-icon color="#e6a23c" size="24"><Timer /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{{ "{{ apiStats.avgResponseTime }}" }}ms</div>
                        <div class="stats-label">平均响应时间</div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>

    <!-- 时间范围选择 -->
    <el-card style="margin-bottom: 20px;">
        <el-row :gutter="20" align="middle">
            <el-col :span="8">
                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    @change="handleDateChange">
                </el-date-picker>
            </el-col>
            <el-col :span="6">
                <el-select v-model="selectedLicense" placeholder="选择授权" clearable @change="loadApiUsage">
                    <el-option label="全部授权" value=""></el-option>
                    <el-option 
                        v-for="license in licenses" 
                        :key="license.id" 
                        :label="license.license_code" 
                        :value="license.id">
                    </el-option>
                </el-select>
            </el-col>
            <el-col :span="6">
                <el-select v-model="selectedProduct" placeholder="选择产品" clearable @change="loadApiUsage">
                    <el-option label="全部产品" value=""></el-option>
                    <el-option 
                        v-for="product in products" 
                        :key="product.id" 
                        :label="product.name" 
                        :value="product.id">
                    </el-option>
                </el-select>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="refreshData">
                    <el-icon><Refresh /></el-icon>
                    刷新
                </el-button>
            </el-col>
        </el-row>
    </el-card>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="12">
            <el-card>
                <template #header>
                    <span>API调用趋势</span>
                </template>
                <div id="callTrendChart" style="height: 300px;"></div>
            </el-card>
        </el-col>
        <el-col :span="12">
            <el-card>
                <template #header>
                    <span>成功率统计</span>
                </template>
                <div id="successRateChart" style="height: 300px;"></div>
            </el-card>
        </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="12">
            <el-card>
                <template #header>
                    <span>响应时间分布</span>
                </template>
                <div id="responseTimeChart" style="height: 300px;"></div>
            </el-card>
        </el-col>
        <el-col :span="12">
            <el-card>
                <template #header>
                    <span>API接口使用排行</span>
                </template>
                <div id="apiRankingChart" style="height: 300px;"></div>
            </el-card>
        </el-col>
    </el-row>

    <!-- 详细日志 -->
    <el-card>
        <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>API调用日志</span>
                <el-button type="primary" size="small" @click="exportLogs">
                    <el-icon><Download /></el-icon>
                    导出日志
                </el-button>
            </div>
        </template>

        <!-- 搜索筛选 -->
        <el-row :gutter="20" style="margin-bottom: 15px;">
            <el-col :span="6">
                <el-input
                    v-model="logSearch.keyword"
                    placeholder="搜索API路径或IP"
                    clearable
                    @input="handleLogSearch">
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
            </el-col>
            <el-col :span="4">
                <el-select v-model="logSearch.status" placeholder="状态" clearable @change="handleLogSearch">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="成功" value="success"></el-option>
                    <el-option label="失败" value="error"></el-option>
                </el-select>
            </el-col>
            <el-col :span="4">
                <el-select v-model="logSearch.method" placeholder="请求方法" clearable @change="handleLogSearch">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="GET" value="GET"></el-option>
                    <el-option label="POST" value="POST"></el-option>
                    <el-option label="PUT" value="PUT"></el-option>
                    <el-option label="DELETE" value="DELETE"></el-option>
                </el-select>
            </el-col>
        </el-row>

        <el-table 
            :data="apiLogs" 
            v-loading="logsLoading"
            style="width: 100%"
            @sort-change="handleLogSortChange">
            
            <el-table-column prop="timestamp" label="时间" width="160" sortable="custom">
                <template #default="scope">
                    {{ "{{ formatDate(scope.row.timestamp) }}" }}
                </template>
            </el-table-column>
            
            <el-table-column prop="method" label="方法" width="80">
                <template #default="scope">
                    <el-tag 
                        :type="getMethodType(scope.row.method)"
                        size="small">
                        {{ "{{ scope.row.method }}" }}
                    </el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="api_path" label="API路径" min-width="200" show-overflow-tooltip>
                <template #default="scope">
                    <el-text style="font-family: monospace;">{{ "{{ scope.row.api_path }}" }}</el-text>
                </template>
            </el-table-column>
            
            <el-table-column prop="status_code" label="状态码" width="100">
                <template #default="scope">
                    <el-tag 
                        :type="getStatusType(scope.row.status_code)"
                        size="small">
                        {{ "{{ scope.row.status_code }}" }}
                    </el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="response_time" label="响应时间" width="100" sortable="custom">
                <template #default="scope">
                    <span :class="getResponseTimeClass(scope.row.response_time)">
                        {{ "{{ scope.row.response_time }}" }}ms
                    </span>
                </template>
            </el-table-column>
            
            <el-table-column prop="ip_address" label="IP地址" width="130">
                <template #default="scope">
                    <el-text style="font-family: monospace;">{{ "{{ scope.row.ip_address }}" }}</el-text>
                </template>
            </el-table-column>
            
            <el-table-column prop="license_code" label="授权码" width="150" show-overflow-tooltip>
                <template #default="scope">
                    <el-text v-if="scope.row.license_code" style="font-family: monospace;">
                        {{ "{{ scope.row.license_code }}" }}
                    </el-text>
                    <el-text v-else type="info">-</el-text>
                </template>
            </el-table-column>
            
            <el-table-column label="操作" width="100" fixed="right">
                <template #default="scope">
                    <el-button 
                        type="primary" 
                        size="small" 
                        @click="viewLogDetail(scope.row)"
                        link>
                        详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div style="margin-top: 20px; text-align: center;">
            <el-pagination
                v-model:current-page="logPagination.page"
                v-model:page-size="logPagination.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="logPagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleLogSizeChange"
                @current-change="handleLogPageChange">
            </el-pagination>
        </div>
    </el-card>
</div>

<!-- 日志详情对话框 -->
<el-dialog v-model="showLogDetailDialog" title="API调用详情" width="800px">
    <div v-if="selectedLog">
        <el-descriptions :column="2" border>
            <el-descriptions-item label="时间">
                {{ "{{ formatDate(selectedLog.timestamp) }}" }}
            </el-descriptions-item>
            <el-descriptions-item label="请求方法">
                <el-tag :type="getMethodType(selectedLog.method)">{{ "{{ selectedLog.method }}" }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="API路径" :span="2">
                <el-text style="font-family: monospace;">{{ "{{ selectedLog.api_path }}" }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="状态码">
                <el-tag :type="getStatusType(selectedLog.status_code)">{{ "{{ selectedLog.status_code }}" }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="响应时间">
                {{ "{{ selectedLog.response_time }}" }}ms
            </el-descriptions-item>
            <el-descriptions-item label="IP地址">
                {{ "{{ selectedLog.ip_address }}" }}
            </el-descriptions-item>
            <el-descriptions-item label="用户代理" :span="2">
                <el-text size="small">{{ "{{ selectedLog.user_agent || '-' }}" }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="授权码" :span="2">
                <el-text v-if="selectedLog.license_code" style="font-family: monospace;">
                    {{ "{{ selectedLog.license_code }}" }}
                </el-text>
                <el-text v-else type="info">未使用授权</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="请求参数" :span="2">
                <pre v-if="selectedLog.request_params">{{ "{{ JSON.stringify(selectedLog.request_params, null, 2) }}" }}</pre>
                <span v-else>无</span>
            </el-descriptions-item>
            <el-descriptions-item label="响应数据" :span="2">
                <pre v-if="selectedLog.response_data">{{ "{{ JSON.stringify(selectedLog.response_data, null, 2) }}" }}</pre>
                <span v-else>无</span>
            </el-descriptions-item>
            <el-descriptions-item label="错误信息" :span="2" v-if="selectedLog.error_message">
                <el-text type="danger">{{ "{{ selectedLog.error_message }}" }}</el-text>
            </el-descriptions-item>
        </el-descriptions>
    </div>
</el-dialog>

<style scoped>
.content-container {
    max-width: 1400px;
}

.stats-card {
    height: 100px;
}

.stats-content {
    display: flex;
    align-items: center;
    height: 100%;
}

.stats-icon {
    margin-right: 15px;
}

.stats-info {
    flex: 1;
}

.stats-number {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
    line-height: 1;
}

.stats-label {
    font-size: 14px;
    color: #909399;
    margin-top: 5px;
}

.response-time-fast {
    color: #67c23a;
}

.response-time-normal {
    color: #e6a23c;
}

.response-time-slow {
    color: #f56c6c;
}

pre {
    background-color: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
}
</style>
{% endblock %}

{% block data %}
apiStats: {
    totalCalls: 0,
    successCalls: 0,
    failedCalls: 0,
    avgResponseTime: 0
},
dateRange: [],
selectedLicense: '',
selectedProduct: '',
licenses: [],
products: [],
apiLogs: [],
logsLoading: false,
logSearch: {
    keyword: '',
    status: '',
    method: ''
},
logPagination: {
    page: 1,
    size: 20,
    total: 0
},
showLogDetailDialog: false,
selectedLog: null,
charts: {}
{% endblock %}

{% block methods %}
async loadApiStats() {
    try {
        const params = new URLSearchParams();
        if (this.dateRange && this.dateRange.length === 2) {
            params.append('start_date', this.dateRange[0]);
            params.append('end_date', this.dateRange[1]);
        }
        if (this.selectedLicense) params.append('license_id', this.selectedLicense);
        if (this.selectedProduct) params.append('product_id', this.selectedProduct);
        
        const response = await fetch(`/api/user-auth/api-usage/stats?${params}`);
        if (response.ok) {
            this.apiStats = await response.json();
        }
    } catch (error) {
        console.error('加载API统计失败:', error);
    }
},

async loadApiUsage() {
    await this.loadApiStats();
    await this.loadApiLogs();
    this.loadCharts();
},

async loadApiLogs() {
    this.logsLoading = true;
    try {
        const params = new URLSearchParams({
            page: this.logPagination.page,
            size: this.logPagination.size,
            ...this.logSearch
        });
        
        if (this.dateRange && this.dateRange.length === 2) {
            params.append('start_date', this.dateRange[0]);
            params.append('end_date', this.dateRange[1]);
        }
        if (this.selectedLicense) params.append('license_id', this.selectedLicense);
        if (this.selectedProduct) params.append('product_id', this.selectedProduct);
        
        const response = await fetch(`/api/user-auth/api-usage/logs?${params}`);
        if (response.ok) {
            const data = await response.json();
            this.apiLogs = data.logs || [];
            this.logPagination.total = data.total || 0;
        }
    } catch (error) {
        console.error('加载API日志失败:', error);
        this.$message.error('加载API日志失败');
    } finally {
        this.logsLoading = false;
    }
},

async loadLicenses() {
    try {
        const response = await fetch('/api/user-auth/licenses');
        if (response.ok) {
            const data = await response.json();
            this.licenses = data.licenses || [];
        }
    } catch (error) {
        console.error('加载授权列表失败:', error);
    }
},

async loadProducts() {
    try {
        const response = await fetch('/api/products');
        if (response.ok) {
            this.products = await response.json();
        }
    } catch (error) {
        console.error('加载产品列表失败:', error);
    }
},

handleDateChange() {
    this.loadApiUsage();
},

handleLogSearch() {
    this.logPagination.page = 1;
    this.loadApiLogs();
},

handleLogSortChange(sort) {
    // 处理排序
    this.loadApiLogs();
},

handleLogPageChange(page) {
    this.logPagination.page = page;
    this.loadApiLogs();
},

handleLogSizeChange(size) {
    this.logPagination.size = size;
    this.logPagination.page = 1;
    this.loadApiLogs();
},

refreshData() {
    this.loadApiUsage();
},

viewLogDetail(log) {
    this.selectedLog = log;
    this.showLogDetailDialog = true;
},

exportLogs() {
    this.$message.info('导出功能开发中...');
},

loadCharts() {
    // 这里可以集成 ECharts 或其他图表库
    // 由于模板限制，这里只是占位
    console.log('加载图表数据...');
},

getMethodType(method) {
    const types = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger'
    };
    return types[method] || 'info';
},

getStatusType(statusCode) {
    if (statusCode >= 200 && statusCode < 300) return 'success';
    if (statusCode >= 400 && statusCode < 500) return 'warning';
    if (statusCode >= 500) return 'danger';
    return 'info';
},

getResponseTimeClass(responseTime) {
    if (responseTime < 100) return 'response-time-fast';
    if (responseTime < 500) return 'response-time-normal';
    return 'response-time-slow';
},

formatDate(dateStr) {
    if (!dateStr) return '';
    return new Date(dateStr).toLocaleString('zh-CN');
}
{% endblock %}

{% block mounted %}
// 设置默认日期范围为最近7天
const endDate = new Date();
const startDate = new Date();
startDate.setDate(startDate.getDate() - 7);
this.dateRange = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
];

this.loadLicenses();
this.loadProducts();
this.loadApiUsage();
{% endblock %}
