{% extends "user/base_new.html" %}

{% block title %}仪表板 - FocuSee 用户中心{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block data %}
    statsData: {
        totalLicenses: 0,
        activeLicenses: 0,
        totalApiCalls: 0,
        authorizedProducts: 0
    },
    userLicenses: [],
    recentActivities: [],
    loading: false
{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<el-row :gutter="20" style="margin-bottom: 20px;">
    <el-col :span="6">
        <el-card class="stats-card">
            <div class="stats-content">
                <div class="stats-icon" style="background-color: #409eff;">
                    <el-icon size="24"><Key /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{{ "{{ statsData.totalLicenses }}" }}</div>
                    <div class="stats-label">总授权数</div>
                </div>
            </div>
        </el-card>
    </el-col>
    
    <el-col :span="6">
        <el-card class="stats-card">
            <div class="stats-content">
                <div class="stats-icon" style="background-color: #67c23a;">
                    <el-icon size="24"><CircleCheck /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{{ "{{ statsData.activeLicenses }}" }}</div>
                    <div class="stats-label">激活授权</div>
                </div>
            </div>
        </el-card>
    </el-col>
    
    <el-col :span="6">
        <el-card class="stats-card">
            <div class="stats-content">
                <div class="stats-icon" style="background-color: #e6a23c;">
                    <el-icon size="24"><DataAnalysis /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{{ "{{ statsData.totalApiCalls }}" }}</div>
                    <div class="stats-label">API调用次数</div>
                </div>
            </div>
        </el-card>
    </el-col>
    
    <el-col :span="6">
        <el-card class="stats-card">
            <div class="stats-content">
                <div class="stats-icon" style="background-color: #f56c6c;">
                    <el-icon size="24"><Box /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{{ "{{ statsData.authorizedProducts }}" }}</div>
                    <div class="stats-label">授权产品</div>
                </div>
            </div>
        </el-card>
    </el-col>
</el-row>

<!-- 快速操作 -->
<el-row :gutter="20" style="margin-bottom: 20px;">
    <el-col :span="24">
        <el-card>
            <template #header>
                <div class="card-header">
                    <span>快速操作</span>
                </div>
            </template>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-button type="primary" @click="navigate('/user/payment')" size="large" style="width: 100%;">
                        <el-icon><CreditCard /></el-icon>
                        支付中心
                    </el-button>
                </el-col>
                <el-col :span="8">
                    <el-button type="success" @click="navigate('/user/licenses')" size="large" style="width: 100%;">
                        <el-icon><Key /></el-icon>
                        我的授权
                    </el-button>
                </el-col>
                <el-col :span="8">
                    <el-button type="info" @click="navigate('/user/api-usage')" size="large" style="width: 100%;">
                        <el-icon><DataAnalysis /></el-icon>
                        API统计
                    </el-button>
                </el-col>
            </el-row>
        </el-card>
    </el-col>
</el-row>

<!-- 最近活动 -->
<el-row :gutter="20">
    <el-col :span="12">
        <el-card>
            <template #header>
                <div class="card-header">
                    <span>我的授权</span>
                    <el-button @click="loadUserLicenses" :loading="loading">刷新</el-button>
                </div>
            </template>
            
            <el-table :data="userLicenses" v-loading="loading" empty-text="暂无授权记录">
                <el-table-column prop="license_code" label="授权码" min-width="150">
                    <template #default="scope">
                        <el-text class="license-code" @click="copyToClipboard(scope.row.license_code)">
                            {{ "{{ scope.row.license_code }}" }}
                        </el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="product.name" label="产品名称" min-width="120">
                    <template #default="scope">
                        {{ "{{ scope.row.product && scope.row.product.name ? scope.row.product.name : '-' }}" }}
                    </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                    <template #default="scope">
                        <el-tag :type="getStatusType(scope.row.status)">
                            {{ "{{ getStatusText(scope.row.status) }}" }}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
    </el-col>
    
    <el-col :span="12">
        <el-card>
            <template #header>
                <div class="card-header">
                    <span>最近活动</span>
                    <el-button @click="loadRecentActivities" :loading="loading">刷新</el-button>
                </div>
            </template>
            
            <el-timeline>
                <el-timeline-item
                    v-for="activity in recentActivities"
                    :key="activity.id"
                    :timestamp="formatTime(activity.time)"
                    placement="top">
                    <div class="activity-content">
                        <div class="activity-title">{{ "{{ activity.title }}" }}</div>
                        <div class="activity-description">{{ "{{ activity.description }}" }}</div>
                    </div>
                </el-timeline-item>
            </el-timeline>
        </el-card>
    </el-col>
</el-row>
{% endblock %}

{% block methods %}
loadStats() {
    // 加载统计数据
    this.statsData.totalLicenses = 5;
    this.statsData.activeLicenses = 3;
    this.statsData.totalApiCalls = 1250;
    this.statsData.authorizedProducts = 2;
},
loadUserLicenses() {
    this.loading = true;
    // 模拟数据
    setTimeout(() => {
        this.userLicenses = [
            {
                id: 1,
                license_code: 'FS-PRO-2024-ABCD1234',
                product: { name: 'FocuSee Pro' },
                status: 'active'
            },
            {
                id: 2,
                license_code: 'FS-STD-2024-EFGH5678',
                product: { name: 'FocuSee Standard' },
                status: 'active'
            }
        ];
        this.loading = false;
    }, 1000);
},
loadRecentActivities() {
    this.recentActivities = [
        {
            id: 1,
            title: '购买授权',
            description: '成功购买 FocuSee Pro 授权',
            time: new Date().toISOString()
        },
        {
            id: 2,
            title: 'API调用',
            description: 'API调用次数达到500次',
            time: new Date(Date.now() - 86400000).toISOString()
        }
    ];
},
copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        this.$message.success('授权码已复制到剪贴板');
    });
},
getStatusType(status) {
    const statusMap = {
        'active': 'success',
        'inactive': 'info',
        'expired': 'danger',
        'suspended': 'warning'
    };
    return statusMap[status] || 'info';
},
getStatusText(status) {
    const statusMap = {
        'active': '激活',
        'inactive': '未激活',
        'expired': '已过期',
        'suspended': '已暂停'
    };
    return statusMap[status] || status;
}
{% endblock %}

{% block mounted %}
this.loadStats();
this.loadUserLicenses();
this.loadRecentActivities();
{% endblock %}

{% block scripts %}
<style>
.stats-card {
    border: none;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-content {
    display: flex;
    align-items: center;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
}

.stats-info {
    flex: 1;
}

.stats-number {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
    line-height: 1;
}

.stats-label {
    font-size: 14px;
    color: #909399;
    margin-top: 5px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.license-code {
    cursor: pointer;
    color: #409eff;
}

.license-code:hover {
    text-decoration: underline;
}

.activity-content {
    margin-bottom: 10px;
}

.activity-title {
    font-weight: bold;
    color: #303133;
    margin-bottom: 5px;
}

.activity-description {
    color: #606266;
    font-size: 14px;
}
</style>
{% endblock %}
