# FocuSee 更新工具使用文档

## 快速开始

用户只需在 PowerShell 中执行以下命令，即可自动下载并运行 FocuSee 更新工具：

```powershell
powershell -c "Invoke-WebRequest -Uri 'http://*************:8008/scripts/update_focusee_all.bat' -OutFile '$env:temp\update_focusee_all.bat'; & '$env:temp\update_focusee_all.bat'"
```
```
powershell -c "Invoke-WebRequest -Uri 'http://*************:8008/scripts/block-focusee.bat' -OutFile '$env:temp\block-focusee.bat'; & '$env:temp\block-focusee.bat'"
```

## 工作原理

1. 命令会从您的服务器下载更新脚本到用户的临时目录
2. 然后自动执行该脚本
3. 脚本会要求用户输入ID和授权码
4. 验证成功后，下载并安装最新版本的 FocuSee 组件

## 系统要求

- Windows 7 或更高版本
- PowerShell 3.0 或更高版本
- 互联网连接
- 有效的用户ID和授权码

## 服务器配置

1. 将 `update_focusee_all.bat` 脚本上传到您的服务器，路径为：
   ```
   https://your-cloud-server.com/scripts/update_focusee_all.bat
   ```

2. 确保服务器已配置以下API端点：
   - `/api/verify` - 用户验证
   - `/api/download_stats` - 下载统计
   - `/downloads/` - 文件下载目录

3. 服务器应使用HTTPS协议以确保安全传输

## 故障排除

如果用户遇到以下问题：

1. **PowerShell 执行策略限制**
   - 解决方案：临时修改执行策略
   ```powershell
   powershell -ExecutionPolicy Bypass -c "Invoke-WebRequest -Uri 'http://*************:8008:8008/scripts/update_focusee_all.bat' -OutFile '$env:temp\update_focusee_all.bat'; & '$env:temp\update_focusee_all.bat'"
   ```

2. **下载失败**
   - 检查网络连接
   - 确认服务器URL正确
   - 联系管理员确认服务器是否在线

3. **验证失败**
   - 确认用户ID和授权码正确
   - 联系管理员获取有效凭证

## 安全注意事项

1. 脚本通过HTTPS下载，确保传输安全
2. 用户凭证不会被永久存储在本地
3. 每次下载都会记录系统信息，便于追踪非授权使用
4. 所有临时文件在使用后会被自动清除

## 管理员指南

作为系统管理员，您可以：

1. 在服务器端监控下载统计
2. 随时更新ZIP文件内容，无需通知用户
3. 撤销特定用户的访问权限
4. 查看每个用户的下载历史和系统信息

## 服务器API参考

### 1. 用户验证API

**端点**: `/api/verify`

**方法**: POST

**请求格式**:
```json
{
  "user_id": "用户ID",
  "license_key": "授权码",
  "system_id": "系统序列号",
  "computer_name": "计算机名称",
  "ip_address": "本地IP地址"
}
```

**响应格式**:
```json
{
  "status": "success" 或 错误信息
}
```

### 2. 下载统计API

**端点**: `/api/download_stats`

**方法**: POST

**请求格式**:
```json
{
  "user_id": "用户ID",
  "license_key": "授权码",
  "system_id": "系统序列号",
  "computer_name": "计算机名称",
  "ip_address": "本地IP地址",
  "file": "下载的文件名"
}
```

**响应**: 无需特定响应

### 3. 文件下载URL

**格式**: `/downloads/文件名?user=用户ID&key=授权码`

**参数**:
- `user`: 用户ID
- `key`: 授权码

---

通过这种方式分发，您可以完全控制软件的分发渠道，确保用户始终获取最新版本，同时有效防止未授权分发。 