{% extends "admin/base.html" %}

{% block title %}个人设置 - FocuSee 管理系统{% endblock %}
{% block page_title %}个人设置{% endblock %}

{% block data %}
    admin: {
        id: {{ admin.id }},
        username: "{{ admin.username }}",
        email: "{{ admin.email or '' }}",
        phone: "{{ admin.phone or '' }}",
        full_name: "{{ admin.full_name or '' }}",
        is_active: {{ 'true' if admin.is_active else 'false' }},
        last_login_at: "{{ admin.last_login_at.isoformat() if admin.last_login_at else '' }}",
        created_at: "{{ admin.created_at.isoformat() if admin.created_at else '' }}"
    },
    profileForm: {
        username: "{{ admin.username }}",
        email: "{{ admin.email or '' }}",
        phone: "{{ admin.phone or '' }}",
        full_name: "{{ admin.full_name or '' }}"
    },
    passwordForm: {
        current_password: '',
        new_password: '',
        confirm_password: ''
    },
    rules: {
        username: [
            { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        email: [
            { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        current_password: [
            { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        new_password: [
            { required: true, message: '请输入新密码', trigger: 'blur' },
            { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        confirm_password: [
            { required: true, message: '请确认新密码', trigger: 'blur' },
            { validator: this.validateConfirmPassword, trigger: 'blur' }
        ]
    },
    loading: false,
    activeTab: 'profile'
{% endblock %}

{% block content %}
<div class="content-card">
    <!-- 个人信息卡片 -->
    <div style="margin-bottom: 20px; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
        <el-row :gutter="20" style="align-items: center;">
            <el-col :span="4">
                <el-avatar :size="80" style="background-color: #409EFF;">
                    {% raw %}{{ admin.full_name ? admin.full_name.charAt(0).toUpperCase() : admin.username.charAt(0).toUpperCase() }}{% endraw %}
                </el-avatar>
            </el-col>
            <el-col :span="20">
                <h3 style="margin: 0 0 10px 0; color: #303133;">
                    {% raw %}{{ admin.full_name || admin.username }}{% endraw %}
                </h3>
                <p style="margin: 0 0 5px 0; color: #606266;">
                    <el-icon><User /></el-icon>
                    用户名: {% raw %}{{ admin.username }}{% endraw %}
                </p>
                <p style="margin: 0 0 5px 0; color: #606266;">
                    <el-icon><Message /></el-icon>
                    邮箱: {% raw %}{{ admin.email || '未设置' }}{% endraw %}
                </p>
                <p style="margin: 0 0 5px 0; color: #606266;">
                    <el-icon><Phone /></el-icon>
                    电话: {% raw %}{{ admin.phone || '未设置' }}{% endraw %}
                </p>
                <p style="margin: 0; color: #909399; font-size: 12px;">
                    最后登录: {% raw %}{{ admin.last_login_at ? formatTime(admin.last_login_at) : '从未登录' }}{% endraw %}
                </p>
            </el-col>
        </el-row>
    </div>

    <!-- 设置选项卡 -->
    <el-tabs v-model="activeTab" type="card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="profile">
            <el-form ref="profileFormRef" :model="profileForm" :rules="rules" label-width="100px" style="max-width: 600px;">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="profileForm.username" disabled>
                        <template #suffix>
                            <el-tooltip content="用户名不可修改" placement="top">
                                <el-icon><InfoFilled /></el-icon>
                            </el-tooltip>
                        </template>
                    </el-input>
                </el-form-item>
                
                <el-form-item label="姓名" prop="full_name">
                    <el-input v-model="profileForm.full_name" placeholder="请输入真实姓名"></el-input>
                </el-form-item>
                
                <el-form-item label="邮箱" prop="email">
                    <el-input v-model="profileForm.email" placeholder="请输入邮箱地址"></el-input>
                </el-form-item>
                
                <el-form-item label="电话" prop="phone">
                    <el-input v-model="profileForm.phone" placeholder="请输入电话号码"></el-input>
                </el-form-item>
                
                <el-form-item>
                    <el-button type="primary" @click="updateProfile" :loading="loading">
                        <el-icon><Check /></el-icon>
                        保存修改
                    </el-button>
                    <el-button @click="resetProfileForm">
                        <el-icon><Refresh /></el-icon>
                        重置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-tab-pane>

        <!-- 修改密码 -->
        <el-tab-pane label="修改密码" name="password">
            <el-form ref="passwordFormRef" :model="passwordForm" :rules="rules" label-width="100px" style="max-width: 600px;">
                <el-form-item label="当前密码" prop="current_password">
                    <el-input v-model="passwordForm.current_password" type="password" show-password placeholder="请输入当前密码"></el-input>
                </el-form-item>
                
                <el-form-item label="新密码" prop="new_password">
                    <el-input v-model="passwordForm.new_password" type="password" show-password placeholder="请输入新密码"></el-input>
                </el-form-item>
                
                <el-form-item label="确认密码" prop="confirm_password">
                    <el-input v-model="passwordForm.confirm_password" type="password" show-password placeholder="请再次输入新密码"></el-input>
                </el-form-item>
                
                <el-form-item>
                    <el-button type="primary" @click="updatePassword" :loading="loading">
                        <el-icon><Lock /></el-icon>
                        修改密码
                    </el-button>
                    <el-button @click="resetPasswordForm">
                        <el-icon><Refresh /></el-icon>
                        重置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-tab-pane>

        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
            <div style="max-width: 600px;">
                <el-alert
                    title="安全提醒"
                    type="info"
                    description="为了保护您的账户安全，建议定期修改密码，并启用相关安全功能。"
                    show-icon
                    :closable="false"
                    style="margin-bottom: 20px;">
                </el-alert>
                
                <el-descriptions title="账户安全信息" :column="1" border>
                    <el-descriptions-item label="账户状态">
                        <el-tag :type="admin.is_active ? 'success' : 'danger'">
                            {% raw %}{{ admin.is_active ? '正常' : '已禁用' }}{% endraw %}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="创建时间">
                        {% raw %}{{ formatTime(admin.created_at) }}{% endraw %}
                    </el-descriptions-item>
                    <el-descriptions-item label="最后登录">
                        {% raw %}{{ admin.last_login_at ? formatTime(admin.last_login_at) : '从未登录' }}{% endraw %}
                    </el-descriptions-item>
                    <el-descriptions-item label="登录IP">
                        {% raw %}{{ admin.last_login_ip || '未记录' }}{% endraw %}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </el-tab-pane>
    </el-tabs>
</div>
{% endblock %}

{% block methods %}
validateConfirmPassword(rule, value, callback) {
    if (value !== this.passwordForm.new_password) {
        callback(new Error('两次输入的密码不一致'));
    } else {
        callback();
    }
},
updateProfile() {
    this.$refs.profileFormRef.validate((valid) => {
        if (valid) {
            this.loading = true;
            
            fetch('/api/admin/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.profileForm)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success || data.id) {
                    this.$message.success('个人信息更新成功');
                    // 更新页面显示的数据
                    Object.assign(this.admin, this.profileForm);
                } else {
                    this.$message.error(data.message || '更新失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.$message.error('更新失败，请重试');
            })
            .finally(() => {
                this.loading = false;
            });
        }
    });
},
updatePassword() {
    this.$refs.passwordFormRef.validate((valid) => {
        if (valid) {
            this.loading = true;
            
            fetch('/api/admin/password', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.passwordForm)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.$message.success('密码修改成功');
                    this.resetPasswordForm();
                } else {
                    this.$message.error(data.message || '密码修改失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.$message.error('密码修改失败，请重试');
            })
            .finally(() => {
                this.loading = false;
            });
        }
    });
},
resetProfileForm() {
    this.profileForm = {
        username: this.admin.username,
        email: this.admin.email,
        phone: this.admin.phone,
        full_name: this.admin.full_name
    };
    if (this.$refs.profileFormRef) {
        this.$refs.profileFormRef.resetFields();
    }
},
resetPasswordForm() {
    this.passwordForm = {
        current_password: '',
        new_password: '',
        confirm_password: ''
    };
    if (this.$refs.passwordFormRef) {
        this.$refs.passwordFormRef.resetFields();
    }
}
{% endblock %}
