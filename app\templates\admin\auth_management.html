{% extends "admin/base.html" %}


{% block title %}授权管理 - FocuSee 管理系统{% endblock %}
{% block page_title %}授权管理{% endblock %}

{% block data %}
    // 授权验证测试数据
    testForms: {
        order: {
            order_number: '',
            user_id: '',
            product_code: ''
        },
        license: {
            license_code: '',
            user_id: '',
            product_code: '',
            consume_quota: false
        },
        product: {
            user_id: '',
            product_code: ''
        },
        mixed: {
            user_id: '',
            license_code: '',
            order_number: '',
            product_code: '',
            consume_quota: false
        }
    },
    testResults: {},
    
    // 授权日志数据
    authLogs: [],
    logFilters: {
        action: '',
        result: '',
        user_id: '',
        order_number: '',
        date_range: []
    },
    

    
    // 统计数据
    authStats: {
        total_verifications: 0,
        success_rate: 0,
        quota_usage: 0,
        active_licenses: 0
    },
    
    loading: false,
    activeTab: 'test',
    
    // 分页数据
    pagination: {
        logs: { current: 1, size: 20, total: 0 }
    }
{% endblock %}

{% block content %}
<div class="auth-management">
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
            <el-card class="stats-card modern-card">
                <div class="stats-content">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <el-icon size="24"><Key /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{% raw %}{{ authStats.active_licenses }}{% endraw %}</div>
                        <div class="stats-label">活跃授权码</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card class="stats-card modern-card">
                <div class="stats-content">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                        <el-icon size="24"><DataAnalysis /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{% raw %}{{ authStats.total_verifications }}{% endraw %}</div>
                        <div class="stats-label">总验证次数</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card class="stats-card modern-card">
                <div class="stats-content">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        <el-icon size="24"><SuccessFilled /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{% raw %}{{ authStats.success_rate }}%{% endraw %}</div>
                        <div class="stats-label">验证成功率</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card class="stats-card modern-card">
                <div class="stats-content">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                        <el-icon size="24"><TrendCharts /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{% raw %}{{ authStats.quota_usage }}%{% endraw %}</div>
                        <div class="stats-label">配额使用率</div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-card class="content-card">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <!-- 授权验证测试 -->
            <el-tab-pane label="授权验证测试" name="test">
                <div class="test-section">
                    <el-row :gutter="20">
                        <!-- 订单号验证 -->
                        <el-col :span="12">
                            <el-card class="test-card">
                                <template #header>
                                    <span>订单号验证</span>
                                </template>
                                <el-form :model="testForms.order" label-width="100px">
                                    <el-form-item label="订单号">
                                        <el-input v-model="testForms.order.order_number" placeholder="请输入订单号"></el-input>
                                    </el-form-item>
                                    <el-form-item label="用户ID">
                                        <el-input v-model="testForms.order.user_id" placeholder="请输入用户ID"></el-input>
                                    </el-form-item>
                                    <el-form-item label="产品代码">
                                        <el-input v-model="testForms.order.product_code" placeholder="可选，产品代码"></el-input>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="testOrderVerify" :loading="loading">
                                            验证订单
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                                <div v-if="testResults.order" class="test-result">
                                    <el-alert 
                                        :type="testResults.order.success ? 'success' : 'error'"
                                        :title="testResults.order.message"
                                        show-icon
                                        :closable="false">
                                        <template #default>
                                            <pre>{% raw %}{{ JSON.stringify(testResults.order.data, null, 2) }}{% endraw %}</pre>
                                        </template>
                                    </el-alert>
                                </div>
                            </el-card>
                        </el-col>

                        <!-- 授权码验证 -->
                        <el-col :span="12">
                            <el-card class="test-card">
                                <template #header>
                                    <span>授权码验证</span>
                                </template>
                                <el-form :model="testForms.license" label-width="100px">
                                    <el-form-item label="授权码">
                                        <el-input v-model="testForms.license.license_code" placeholder="请输入授权码"></el-input>
                                    </el-form-item>
                                    <el-form-item label="用户ID">
                                        <el-input v-model="testForms.license.user_id" placeholder="请输入用户ID"></el-input>
                                    </el-form-item>
                                    <el-form-item label="产品代码">
                                        <el-input v-model="testForms.license.product_code" placeholder="可选，产品代码"></el-input>
                                    </el-form-item>
                                    <el-form-item label="消费配额">
                                        <el-switch v-model="testForms.license.consume_quota"></el-switch>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="testLicenseVerify" :loading="loading">
                                            验证授权码
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                                <div v-if="testResults.license" class="test-result">
                                    <el-alert 
                                        :type="testResults.license.success ? 'success' : 'error'"
                                        :title="testResults.license.message"
                                        show-icon
                                        :closable="false">
                                        <template #default>
                                            <pre>{% raw %}{{ JSON.stringify(testResults.license.data, null, 2) }}{% endraw %}</pre>
                                        </template>
                                    </el-alert>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20" style="margin-top: 20px;">
                        <!-- 产品类型验证 -->
                        <el-col :span="12">
                            <el-card class="test-card">
                                <template #header>
                                    <span>产品类型验证</span>
                                </template>
                                <el-form :model="testForms.product" label-width="100px">
                                    <el-form-item label="用户ID">
                                        <el-input v-model="testForms.product.user_id" placeholder="请输入用户ID"></el-input>
                                    </el-form-item>
                                    <el-form-item label="产品代码">
                                        <el-input v-model="testForms.product.product_code" placeholder="请输入产品代码"></el-input>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="testProductVerify" :loading="loading">
                                            验证产品授权
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                                <div v-if="testResults.product" class="test-result">
                                    <el-alert 
                                        :type="testResults.product.success ? 'success' : 'error'"
                                        :title="testResults.product.message"
                                        show-icon
                                        :closable="false">
                                        <template #default>
                                            <pre>{% raw %}{{ JSON.stringify(testResults.product.data, null, 2) }}{% endraw %}</pre>
                                        </template>
                                    </el-alert>
                                </div>
                            </el-card>
                        </el-col>

                        <!-- 混合验证 -->
                        <el-col :span="12">
                            <el-card class="test-card">
                                <template #header>
                                    <span>混合验证</span>
                                </template>
                                <el-form :model="testForms.mixed" label-width="100px">
                                    <el-form-item label="用户ID">
                                        <el-input v-model="testForms.mixed.user_id" placeholder="请输入用户ID"></el-input>
                                    </el-form-item>
                                    <el-form-item label="授权码">
                                        <el-input v-model="testForms.mixed.license_code" placeholder="可选，授权码"></el-input>
                                    </el-form-item>
                                    <el-form-item label="订单号">
                                        <el-input v-model="testForms.mixed.order_number" placeholder="可选，订单号"></el-input>
                                    </el-form-item>
                                    <el-form-item label="产品代码">
                                        <el-input v-model="testForms.mixed.product_code" placeholder="可选，产品代码"></el-input>
                                    </el-form-item>
                                    <el-form-item label="消费配额">
                                        <el-switch v-model="testForms.mixed.consume_quota"></el-switch>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="testMixedVerify" :loading="loading">
                                            混合验证
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                                <div v-if="testResults.mixed" class="test-result">
                                    <el-alert 
                                        :type="testResults.mixed.success ? 'success' : 'error'"
                                        :title="testResults.mixed.message"
                                        show-icon
                                        :closable="false">
                                        <template #default>
                                            <pre>{% raw %}{{ JSON.stringify(testResults.mixed.data, null, 2) }}{% endraw %}</pre>
                                        </template>
                                    </el-alert>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
            </el-tab-pane>

            <!-- 授权日志 -->
            <el-tab-pane label="授权日志" name="logs">
                <div class="logs-section">
                    <!-- 筛选条件 -->
                    <el-card class="filter-card" style="margin-bottom: 20px;">
                        <el-row :gutter="20">
                            <el-col :span="6">
                                <el-form-item label="操作类型">
                                    <el-select v-model="logFilters.action" placeholder="选择操作类型" clearable style="width: 100%">
                                        <el-option label="订单验证" value="verify_order"></el-option>
                                        <el-option label="授权码验证" value="verify_license"></el-option>
                                        <el-option label="产品验证" value="verify_product"></el-option>
                                        <el-option label="混合验证" value="verify_mixed"></el-option>
                                        <el-option label="API调用" value="api_call"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="结果">
                                    <el-select v-model="logFilters.result" placeholder="选择结果" clearable style="width: 100%">
                                        <el-option label="成功" value="success"></el-option>
                                        <el-option label="失败" value="failed"></el-option>
                                        <el-option label="已过期" value="expired"></el-option>
                                        <el-option label="配额超限" value="quota_exceeded"></el-option>
                                        <el-option label="无效授权" value="invalid_license"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="用户ID">
                                    <el-input v-model="logFilters.user_id" placeholder="输入用户ID" clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="订单号">
                                    <el-input v-model="logFilters.order_number" placeholder="输入订单号" clearable></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20" style="margin-top: 10px;">
                            <el-col :span="12">
                                <el-form-item label="时间范围">
                                    <el-date-picker
                                        v-model="logFilters.date_range"
                                        type="datetimerange"
                                        range-separator="至"
                                        start-placeholder="开始时间"
                                        end-placeholder="结束时间"
                                        style="width: 100%">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12" style="display: flex; align-items: end;">
                                <el-form-item style="margin-bottom: 0;">
                                    <el-button type="primary" @click="loadAuthLogs">
                                        <el-icon><Search /></el-icon>
                                        查询
                                    </el-button>
                                    <el-button @click="resetLogFilters">
                                        <el-icon><Refresh /></el-icon>
                                        重置
                                    </el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-card>

                    <!-- 日志表格 -->
                    <el-table :data="authLogs" v-loading="loading" style="width: 100%">
                        <el-table-column prop="created_at" label="时间" width="160">
                            <template #default="scope">
                                {% raw %}{{ formatTime(scope.row.created_at) }}{% endraw %}
                            </template>
                        </el-table-column>
                        <el-table-column prop="action" label="操作类型" width="120">
                            <template #default="scope">
                                <el-tag :type="getActionType(scope.row.action)">
                                    {% raw %}{{ getActionLabel(scope.row.action) }}{% endraw %}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="result" label="结果" width="100">
                            <template #default="scope">
                                <el-tag :type="getResultType(scope.row.result)">
                                    {% raw %}{{ getResultLabel(scope.row.result) }}{% endraw %}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="user_id" label="用户ID" width="120"></el-table-column>
                        <el-table-column prop="ip_address" label="IP地址" width="120"></el-table-column>
                        <el-table-column prop="license_code" label="授权码" width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="order_number" label="订单号" width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="product_name" label="产品" width="120" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="quota_info" label="配额变化" width="120">
                            <template #default="scope">
                                <span v-if="scope.row.quota_before !== null && scope.row.quota_after !== null">
                                    {% raw %}{{ scope.row.quota_before }} → {{ scope.row.quota_after }}{% endraw %}
                                </span>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="error_message" label="错误信息" show-overflow-tooltip></el-table-column>
                        <el-table-column label="操作" width="100">
                            <template #default="scope">
                                <el-button size="small" @click="viewLogDetail(scope.row)">详情</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div style="text-align: center; margin-top: 20px;">
                        <el-pagination
                            v-model:current-page="pagination.logs.current"
                            v-model:page-size="pagination.logs.size"
                            :total="pagination.logs.total"
                            :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="loadAuthLogs"
                            @current-change="loadAuthLogs">
                        </el-pagination>
                    </div>
                </div>
            </el-tab-pane>


        </el-tabs>
    </el-card>
</div>

<!-- 日志详情对话框 -->
<el-dialog v-model="logDetailVisible" title="日志详情" width="60%">
    <div v-if="selectedLog">
        <el-descriptions :column="2" border>
            <el-descriptions-item label="时间">{% raw %}{{ formatTime(selectedLog.created_at) }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="操作类型">{% raw %}{{ getActionLabel(selectedLog.action) }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="结果">{% raw %}{{ getResultLabel(selectedLog.result) }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="用户ID">{% raw %}{{ selectedLog.user_id || '-' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="IP地址">{% raw %}{{ selectedLog.ip_address || '-' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="用户代理">{% raw %}{{ selectedLog.user_agent || '-' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="授权码">{% raw %}{{ selectedLog.license_code || '-' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="订单号">{% raw %}{{ selectedLog.order_number || '-' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="产品">{% raw %}{{ selectedLog.product_name || '-' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="配额变化" v-if="selectedLog.quota_before !== null && selectedLog.quota_after !== null">
                {% raw %}{{ selectedLog.quota_before }} → {{ selectedLog.quota_after }}{% endraw %}
            </el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px;" v-if="selectedLog.request_params">
            <h4>请求参数</h4>
            <pre class="json-display">{% raw %}{{ JSON.stringify(JSON.parse(selectedLog.request_params), null, 2) }}{% endraw %}</pre>
        </div>

        <div style="margin-top: 20px;" v-if="selectedLog.response_data">
            <h4>响应数据</h4>
            <pre class="json-display">{% raw %}{{ JSON.stringify(JSON.parse(selectedLog.response_data), null, 2) }}{% endraw %}</pre>
        </div>

        <div style="margin-top: 20px;" v-if="selectedLog.error_message">
            <h4>错误信息</h4>
            <el-alert :title="selectedLog.error_message" type="error" :closable="false"></el-alert>
        </div>
    </div>
</el-dialog>
{% endblock %}

{% block methods %}
// 授权验证测试方法
async testOrderVerify() {
    this.loading = true;
    try {
        const response = await fetch('/api/auth/verify-order', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(this.testForms.order)
        });
        this.testResults.order = await response.json();
    } catch (error) {
        this.testResults.order = { success: false, message: '请求失败', data: null };
        ElMessage.error('测试失败');
    }
    this.loading = false;
},

async testLicenseVerify() {
    this.loading = true;
    try {
        const response = await fetch('/api/auth/verify-license', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(this.testForms.license)
        });
        this.testResults.license = await response.json();
    } catch (error) {
        this.testResults.license = { success: false, message: '请求失败', data: null };
        ElMessage.error('测试失败');
    }
    this.loading = false;
},

async testProductVerify() {
    this.loading = true;
    try {
        const response = await fetch('/api/auth/verify-product', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(this.testForms.product)
        });
        this.testResults.product = await response.json();
    } catch (error) {
        this.testResults.product = { success: false, message: '请求失败', data: null };
        ElMessage.error('测试失败');
    }
    this.loading = false;
},

async testMixedVerify() {
    this.loading = true;
    try {
        const response = await fetch('/api/auth/verify-mixed', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(this.testForms.mixed)
        });
        this.testResults.mixed = await response.json();
    } catch (error) {
        this.testResults.mixed = { success: false, message: '请求失败', data: null };
        ElMessage.error('测试失败');
    }
    this.loading = false;
},

// 标签页切换
handleTabClick(tab) {
    // 数据已在页面加载时初始化，无需额外处理
},

// 加载授权日志
async loadAuthLogs() {
    this.loading = true;
    try {
        const params = new URLSearchParams({
            page: this.pagination.logs.current,
            size: this.pagination.logs.size,
            ...this.logFilters
        });

        const response = await fetch(`/api/auth-logs?${params}`);
        const data = await response.json();

        this.authLogs = data.logs || [];
        this.pagination.logs.total = data.total || 0;
    } catch (error) {
        ElMessage.error('加载授权日志失败');
    }
    this.loading = false;
},



// 加载统计数据
async loadAuthStats() {
    try {
        const response = await fetch('/api/auth-stats');
        const data = await response.json();
        this.authStats = data;
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
},

// 重置筛选条件
resetLogFilters() {
    this.logFilters = {
        action: '',
        result: '',
        user_id: '',
        order_number: '',
        date_range: []
    };
    this.loadAuthLogs();
},



// 格式化时间
formatTime(timeStr) {
    if (!timeStr) return '-';
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
},

// 获取操作类型标签
getActionType(action) {
    const types = {
        'verify_order': 'primary',
        'verify_license': 'success',
        'verify_product': 'warning',
        'verify_mixed': 'info',
        'api_call': 'danger'
    };
    return types[action] || 'info';
},

getActionLabel(action) {
    const labels = {
        'verify_order': '订单验证',
        'verify_license': '授权码验证',
        'verify_product': '产品验证',
        'verify_mixed': '混合验证',
        'api_call': 'API调用'
    };
    return labels[action] || action;
},

// 获取结果类型标签
getResultType(result) {
    const types = {
        'success': 'success',
        'failed': 'danger',
        'expired': 'warning',
        'quota_exceeded': 'danger',
        'invalid_license': 'danger',
        'invalid_product': 'warning',
        'unauthorized': 'danger'
    };
    return types[result] || 'info';
},

getResultLabel(result) {
    const labels = {
        'success': '成功',
        'failed': '失败',
        'expired': '已过期',
        'quota_exceeded': '配额超限',
        'invalid_license': '无效授权',
        'invalid_product': '无效产品',
        'unauthorized': '未授权'
    };
    return labels[result] || result;
},



// 查看日志详情
logDetailVisible: false,
selectedLog: null,
viewLogDetail(log) {
    this.selectedLog = log;
    this.logDetailVisible = true;
},


{% endblock %}

{% block mounted %}
// 加载初始数据
this.loadAuthStats();
this.loadAuthLogs();
{% endblock %}

<style>
.auth-management {
    padding: 20px;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h2 {
    margin: 0 0 10px 0;
    color: #303133;
    font-weight: 600;
}

.page-header p {
    margin: 0;
    color: #909399;
    font-size: 14px;
}

.stats-card {
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.modern-card {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stats-content {
    display: flex;
    align-items: center;
    padding: 10px 0;
}

.stats-icon {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.stats-info {
    flex: 1;
}

.stats-number {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
    line-height: 1;
}

.stats-label {
    font-size: 14px;
    color: #909399;
    font-weight: 500;
}

.content-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: none;
}

.test-section {
    padding: 20px 0;
}

.test-card {
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.test-result {
    margin-top: 20px;
}

.test-result pre {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
}

.filter-card {
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.filter-card .el-form-item {
    margin-bottom: 18px;
}

.filter-card .el-form-item__label {
    font-weight: 500;
    color: #606266;
    font-size: 14px;
    line-height: 32px;
}

.filter-card .el-select,
.filter-card .el-input,
.filter-card .el-date-editor {
    width: 100%;
}

.filter-card .el-button {
    margin-right: 10px;
    min-width: 80px;
}

.filter-card .el-button:last-child {
    margin-right: 0;
}

.filter-card .el-row {
    align-items: flex-end;
}

.logs-section, .licenses-section {
    padding: 20px 0;
}

.json-display {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
}

.el-table {
    border-radius: 8px;
    overflow: hidden;
}

.el-table th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 600;
}

.el-table td {
    border-bottom: 1px solid #f5f5f5;
}

.el-table tr:hover > td {
    background-color: #f8f9ff;
}

.el-tabs__item {
    font-weight: 500;
}

.el-tabs__item.is-active {
    color: #409eff;
    font-weight: 600;
}

.el-form--inline .el-form-item {
    margin-right: 20px;
    margin-bottom: 10px;
}

.el-pagination {
    justify-content: center;
}

.el-progress {
    width: 100%;
}

.el-descriptions {
    margin-top: 20px;
}

.el-alert {
    margin-top: 10px;
}
</style>
