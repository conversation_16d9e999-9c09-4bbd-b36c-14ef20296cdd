<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - FocuSee授权管理系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 400px;
            max-width: 90vw;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .login-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .login-form {
            margin-top: 20px;
        }
        
        .form-item {
            margin-bottom: 20px;
        }
        
        .login-button {
            width: 100%;
            height: 45px;
            font-size: 16px;
            font-weight: 500;
        }
        
        .register-button {
            width: 100%;
            height: 45px;
            font-size: 16px;
            font-weight: 500;
            margin-top: 10px;
        }
        
        .footer-links {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .footer-link {
            color: #666;
            text-decoration: none;
            font-size: 14px;
            margin: 0 10px;
        }
        
        .footer-link:hover {
            color: #409EFF;
        }
        
        .tab-container {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="login-container">
            <div class="login-header">
                <h1 class="login-title">用户中心</h1>
                <p class="login-subtitle">FocuSee授权管理系统</p>
            </div>
            
            <div class="tab-container">
                <el-tabs v-model="activeTab" @tab-click="handleTabClick">
                    <el-tab-pane label="登录" name="login">
                        <el-form 
                            :model="loginForm" 
                            :rules="loginRules" 
                            ref="loginFormRef" 
                            class="login-form"
                            @submit.prevent="handleLogin">
                            
                            <div class="form-item">
                                <el-form-item prop="username">
                                    <el-input
                                        v-model="loginForm.username"
                                        placeholder="请输入用户名"
                                        size="large"
                                        :prefix-icon="User"
                                        @keyup.enter="handleLogin">
                                    </el-input>
                                </el-form-item>
                            </div>
                            
                            <div class="form-item">
                                <el-form-item prop="password">
                                    <el-input
                                        v-model="loginForm.password"
                                        type="password"
                                        placeholder="请输入密码"
                                        size="large"
                                        :prefix-icon="Lock"
                                        show-password
                                        @keyup.enter="handleLogin">
                                    </el-input>
                                </el-form-item>
                            </div>
                            
                            <div class="form-item">
                                <el-button 
                                    type="primary" 
                                    class="login-button"
                                    :loading="loginLoading"
                                    @click="handleLogin">
                                    <span v-if="!loginLoading">登录</span>
                                    <span v-else>登录中...</span>
                                </el-button>
                            </div>
                        </el-form>
                    </el-tab-pane>
                    
                    <el-tab-pane label="注册" name="register">
                        <el-form 
                            :model="registerForm" 
                            :rules="registerRules" 
                            ref="registerFormRef" 
                            class="login-form"
                            @submit.prevent="handleRegister">
                            
                            <div class="form-item">
                                <el-form-item prop="username">
                                    <el-input
                                        v-model="registerForm.username"
                                        placeholder="请输入用户名"
                                        size="large"
                                        :prefix-icon="User">
                                    </el-input>
                                </el-form-item>
                            </div>
                            
                            <div class="form-item">
                                <el-form-item prop="email">
                                    <el-input
                                        v-model="registerForm.email"
                                        placeholder="请输入邮箱"
                                        size="large"
                                        :prefix-icon="Message">
                                    </el-input>
                                </el-form-item>
                            </div>
                            
                            <div class="form-item">
                                <el-form-item prop="phone">
                                    <el-input
                                        v-model="registerForm.phone"
                                        placeholder="请输入手机号（可选）"
                                        size="large"
                                        :prefix-icon="Phone">
                                    </el-input>
                                </el-form-item>
                            </div>
                            
                            <div class="form-item">
                                <el-form-item prop="password">
                                    <el-input
                                        v-model="registerForm.password"
                                        type="password"
                                        placeholder="请输入密码"
                                        size="large"
                                        :prefix-icon="Lock"
                                        show-password>
                                    </el-input>
                                </el-form-item>
                            </div>
                            
                            <div class="form-item">
                                <el-form-item prop="confirmPassword">
                                    <el-input
                                        v-model="registerForm.confirmPassword"
                                        type="password"
                                        placeholder="请确认密码"
                                        size="large"
                                        :prefix-icon="Lock"
                                        show-password
                                        @keyup.enter="handleRegister">
                                    </el-input>
                                </el-form-item>
                            </div>
                            
                            <div class="form-item">
                                <el-button 
                                    type="success" 
                                    class="register-button"
                                    :loading="registerLoading"
                                    @click="handleRegister">
                                    <span v-if="!registerLoading">注册</span>
                                    <span v-else>注册中...</span>
                                </el-button>
                            </div>
                        </el-form>
                    </el-tab-pane>
                </el-tabs>
            </div>
            
            <div class="footer-links">
                <a href="/" class="footer-link">管理员登录</a>
                <a href="/agent/login" class="footer-link">代理商登录</a>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const activeTab = ref('login');
                const loginFormRef = ref(null);
                const registerFormRef = ref(null);
                const loginLoading = ref(false);
                const registerLoading = ref(false);
                
                const loginForm = reactive({
                    username: '',
                    password: ''
                });
                
                const registerForm = reactive({
                    username: '',
                    email: '',
                    phone: '',
                    password: '',
                    confirmPassword: ''
                });
                
                const loginRules = {
                    username: [
                        { required: true, message: '请输入用户名', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入密码', trigger: 'blur' }
                    ]
                };
                
                const registerRules = {
                    username: [
                        { required: true, message: '请输入用户名', trigger: 'blur' },
                        { min: 3, message: '用户名长度不能少于3位', trigger: 'blur' }
                    ],
                    email: [
                        { required: true, message: '请输入邮箱', trigger: 'blur' },
                        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入密码', trigger: 'blur' },
                        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
                    ],
                    confirmPassword: [
                        { required: true, message: '请确认密码', trigger: 'blur' },
                        {
                            validator: (rule, value, callback) => {
                                if (value !== registerForm.password) {
                                    callback(new Error('两次输入的密码不一致'));
                                } else {
                                    callback();
                                }
                            },
                            trigger: 'blur'
                        }
                    ]
                };
                
                const handleTabClick = (tab) => {
                    // 清空表单
                    if (tab.props.name === 'login') {
                        Object.assign(loginForm, { username: '', password: '' });
                        if (loginFormRef.value) {
                            loginFormRef.value.resetFields();
                        }
                    } else {
                        Object.assign(registerForm, {
                            username: '',
                            email: '',
                            phone: '',
                            password: '',
                            confirmPassword: ''
                        });
                        if (registerFormRef.value) {
                            registerFormRef.value.resetFields();
                        }
                    }
                };
                
                const handleLogin = async () => {
                    try {
                        await loginFormRef.value.validate();
                        loginLoading.value = true;
                        
                        const response = await fetch('/api/user-auth/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(loginForm)
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            
                            // 保存token到localStorage
                            localStorage.setItem('user_token', data.access_token);
                            localStorage.setItem('user_info', JSON.stringify(data.user_info));
                            
                            ElMessage.success('登录成功');
                            
                            // 跳转到用户仪表板
                            setTimeout(() => {
                                window.location.href = '/user/dashboard';
                            }, 1000);
                            
                        } else {
                            const error = await response.json();
                            ElMessage.error(error.detail || '登录失败');
                        }
                        
                    } catch (error) {
                        if (error.message) {
                            return; // 表单验证错误
                        }
                        ElMessage.error('网络错误，请稍后重试');
                    } finally {
                        loginLoading.value = false;
                    }
                };
                
                const handleRegister = async () => {
                    try {
                        await registerFormRef.value.validate();
                        registerLoading.value = true;
                        
                        const response = await fetch('/api/user-auth/register', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                username: registerForm.username,
                                email: registerForm.email,
                                phone: registerForm.phone,
                                password: registerForm.password
                            })
                        });
                        
                        if (response.ok) {
                            ElMessage.success('注册成功，请登录');
                            activeTab.value = 'login';
                            
                            // 将注册的用户名填入登录表单
                            loginForm.username = registerForm.username;
                            
                        } else {
                            const error = await response.json();
                            ElMessage.error(error.detail || '注册失败');
                        }
                        
                    } catch (error) {
                        if (error.message) {
                            return; // 表单验证错误
                        }
                        ElMessage.error('网络错误，请稍后重试');
                    } finally {
                        registerLoading.value = false;
                    }
                };
                
                return {
                    activeTab,
                    loginForm,
                    registerForm,
                    loginRules,
                    registerRules,
                    loginFormRef,
                    registerFormRef,
                    loginLoading,
                    registerLoading,
                    handleTabClick,
                    handleLogin,
                    handleRegister,
                    User: ElementPlusIconsVue.User,
                    Lock: ElementPlusIconsVue.Lock,
                    Message: ElementPlusIconsVue.Message,
                    Phone: ElementPlusIconsVue.Phone
                };
            }
        });

        // 注册 Element Plus 图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>
