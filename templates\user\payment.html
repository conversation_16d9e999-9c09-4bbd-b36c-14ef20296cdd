{% extends "user/base.html" %}

{% block title %}支付中心{% endblock %}
{% block page_title %}支付中心{% endblock %}

{% block content %}
<div id="payment-app">
    <!-- 产品选择区域 -->
    <el-card class="mb-4">
        <template #header>
            <div class="card-header">
                <span>选择产品</span>
            </div>
        </template>
        
        <el-row :gutter="20">
            <el-col :span="24" v-if="loading">
                <el-skeleton :rows="3" animated />
            </el-col>
            <el-col :span="8" v-for="product in products" :key="product.id" v-else>
                <el-card 
                    class="product-card" 
                    :class="{ 'selected': selectedProduct && selectedProduct.id === product.id }"
                    @click="selectProduct(product)"
                    shadow="hover">
                    <div class="product-info">
                        <h3 v-text="product.name"></h3>
                        <p class="product-description" v-text="product.description"></p>
                        <div class="product-price">
                            <span class="price">¥<span v-text="product.price"></span></span>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </el-card>

    <!-- 支付方式选择 -->
    <el-card class="mb-4" v-if="selectedProduct">
        <template #header>
            <div class="card-header">
                <span>选择支付方式</span>
            </div>
        </template>
        
        <el-radio-group v-model="paymentMethod" size="large">
            <el-radio-button label="face_to_face">
                <el-icon><QrCode /></el-icon>
                扫码支付
            </el-radio-button>
            <el-radio-button label="order_code">
                <el-icon><CreditCard /></el-icon>
                订单码支付
            </el-radio-button>
        </el-radio-group>
    </el-card>

    <!-- 用户信息输入 -->
    <el-card class="mb-4" v-if="selectedProduct">
        <template #header>
            <div class="card-header">
                <span>用户信息</span>
            </div>
        </template>
        
        <el-form :model="userForm" label-width="100px">
            <el-form-item label="用户ID" required>
                <el-input v-model="userForm.user_id" placeholder="请输入用户ID" />
            </el-form-item>
            <el-form-item label="订单码" v-if="paymentMethod === 'order_code'" required>
                <el-input v-model="userForm.auth_code" placeholder="请输入支付宝付款码" />
            </el-form-item>
        </el-form>
    </el-card>

    <!-- 支付操作区域 -->
    <el-card v-if="selectedProduct">
        <template #header>
            <div class="card-header">
                <span>支付操作</span>
            </div>
        </template>
        
        <div class="payment-actions">
            <el-button 
                type="primary" 
                size="large" 
                @click="createPayment"
                :loading="paymentLoading"
                :disabled="!userForm.user_id || (paymentMethod === 'order_code' && !userForm.auth_code)">
                <span v-text="paymentMethod === 'face_to_face' ? '生成支付二维码' : '立即支付'"></span>
            </el-button>
        </div>
    </el-card>

    <!-- 支付结果显示 -->
    <el-card v-if="paymentResult" class="mt-4">
        <template #header>
            <div class="card-header">
                <span>支付结果</span>
            </div>
        </template>
        
        <!-- 扫码支付结果 -->
        <div v-if="paymentMethod === 'face_to_face' && paymentResult.success" class="qr-payment-result">
            <el-row :gutter="20">
                <el-col :span="12">
                    <div class="qr-code-container">
                        <img :src="paymentResult.qr_image" alt="支付二维码" class="qr-code-image" />
                        <p class="qr-code-tip">请使用支付宝扫描二维码完成支付</p>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="payment-info">
                        <el-descriptions title="订单信息" :column="1" border>
                            <el-descriptions-item label="订单号"><span v-text="paymentResult.order_no"></span></el-descriptions-item>
                            <el-descriptions-item label="产品名称"><span v-text="paymentResult.product_name"></span></el-descriptions-item>
                            <el-descriptions-item label="支付金额">¥<span v-text="paymentResult.amount"></span></el-descriptions-item>
                            <el-descriptions-item label="过期时间"><span v-text="formatTime(paymentResult.expire_time)"></span></el-descriptions-item>
                        </el-descriptions>
                        
                        <div class="payment-status mt-4">
                            <el-button @click="checkPaymentStatus" :loading="statusChecking">
                                查询支付状态
                            </el-button>
                            <el-button @click="cancelPayment" type="danger" plain>
                                取消支付
                            </el-button>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
        
        <!-- 订单码支付结果 -->
        <div v-else-if="paymentMethod === 'order_code'" class="order-code-result">
            <el-result
                :icon="paymentResult.success ? 'success' : 'error'"
                :title="paymentResult.success ? '支付成功' : '支付失败'"
                :sub-title="paymentResult.success ? `订单号: ${paymentResult.order_no}` : paymentResult.error">
                
                <template #extra v-if="paymentResult.success">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="交易号"><span v-text="paymentResult.trade_no"></span></el-descriptions-item>
                        <el-descriptions-item label="支付金额">¥<span v-text="paymentResult.amount"></span></el-descriptions-item>
                        <el-descriptions-item label="支付时间"><span v-text="formatTime(paymentResult.paid_at)"></span></el-descriptions-item>
                        <el-descriptions-item label="产品名称"><span v-text="paymentResult.product_name"></span></el-descriptions-item>
                    </el-descriptions>
                </template>
            </el-result>
        </div>
        
        <!-- 错误信息 -->
        <el-alert v-if="!paymentResult.success" :title="paymentResult.error" type="error" show-icon />
    </el-card>

    <!-- 我的订单 -->
    <el-card class="mt-4">
        <template #header>
            <div class="card-header">
                <span>我的订单</span>
                <el-button @click="loadUserOrders" :loading="ordersLoading">刷新</el-button>
            </div>
        </template>
        
        <el-table :data="userOrders" v-loading="ordersLoading">
            <el-table-column prop="order_no" label="订单号" width="180" />
            <el-table-column prop="product_name" label="产品名称" />
            <el-table-column prop="amount" label="金额" width="100">
                <template #default="scope">
                    ¥<span v-text="scope.row.amount"></span>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                        <span v-text="getStatusText(scope.row.status)"></span>
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="scope">
                    <span v-text="formatTime(scope.row.created_at)"></span>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
                <template #default="scope">
                    <el-button size="small" @click="viewOrderDetail(scope.row)">详情</el-button>
                    <el-button 
                        v-if="scope.row.status === 'pending'" 
                        size="small" 
                        type="danger" 
                        @click="cancelOrder(scope.row.order_no)">
                        取消
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </el-card>
</div>
{% endblock %}

{% block scripts %}
<script>
const { createApp, ref, reactive, onMounted } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

const paymentApp = createApp({
    setup() {
        const loading = ref(true);
        const paymentLoading = ref(false);
        const statusChecking = ref(false);
        const ordersLoading = ref(false);
        
        const products = ref([]);
        const selectedProduct = ref(null);
        const paymentMethod = ref('face_to_face');
        const userForm = reactive({
            user_id: '',
            auth_code: ''
        });
        const paymentResult = ref(null);
        const userOrders = ref([]);
        
        // 加载产品列表
        const loadProducts = async () => {
            try {
                loading.value = true;
                const response = await fetch('/api/payment/test/products');
                const data = await response.json();
                
                if (data.success) {
                    products.value = data.products;
                } else {
                    ElMessage.error(data.error || '加载产品失败');
                }
            } catch (error) {
                ElMessage.error('加载产品失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        };
        
        // 选择产品
        const selectProduct = (product) => {
            selectedProduct.value = product;
            paymentResult.value = null;
        };
        
        // 创建支付
        const createPayment = async () => {
            if (!selectedProduct.value || !userForm.user_id) {
                ElMessage.warning('请选择产品并填写用户信息');
                return;
            }
            
            if (paymentMethod.value === 'order_code' && !userForm.auth_code) {
                ElMessage.warning('请输入支付宝付款码');
                return;
            }
            
            try {
                paymentLoading.value = true;
                
                const endpoint = paymentMethod.value === 'face_to_face' 
                    ? '/api/payment/face-to-face' 
                    : '/api/payment/order-code';
                
                const requestData = {
                    user_id: userForm.user_id,
                    product_id: selectedProduct.value.id
                };
                
                if (paymentMethod.value === 'order_code') {
                    requestData.auth_code = userForm.auth_code;
                }
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                paymentResult.value = data;
                
                if (data.success) {
                    if (paymentMethod.value === 'face_to_face') {
                        ElMessage.success('支付二维码生成成功，请扫码支付');
                    } else {
                        ElMessage.success('支付成功');
                        loadUserOrders(); // 刷新订单列表
                    }
                } else {
                    ElMessage.error(data.error || '支付失败');
                }
                
            } catch (error) {
                ElMessage.error('支付失败: ' + error.message);
            } finally {
                paymentLoading.value = false;
            }
        };
        
        // 查询支付状态
        const checkPaymentStatus = async () => {
            if (!paymentResult.value || !paymentResult.value.order_no) {
                return;
            }
            
            try {
                statusChecking.value = true;
                
                const response = await fetch('/api/payment/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        order_no: paymentResult.value.order_no
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    if (data.status === 'paid') {
                        ElMessage.success('支付成功！');
                        paymentResult.value = { ...paymentResult.value, ...data };
                        loadUserOrders(); // 刷新订单列表
                    } else {
                        ElMessage.info('订单状态: ' + getStatusText(data.status));
                    }
                } else {
                    ElMessage.error(data.error || '查询失败');
                }
                
            } catch (error) {
                ElMessage.error('查询失败: ' + error.message);
            } finally {
                statusChecking.value = false;
            }
        };
        
        // 取消支付
        const cancelPayment = async () => {
            if (!paymentResult.value || !paymentResult.value.order_no) {
                return;
            }
            
            try {
                await ElMessageBox.confirm('确定要取消这个支付订单吗？', '确认取消', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                const response = await fetch(`/api/payment/cancel/${paymentResult.value.order_no}`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    ElMessage.success('订单已取消');
                    paymentResult.value = null;
                    loadUserOrders(); // 刷新订单列表
                } else {
                    ElMessage.error(data.error || '取消失败');
                }
                
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error('取消失败: ' + error.message);
                }
            }
        };
        
        // 加载用户订单
        const loadUserOrders = async () => {
            if (!userForm.user_id) {
                return;
            }
            
            try {
                ordersLoading.value = true;
                
                const response = await fetch(`/api/payment/orders/${userForm.user_id}`);
                const data = await response.json();
                
                if (data.success) {
                    userOrders.value = data.orders;
                } else {
                    ElMessage.error(data.error || '加载订单失败');
                }
                
            } catch (error) {
                ElMessage.error('加载订单失败: ' + error.message);
            } finally {
                ordersLoading.value = false;
            }
        };
        
        // 查看订单详情
        const viewOrderDetail = (order) => {
            ElMessageBox.alert(
                `订单号: ${order.order_no}\n产品: ${order.product_name}\n金额: ¥${order.amount}\n状态: ${getStatusText(order.status)}\n创建时间: ${formatTime(order.created_at)}`,
                '订单详情',
                { confirmButtonText: '确定' }
            );
        };
        
        // 取消订单
        const cancelOrder = async (orderNo) => {
            try {
                await ElMessageBox.confirm('确定要取消这个订单吗？', '确认取消', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                const response = await fetch(`/api/payment/cancel/${orderNo}`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    ElMessage.success('订单已取消');
                    loadUserOrders(); // 刷新订单列表
                } else {
                    ElMessage.error(data.error || '取消失败');
                }
                
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error('取消失败: ' + error.message);
                }
            }
        };
        
        // 格式化时间
        const formatTime = (timeStr) => {
            if (!timeStr) return '-';
            return new Date(timeStr).toLocaleString('zh-CN');
        };
        
        // 获取状态类型
        const getStatusType = (status) => {
            const statusMap = {
                'pending': 'warning',
                'paid': 'success',
                'cancelled': 'info',
                'expired': 'danger',
                'failed': 'danger'
            };
            return statusMap[status] || 'info';
        };
        
        // 获取状态文本
        const getStatusText = (status) => {
            const statusMap = {
                'pending': '待支付',
                'paid': '已支付',
                'cancelled': '已取消',
                'expired': '已过期',
                'failed': '支付失败'
            };
            return statusMap[status] || status;
        };
        
        onMounted(() => {
            loadProducts();
        });
        
        return {
            loading,
            paymentLoading,
            statusChecking,
            ordersLoading,
            products,
            selectedProduct,
            paymentMethod,
            userForm,
            paymentResult,
            userOrders,
            selectProduct,
            createPayment,
            checkPaymentStatus,
            cancelPayment,
            loadUserOrders,
            viewOrderDetail,
            cancelOrder,
            formatTime,
            getStatusType,
            getStatusText
        };
    }
});

paymentApp.use(ElementPlus);
paymentApp.mount('#payment-app');
</script>

<style>
.product-card {
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 20px;
}

.product-card:hover {
    transform: translateY(-2px);
}

.product-card.selected {
    border-color: #409eff;
    box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.3);
}

.product-info h3 {
    margin: 0 0 10px 0;
    color: #303133;
}

.product-description {
    color: #606266;
    margin: 0 0 15px 0;
    min-height: 40px;
}

.product-price {
    text-align: right;
}

.price {
    font-size: 24px;
    font-weight: bold;
    color: #f56c6c;
}

.payment-actions {
    text-align: center;
    padding: 20px 0;
}

.qr-code-container {
    text-align: center;
}

.qr-code-image {
    max-width: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
}

.qr-code-tip {
    margin-top: 10px;
    color: #606266;
    font-size: 14px;
}

.payment-info {
    padding-left: 20px;
}

.payment-status {
    text-align: center;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mb-4 {
    margin-bottom: 20px;
}

.mt-4 {
    margin-top: 20px;
}
</style>
{% endblock %}
