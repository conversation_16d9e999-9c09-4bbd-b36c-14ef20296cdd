from typing import List, Optional
from sqlalchemy.orm import Session
from app.models import User, Role, UserRole
from app.models.user import UserType
import json
import logging

logger = logging.getLogger(__name__)

class PermissionService:
    """权限管理服务"""
    
    # 系统权限定义
    PERMISSIONS = {
        # 用户管理权限
        "user.view": "查看用户",
        "user.create": "创建用户", 
        "user.update": "更新用户",
        "user.delete": "删除用户",
        
        # 产品管理权限
        "product.view": "查看产品",
        "product.create": "创建产品",
        "product.update": "更新产品", 
        "product.delete": "删除产品",
        
        # 订单管理权限
        "order.view": "查看订单",
        "order.create": "创建订单",
        "order.update": "更新订单",
        "order.delete": "删除订单",
        
        # 授权码管理权限
        "license.view": "查看授权码",
        "license.create": "创建授权码",
        "license.update": "更新授权码",
        "license.delete": "删除授权码",
        
        # 代理商管理权限
        "agent.view": "查看代理商",
        "agent.create": "创建代理商",
        "agent.update": "更新代理商",
        "agent.delete": "删除代理商",
        "agent.auth": "代理商授权管理",
        
        # 系统管理权限
        "system.admin": "系统管理",
        "system.config": "系统配置",
        "system.log": "系统日志",
        
        # 个人权限
        "profile.view": "查看个人信息",
        "profile.update": "更新个人信息",
    }
    
    # 预定义角色
    DEFAULT_ROLES = {
        "admin": {
            "name": "系统管理员",
            "code": "admin",
            "description": "系统管理员，拥有所有权限",
            "permissions": list(PERMISSIONS.keys()),
            "is_system": True
        },
        "agent": {
            "name": "代理商",
            "code": "agent", 
            "description": "代理商，可以管理被授权的产品和分发授权",
            "permissions": [
                "product.view", "order.view", "order.create", "order.update",
                "license.view", "license.create", "license.update",
                "profile.view", "profile.update"
            ],
            "is_system": True
        },
        "user": {
            "name": "普通用户",
            "code": "user",
            "description": "普通用户，只能查看自己的信息和授权",
            "permissions": ["profile.view", "profile.update"],
            "is_system": True
        }
    }
    
    @staticmethod
    def init_default_roles(db: Session) -> bool:
        """初始化默认角色"""
        try:
            for role_code, role_data in PermissionService.DEFAULT_ROLES.items():
                existing_role = db.query(Role).filter(Role.code == role_code).first()
                if not existing_role:
                    role = Role(
                        name=role_data["name"],
                        code=role_data["code"],
                        description=role_data["description"],
                        permissions=json.dumps(role_data["permissions"]),
                        is_system=role_data["is_system"]
                    )
                    db.add(role)
            
            db.commit()
            logger.info("Default roles initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing default roles: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def assign_role_to_user(db: Session, user_id: str, role_code: str, assigned_by: str = None) -> bool:
        """为用户分配角色"""
        try:
            # 检查角色是否存在
            role = db.query(Role).filter(Role.code == role_code, Role.is_active == True).first()
            if not role:
                logger.error(f"Role {role_code} not found")
                return False
            
            # 检查是否已经分配过该角色
            existing_assignment = db.query(UserRole).filter(
                UserRole.user_id == user_id,
                UserRole.role_id == role.id,
                UserRole.is_active == True
            ).first()
            
            if existing_assignment:
                logger.warning(f"User {user_id} already has role {role_code}")
                return True
            
            # 创建角色分配
            user_role = UserRole(
                user_id=user_id,
                role_id=role.id,
                assigned_by=assigned_by
            )
            
            db.add(user_role)
            db.commit()
            
            logger.info(f"Role {role_code} assigned to user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error assigning role to user: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def get_user_permissions(db: Session, user_id: str) -> List[str]:
        """获取用户的所有权限"""
        try:
            user_roles = db.query(UserRole).join(Role).filter(
                UserRole.user_id == user_id,
                UserRole.is_active == True,
                Role.is_active == True
            ).all()
            
            permissions = set()
            for user_role in user_roles:
                role_permissions = json.loads(user_role.role.permissions or "[]")
                permissions.update(role_permissions)
            
            return list(permissions)
            
        except Exception as e:
            logger.error(f"Error getting user permissions: {str(e)}")
            return []
    
    @staticmethod
    def check_permission(db: Session, user_id: str, permission: str) -> bool:
        """检查用户是否有指定权限"""
        user_permissions = PermissionService.get_user_permissions(db, user_id)
        return permission in user_permissions
    
    @staticmethod
    def get_user_roles(db: Session, user_id: str) -> List[Role]:
        """获取用户的角色列表"""
        try:
            user_roles = db.query(UserRole).join(Role).filter(
                UserRole.user_id == user_id,
                UserRole.is_active == True,
                Role.is_active == True
            ).all()
            
            return [user_role.role for user_role in user_roles]
            
        except Exception as e:
            logger.error(f"Error getting user roles: {str(e)}")
            return []
