import json
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import qrcode
from io import BytesIO
import base64

from app.config import settings

logger = logging.getLogger(__name__)

# 尝试解决 Crypto 模块问题
try:
    import Crypto
except ImportError:
    try:
        import Cryptodome
        import sys
        sys.modules['Crypto'] = Cryptodome
        logger.info("使用 Cryptodome 作为 Crypto 模块")
    except ImportError:
        logger.warning("Crypto 和 Cryptodome 模块都未找到")

# 尝试导入支付宝SDK
try:
    from alipay.aop.api.AlipayClientConfig import AlipayClientConfig
    from alipay.aop.api.DefaultAlipayClient import DefaultAlipayClient
    from alipay.aop.api.request.AlipayTradePrecreateRequest import AlipayTradePrecreateRequest
    from alipay.aop.api.request.AlipayTradePayRequest import AlipayTradePayRequest
    from alipay.aop.api.request.AlipayTradeQueryRequest import AlipayTradeQueryRequest
    from alipay.aop.api.domain.AlipayTradePrecreateModel import AlipayTradePrecreateModel
    from alipay.aop.api.domain.AlipayTradePayModel import AlipayTradePayModel
    from alipay.aop.api.domain.AlipayTradeQueryModel import AlipayTradeQueryModel
    ALIPAY_SDK_AVAILABLE = True
    logger.info("支付宝SDK导入成功")
except ImportError as e:
    # 支付宝SDK是必需的
    ALIPAY_SDK_AVAILABLE = False
    logger.error(f"支付宝SDK导入失败: {str(e)}")
    logger.error("请安装支付宝SDK: pip install alipay-sdk-python")
    raise ImportError("支付宝SDK是必需的，请安装: pip install alipay-sdk-python")



class AlipayService:
    """支付宝支付服务"""
    
    def __init__(self):
        # 支付宝配置 - 从环境变量或配置文件中读取
        self.app_id = getattr(settings, 'alipay_app_id', '2021000000000000')
        self.app_private_key = getattr(settings, 'alipay_app_private_key', '')
        self.alipay_public_key = getattr(settings, 'alipay_public_key', '')
        self.gateway_url = getattr(settings, 'alipay_gateway_url', 'https://openapi-sandbox.dl.alipaydev.com/gateway.do')
        self.notify_url = getattr(settings, 'alipay_notify_url', '')
        self.return_url = getattr(settings, 'alipay_return_url', '')
        self.sign_type = "RSA2"
        self.debug = getattr(settings, 'debug', True)

        # 验证配置
        self._validate_config()

        # 初始化支付宝客户端
        try:
            # 使用支付宝SDK配置
            alipay_config = AlipayClientConfig()
            alipay_config.app_id = self.app_id
            alipay_config.app_private_key = self.app_private_key
            alipay_config.alipay_public_key = self.alipay_public_key
            alipay_config.sign_type = self.sign_type
            alipay_config.charset = "utf-8"
            alipay_config.server_url = self.gateway_url

            self.alipay = DefaultAlipayClient(alipay_client_config=alipay_config)
            logger.info(f"支付宝客户端初始化成功 - APP_ID: {self.app_id}")
        except Exception as e:
            logger.error(f"支付宝客户端初始化失败: {str(e)}")
            raise

    def _validate_config(self):
        """验证支付宝配置"""

        if not self.app_id or self.app_id == '2021000000000000':
            logger.warning("使用默认APP_ID，请在生产环境中配置真实的APP_ID")

        if not self.app_private_key:
            logger.error("支付宝应用私钥未配置")

        if not self.alipay_public_key:
            logger.error("支付宝公钥未配置")

        if not self.notify_url:
            logger.warning("支付宝异步通知地址未配置")

        if not self.return_url:
            logger.warning("支付宝同步跳转地址未配置")

        # 显示网关地址信息
        if "sandbox" in self.gateway_url:
            logger.info(f"使用支付宝沙箱环境: {self.gateway_url}")
        else:
            logger.info(f"使用支付宝生产环境: {self.gateway_url}")
    
    def create_face_to_face_payment(self,
                                  out_trade_no: str,
                                  total_amount: float,
                                  subject: str,
                                  body: Optional[str] = None,
                                  timeout_express: str = "30m") -> Dict[str, Any]:
        """
        创建当面付支付订单

        Args:
            out_trade_no: 商户订单号
            total_amount: 订单总金额
            subject: 订单标题
            body: 订单描述
            timeout_express: 订单超时时间

        Returns:
            包含二维码信息的字典
        """
        try:
            logger.info(f"开始创建支付二维码 - 订单号: {out_trade_no}, 金额: {total_amount}, 标题: {subject}")

            if ALIPAY_SDK_AVAILABLE:
                # 使用新版SDK的正确方式
                try:
                    logger.info("使用支付宝SDK创建当面付订单")
                    model = AlipayTradePrecreateModel()
                    model.out_trade_no = out_trade_no
                    model.total_amount = str(total_amount)
                    model.subject = subject
                    model.timeout_express = timeout_express
                    if body:
                        model.body = body

                    # 记录请求参数
                    logger.info(f"支付宝请求参数 - 订单号: {out_trade_no}, 金额: {total_amount}, 超时: {timeout_express}, 回调: {self.notify_url}")

                    request = AlipayTradePrecreateRequest()
                    # 尝试不同的设置方法
                    if hasattr(request, 'set_biz_model'):
                        request.set_biz_model(model)
                        logger.debug("使用 set_biz_model 方法设置请求参数")
                    else:
                        request.biz_model = model
                        logger.debug("使用 biz_model 属性设置请求参数")

                    # 在请求级别设置回调地址（这是关键！）
                    if self.notify_url:
                        request.notify_url = self.notify_url
                        logger.info(f"✅ 设置支付回调地址: {self.notify_url}")
                    else:
                        logger.warning("❌ 未配置支付回调地址，将无法接收支付成功通知")

                    # 设置同步跳转地址
                    if self.return_url:
                        request.return_url = self.return_url
                        logger.info(f"✅ 设置同步跳转地址: {self.return_url}")
                    else:
                        logger.warning("❌ 未配置同步跳转地址")

                    logger.info("调用支付宝当面付API...")
                    response = self.alipay.execute(request)
                    logger.info("支付宝当面付API调用完成")
                except Exception as sdk_error:
                    logger.error(f"支付宝SDK调用失败: {str(sdk_error)}")
                    raise

            logger.info(f"支付宝当面付预创建响应: {response}")

            # 处理不同类型的响应
            response_code = None
            qr_code = None
            error_msg = "创建支付订单失败"

            logger.info("开始解析支付宝当面付响应...")

            # 检查响应类型和内容
            if hasattr(response, 'code'):
                logger.info("响应类型: 对象属性模式")
                response_code = response.code
                qr_code = getattr(response, 'qr_code', None)
                error_msg = getattr(response, 'sub_msg', None) or getattr(response, 'msg', '创建支付订单失败')
                logger.info(f"解析结果 - code: {response_code}, qr_code存在: {bool(qr_code)}")
            elif isinstance(response, dict):
                logger.info("响应类型: 字典模式")
                response_code = response.get('code')
                qr_code = response.get('qr_code')
                error_msg = response.get('sub_msg') or response.get('msg', '创建支付订单失败')
                logger.info(f"解析结果 - code: {response_code}, qr_code存在: {bool(qr_code)}")
            elif isinstance(response, str):
                logger.info("响应类型: 字符串模式")
                try:
                    import json
                    response_dict = json.loads(response)
                    response_code = response_dict.get('code')
                    qr_code = response_dict.get('qr_code')
                    error_msg = response_dict.get('sub_msg') or response_dict.get('msg', '创建支付订单失败')
                    logger.info(f"JSON解析结果 - code: {response_code}, qr_code存在: {bool(qr_code)}")
                except Exception as parse_error:
                    logger.error(f"无法解析JSON响应: {response}, 错误: {str(parse_error)}")
            else:
                logger.warning(f"未知响应类型: {type(response)}")

            if response_code == "10000" and qr_code:
                logger.info(f"支付二维码创建成功 - 二维码: {qr_code}")

                # 生成二维码图片
                logger.info("开始生成二维码图片...")
                qr_image = self._generate_qr_code_image(qr_code)
                if qr_image:
                    logger.info("二维码图片生成成功")
                else:
                    logger.warning("二维码图片生成失败")

                result = {
                    "success": True,
                    "qr_code": qr_code,
                    "qr_image": qr_image,
                    "out_trade_no": out_trade_no,
                    "response": response.__dict__ if hasattr(response, '__dict__') else str(response)
                }
                logger.info(f"支付二维码创建完成 - 订单号: {out_trade_no}")
                return result
            else:
                logger.warning(f"支付二维码创建失败 - 错误码: {response_code}, 错误信息: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "response": response.__dict__ if hasattr(response, '__dict__') else str(response)
                }

        except Exception as e:
            logger.error(f"创建当面付支付订单异常 - 订单号: {out_trade_no}, 错误: {str(e)}")
            logger.exception("创建当面付支付订单异常详情:")
            return {
                "success": False,
                "error": f"创建支付订单失败: {str(e)}"
            }
    
    def create_order_code_payment(self,
                                out_trade_no: str,
                                total_amount: float,
                                subject: str,
                                auth_code: str,
                                body: Optional[str] = None) -> Dict[str, Any]:
        """
        创建订单码支付

        Args:
            out_trade_no: 商户订单号
            total_amount: 订单总金额
            subject: 订单标题
            auth_code: 支付授权码（用户付款码）
            body: 订单描述

        Returns:
            支付结果字典
        """
        try:
            if ALIPAY_SDK_AVAILABLE:
                # 使用新版SDK的正确方式
                try:
                    model = AlipayTradePayModel()
                    model.out_trade_no = out_trade_no
                    model.total_amount = str(total_amount)
                    model.subject = subject
                    model.auth_code = auth_code
                    if body:
                        model.body = body

                    request = AlipayTradePayRequest()
                    # 尝试不同的设置方法
                    if hasattr(request, 'set_biz_model'):
                        request.set_biz_model(model)
                    else:
                        request.biz_model = model

                    # 在请求级别设置回调地址（这是关键！）
                    if self.notify_url:
                        request.notify_url = self.notify_url
                        logger.info(f"✅ 设置支付回调地址: {self.notify_url}")
                    else:
                        logger.warning("❌ 未配置支付回调地址，将无法接收支付成功通知")

                    # 设置同步跳转地址
                    if self.return_url:
                        request.return_url = self.return_url
                        logger.info(f"✅ 设置同步跳转地址: {self.return_url}")
                    else:
                        logger.warning("❌ 未配置同步跳转地址")

                    response = self.alipay.execute(request)
                except Exception as sdk_error:
                    logger.error(f"支付宝SDK调用失败: {str(sdk_error)}")
                    raise

            logger.info(f"支付宝订单码支付响应: {response}")

            # 处理不同类型的响应
            response_code = None
            trade_status = None
            trade_no = None
            error_msg = "支付失败"

            # 检查响应类型和内容
            if hasattr(response, 'code'):
                response_code = response.code
                trade_status = getattr(response, 'trade_status', None)
                trade_no = getattr(response, 'trade_no', None)
                error_msg = getattr(response, 'sub_msg', None) or getattr(response, 'msg', '支付失败')
            elif isinstance(response, dict):
                response_code = response.get('code')
                trade_status = response.get('trade_status')
                trade_no = response.get('trade_no')
                error_msg = response.get('sub_msg') or response.get('msg', '支付失败')
            elif isinstance(response, str):
                try:
                    import json
                    response_dict = json.loads(response)
                    response_code = response_dict.get('code')
                    trade_status = response_dict.get('trade_status')
                    trade_no = response_dict.get('trade_no')
                    error_msg = response_dict.get('sub_msg') or response_dict.get('msg', '支付失败')
                except:
                    logger.error(f"无法解析响应: {response}")

            if response_code == "10000":
                if trade_status == "TRADE_SUCCESS":
                    return {
                        "success": True,
                        "trade_no": trade_no,
                        "out_trade_no": out_trade_no,
                        "trade_status": trade_status,
                        "response": response.__dict__ if hasattr(response, '__dict__') else str(response)
                    }
                elif trade_status == "WAIT_BUYER_PAY":
                    return {
                        "success": False,
                        "error": "等待用户付款",
                        "trade_status": trade_status,
                        "response": response.__dict__ if hasattr(response, '__dict__') else str(response)
                    }

            return {
                "success": False,
                "error": error_msg,
                "response": response.__dict__ if hasattr(response, '__dict__') else str(response)
            }

        except Exception as e:
            logger.error(f"订单码支付失败: {str(e)}")
            return {
                "success": False,
                "error": f"支付失败: {str(e)}"
            }
    
    def query_payment_status(self, out_trade_no: str, trade_no: Optional[str] = None) -> Dict[str, Any]:
        """
        查询支付状态

        Args:
            out_trade_no: 商户订单号
            trade_no: 支付宝交易号

        Returns:
            支付状态查询结果
        """
        try:
            if ALIPAY_SDK_AVAILABLE:
                # 使用新版SDK的正确方式
                try:
                    model = AlipayTradeQueryModel()
                    model.out_trade_no = out_trade_no
                    if trade_no:
                        model.trade_no = trade_no

                    request = AlipayTradeQueryRequest()
                    # 尝试不同的设置方法
                    if hasattr(request, 'set_biz_model'):
                        request.set_biz_model(model)
                    else:
                        request.biz_model = model

                    response = self.alipay.execute(request)
                except Exception as sdk_error:
                    logger.error(f"支付宝SDK调用失败: {str(sdk_error)}")
                    raise

            logger.info(f"支付状态查询请求 - 订单号: {out_trade_no}, 交易号: {trade_no}")
            logger.info(f"支付状态查询响应: {response}")

            # 记录响应的详细信息
            if hasattr(response, '__dict__'):
                logger.debug(f"响应对象属性: {response.__dict__}")
            elif isinstance(response, (dict, str)):
                logger.debug(f"响应内容类型: {type(response)}, 长度: {len(str(response))}")
            else:
                logger.debug(f"响应类型: {type(response)}")

            # 处理不同类型的响应
            response_code = None
            trade_status = None
            trade_no = None
            total_amount = None
            error_msg = "查询失败"

            logger.info("开始解析支付状态查询响应...")

            # 检查响应类型和内容
            if hasattr(response, 'code'):
                logger.info("响应类型: 对象属性模式")
                response_code = response.code
                trade_status = getattr(response, 'trade_status', None)
                trade_no = getattr(response, 'trade_no', None)
                total_amount = getattr(response, 'total_amount', None)
                error_msg = getattr(response, 'sub_msg', None) or getattr(response, 'msg', '查询失败')
                logger.info(f"解析结果 - code: {response_code}, trade_status: {trade_status}, trade_no: {trade_no}")
            elif isinstance(response, dict):
                logger.info("响应类型: 字典模式")
                response_code = response.get('code')
                trade_status = response.get('trade_status')
                trade_no = response.get('trade_no')
                total_amount = response.get('total_amount')
                error_msg = response.get('sub_msg') or response.get('msg', '查询失败')
                logger.info(f"解析结果 - code: {response_code}, trade_status: {trade_status}, trade_no: {trade_no}")
            elif isinstance(response, str):
                logger.info("响应类型: 字符串模式")
                try:
                    import json
                    response_dict = json.loads(response)
                    response_code = response_dict.get('code')
                    trade_status = response_dict.get('trade_status')
                    trade_no = response_dict.get('trade_no')
                    total_amount = response_dict.get('total_amount')
                    error_msg = response_dict.get('sub_msg') or response_dict.get('msg', '查询失败')
                    logger.info(f"JSON解析结果 - code: {response_code}, trade_status: {trade_status}, trade_no: {trade_no}")
                except Exception as parse_error:
                    logger.error(f"无法解析JSON响应: {response}, 错误: {str(parse_error)}")
            else:
                logger.warning(f"未知响应类型: {type(response)}")

            if response_code == "10000":
                logger.info(f"查询成功 - 交易状态: {trade_status}")
                return {
                    "success": True,
                    "trade_status": trade_status,
                    "trade_no": trade_no,
                    "total_amount": total_amount,
                    "response": response.__dict__ if hasattr(response, '__dict__') else str(response)
                }
            else:
                logger.warning(f"查询失败 - 错误码: {response_code}, 错误信息: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "response": response.__dict__ if hasattr(response, '__dict__') else str(response)
                }

        except Exception as e:
            logger.error(f"查询支付状态失败: {str(e)}")
            return {
                "success": False,
                "error": f"查询失败: {str(e)}"
            }
    
    def verify_notify(self, data: Dict[str, Any]) -> bool:
        """
        验证支付宝异步通知

        Args:
            data: 通知数据

        Returns:
            验证结果
        """
        try:
            logger.info("开始验证支付宝通知签名...")

            # 检查基本参数
            required_fields = ['out_trade_no', 'trade_status', 'total_amount', 'app_id']
            for field in required_fields:
                if field not in data:
                    logger.warning(f"支付宝通知缺少必要字段: {field}")
                    return False

            # 验证APP_ID
            if data.get('app_id') != self.app_id:
                logger.warning(f"APP_ID不匹配: 期望{self.app_id}, 实际{data.get('app_id')}")
                return False

            # 在开发和测试阶段，我们先简化签名验证
            # 检查是否有签名字段
            sign = data.get("sign", "")
            if not sign:
                logger.warning("支付宝通知缺少签名")
                return False

            # 使用SDK验证签名
            try:
                from alipay.aop.api.util.SignatureUtils import verify_with_rsa

                # 移除sign和sign_type参数
                verify_data = {k: v for k, v in data.items() if k not in ['sign', 'sign_type']}

                # 按照支付宝要求排序并拼接参数
                sorted_items = sorted(verify_data.items())
                query_string = "&".join([f"{k}={v}" for k, v in sorted_items])

                logger.info(f"验证字符串长度: {len(query_string)}")
                logger.info(f"签名长度: {len(sign)}")

                # 验证签名
                result = verify_with_rsa(query_string.encode('utf-8'), sign, self.alipay_public_key, 'RSA2')

                if result:
                    logger.info("✅ 支付宝通知签名验证成功")
                    return True
                else:
                    logger.warning("❌ 支付宝通知签名验证失败")
                    return False

            except ImportError:
                logger.error("签名验证工具导入失败")
                return False
            except Exception as verify_error:
                logger.error(f"签名验证异常: {str(verify_error)}")
                return False

        except Exception as e:
            logger.error(f"验证支付宝通知失败: {str(e)}")
            logger.exception("签名验证异常详情:")
            return False
    
    def _generate_qr_code_image(self, qr_code_data: str) -> str:
        """
        生成二维码图片并返回base64编码
        
        Args:
            qr_code_data: 二维码数据
            
        Returns:
            base64编码的图片数据
        """
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_code_data)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            
            # 转换为base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            return f"data:image/png;base64,{img_str}"
            
        except Exception as e:
            logger.error(f"生成二维码失败: {str(e)}")
            return ""


