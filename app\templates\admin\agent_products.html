{% extends "admin/base.html" %}

{% block title %}代理商产品授权 - {{ agent.company_name }} - FocuSee 管理系统{% endblock %}
{% block page_title %}代理商产品授权 - {{ agent.company_name }}{% endblock %}

{% block data %}
    agent: {
        id: {{ agent.id }},
        username: "{{ agent.username }}",
        company_name: "{{ agent.company_name }}",
        contact_name: "{{ agent.contact_name }}",
        contact_email: "{{ agent.contact_email }}"
    },
    products: [
        {% for product in products %}
        {
            id: {{ product.id }},
            name: "{{ product.name }}",
            code: "{{ product.code }}",
            description: "{{ product.description or '' }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    agentAuths: [
        {% for auth in agent_auths %}
        {
            id: {{ auth.id }},
            agent_id: {{ auth.agent_id }},
            product_id: {{ auth.product_id }},
            product_name: "{{ auth.product.name if auth.product else '' }}",
            max_licenses: {{ auth.max_licenses }},
            used_licenses: {{ auth.used_licenses }},
            expire_date: "{{ auth.expire_date.isoformat() if auth.expire_date else '' }}",
            is_active: {{ 'true' if auth.is_active else 'false' }},
            created_at: "{{ auth.created_at.isoformat() if auth.created_at else '' }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    dialogVisible: false,
    editMode: false,
    currentAuth: {},
    authForm: {
        product_id: null,
        max_licenses: 100,
        expire_date: ''
    },
    rules: {
        product_id: [
            { required: true, message: '请选择产品', trigger: 'change' }
        ],
        max_licenses: [
            { required: true, message: '请输入最大授权数', trigger: 'blur' },
            { type: 'number', min: 1, message: '最大授权数必须大于0', trigger: 'blur' }
        ]
    },
    loading: false
{% endblock %}

{% block content %}
<div class="content-card">
    <!-- 代理商信息 -->
    <el-card style="margin-bottom: 20px;">
        <template #header>
            <div class="card-header">
                <span>代理商信息</span>
                <el-button type="primary" @click="$router.go(-1)" style="float: right;">返回</el-button>
            </div>
        </template>
        <el-descriptions :column="3" border>
            <el-descriptions-item label="公司名称">{% raw %}{{ agent.company_name }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="联系人">{% raw %}{{ agent.contact_name }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱">{% raw %}{{ agent.contact_email }}{% endraw %}</el-descriptions-item>
        </el-descriptions>
    </el-card>

    <!-- 操作栏 -->
    <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="openAddDialog">
            <el-icon><Plus /></el-icon>
            添加产品授权
        </el-button>
        <el-button @click="loadAgentAuths">
            <el-icon><Refresh /></el-icon>
            刷新
        </el-button>
    </div>

    <!-- 授权表格 -->
    <el-table :data="agentAuths" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="product_name" label="产品名称" min-width="150"></el-table-column>
        <el-table-column prop="max_licenses" label="最大授权数" width="120"></el-table-column>
        <el-table-column prop="used_licenses" label="已使用" width="100"></el-table-column>
        <el-table-column label="剩余授权" width="100">
            <template #default="scope">
                {% raw %}{{ scope.row.max_licenses - scope.row.used_licenses }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column prop="expire_date" label="过期时间" width="180">
            <template #default="scope">
                {% raw %}{{ scope.row.expire_date ? new Date(scope.row.expire_date).toLocaleString() : '永不过期' }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
            <template #default="scope">
                <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                    {% raw %}{{ scope.row.is_active ? '激活' : '禁用' }}{% endraw %}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
            <template #default="scope">
                <el-button size="small" @click="editAuth(scope.row)">编辑</el-button>
                <el-button 
                    size="small" 
                    :type="scope.row.is_active ? 'danger' : 'success'"
                    @click="toggleAuthStatus(scope.row)">
                    {% raw %}{{ scope.row.is_active ? '禁用' : '启用' }}{% endraw %}
                </el-button>
                <el-button size="small" type="danger" @click="deleteAuth(scope.row)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>
</div>

<!-- 添加/编辑授权对话框 -->
<el-dialog
    :title="editMode ? '编辑产品授权' : '添加产品授权'"
    v-model="dialogVisible"
    width="600px">
    <el-form ref="authFormRef" :model="authForm" :rules="rules" label-width="120px">
        <el-form-item label="产品" prop="product_id">
            <el-select v-model="authForm.product_id" placeholder="请选择产品" style="width: 100%;" :disabled="editMode">
                <el-option
                    v-for="product in products"
                    :key="product.id"
                    :label="product.name"
                    :value="product.id">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="最大授权数" prop="max_licenses">
            <el-input-number v-model="authForm.max_licenses" :min="1" :max="10000" style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="过期时间">
            <el-date-picker
                v-model="authForm.expire_date"
                type="datetime"
                placeholder="选择过期时间（可选）"
                style="width: 100%;"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss">
            </el-date-picker>
        </el-form-item>
    </el-form>
    <template #footer>
        <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveAuth" :loading="loading">
                {% raw %}{{ editMode ? '更新' : '添加' }}{% endraw %}
            </el-button>
        </span>
    </template>
</el-dialog>
{% endblock %}

{% block methods %}
openAddDialog() {
    this.editMode = false;
    this.authForm = {
        product_id: null,
        max_licenses: 100,
        expire_date: ''
    };
    this.dialogVisible = true;
},
editAuth(auth) {
    this.editMode = true;
    this.currentAuth = auth;
    this.authForm = {
        product_id: auth.product_id,
        max_licenses: auth.max_licenses,
        expire_date: auth.expire_date
    };
    this.dialogVisible = true;
},
saveAuth() {
    this.$refs.authFormRef.validate((valid) => {
        if (valid) {
            this.loading = true;
            const url = this.editMode ? `/api/agent-product-auth/${this.currentAuth.id}` : '/api/agent-product-auth';
            const method = this.editMode ? 'PUT' : 'POST';
            
            const data = {
                ...this.authForm,
                agent_id: this.agent.id
            };

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.detail) {
                    throw new Error(data.detail);
                }
                ElMessage.success(this.editMode ? '授权更新成功' : '授权添加成功');
                this.dialogVisible = false;
                this.loadAgentAuths();
            })
            .catch(error => {
                ElMessage.error('操作失败：' + error.message);
            })
            .finally(() => {
                this.loading = false;
            });
        }
    });
},
toggleAuthStatus(auth) {
    const action = auth.is_active ? '禁用' : '启用';
    ElMessageBox.confirm(`确定要${action}该产品授权吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        fetch(`/api/agent-product-auth/${auth.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_active: !auth.is_active
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            return response.json();
        })
        .then(data => {
            ElMessage.success(`授权${action}成功`);
            // 直接更新本地数据，避免重新加载
            auth.is_active = !auth.is_active;
            this.loadAgentAuths();
        })
        .catch(error => {
            console.error('Error toggling auth status:', error);
            ElMessage.error('操作失败：' + (error.detail || error.message || '未知错误'));
        });
    });
},
deleteAuth(auth) {
    ElMessageBox.confirm(`确定要删除该产品授权吗？此操作不可恢复！`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        this.loading = true;
        fetch(`/api/agent-product-auth/${auth.id}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (response.ok) {
                ElMessage.success('授权删除成功');
                this.loadAgentAuths();
            } else {
                return response.json().then(err => Promise.reject(err));
            }
        })
        .catch(error => {
            console.error('Error deleting auth:', error);
            ElMessage.error('删除失败：' + (error.detail || error.message || '未知错误'));
        })
        .finally(() => {
            this.loading = false;
        });
    });
},
loadAgentAuths() {
    this.loading = true;
    fetch(`/api/agent-product-auth/agent/${this.agent.id}`)
        .then(response => response.json())
        .then(data => {
            this.agentAuths = data;
            this.loading = false;
        })
        .catch(error => {
            console.error('Error loading agent auths:', error);
            ElMessage.error('加载授权列表失败');
            this.loading = false;
        });
}
{% endblock %}

{% block mounted %}
this.loadAgentAuths();
{% endblock %}
