{% extends "admin/base.html" %}

{% block title %}仪表板 - FocuSee 管理系统{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block data %}
    statsData: {
        totalUsers: {{ total_users }},
        activeUsers: {{ active_users }},
        totalDownloads: {{ total_downloads }},
        recentDownloads: [
            {% for download in recent_downloads %}
            {
                id: {{ download.id }},
                user_id: "{{ download.user_id }}",
                file_name: "{{ download.file_name or '' }}",
                ip_address: "{{ download.ip_address or '' }}",
                computer_name: "{{ download.computer_name or '' }}",
                download_time: "{{ download.download_time.isoformat() if download.download_time else '' }}"
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ]
    },
    chartInstance: null
{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<el-row :gutter="20" style="margin-bottom: 20px;">
    <el-col :span="6">
        <el-card class="stats-card">
            <div class="stats-content">
                <div class="stats-icon" style="background-color: #409eff;">
                    <el-icon size="24"><User /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{{ "{{ statsData.totalUsers }}" }}</div>
                    <div class="stats-label">总用户数</div>
                </div>
            </div>
        </el-card>
    </el-col>
    
    <el-col :span="6">
        <el-card class="stats-card">
            <div class="stats-content">
                <div class="stats-icon" style="background-color: #67c23a;">
                    <el-icon size="24"><UserFilled /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{{ "{{ statsData.activeUsers }}" }}</div>
                    <div class="stats-label">活跃用户</div>
                </div>
            </div>
        </el-card>
    </el-col>
    
    <el-col :span="6">
        <el-card class="stats-card">
            <div class="stats-content">
                <div class="stats-icon" style="background-color: #e6a23c;">
                    <el-icon size="24"><Download /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{{ "{{ statsData.totalDownloads }}" }}</div>
                    <div class="stats-label">总下载次数</div>
                </div>
            </div>
        </el-card>
    </el-col>
    
    <el-col :span="6">
        <el-card class="stats-card">
            <div class="stats-content">
                <div class="stats-icon" style="background-color: #f56c6c;">
                    <el-icon size="24"><TrendCharts /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{{ "{{ Math.round((statsData.activeUsers / statsData.totalUsers) * 100) || 0 }}" }}%</div>
                    <div class="stats-label">活跃率</div>
                </div>
            </div>
        </el-card>
    </el-col>
</el-row>

<!-- 图表和最近下载 -->
<el-row :gutter="20">
    <el-col :span="12">
        <el-card class="content-card">
            <template #header>
                <div class="card-header">
                    <span>下载趋势</span>
                </div>
            </template>
            <canvas id="downloadChart" width="400" height="200"></canvas>
        </el-card>
    </el-col>
    
    <el-col :span="12">
        <el-card class="content-card">
            <template #header>
                <div class="card-header">
                    <span>最近下载记录</span>
                </div>
            </template>
            <el-table :data="statsData.recentDownloads" style="width: 100%" max-height="300">
                <el-table-column prop="user_id" label="用户ID" width="120"></el-table-column>
                <el-table-column prop="file_name" label="文件名" width="150"></el-table-column>
                <el-table-column prop="ip_address" label="IP地址" width="120"></el-table-column>
                <el-table-column prop="download_time" label="下载时间">
                    <template #default="scope">
                        {{ "{{ formatTime(scope.row.download_time) }}" }}
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
    </el-col>
</el-row>
{% endblock %}

{% block methods %}
formatTime(timeStr) {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
},
initChart() {
    const ctx = document.getElementById('downloadChart').getContext('2d');
    
    // 模拟数据，实际应该从后端获取
    const labels = ['今天', '昨天', '前天', '3天前', '4天前', '5天前', '6天前'];
    const data = [12, 19, 8, 15, 10, 13, 7];
    
    this.chartInstance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '下载次数',
                data: data,
                borderColor: '#409eff',
                backgroundColor: 'rgba(64, 158, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}
{% endblock %}

{% block mounted %}
this.initChart();
{% endblock %}

{% block scripts %}
<style>
.stats-card {
    border: none;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-content {
    display: flex;
    align-items: center;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
}

.stats-info {
    flex: 1;
}

.stats-number {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
    line-height: 1;
}

.stats-label {
    font-size: 14px;
    color: #909399;
    margin-top: 5px;
}

.card-header {
    font-weight: bold;
    color: #303133;
}
</style>
{% endblock %}
