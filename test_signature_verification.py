#!/usr/bin/env python3
"""
测试支付宝签名验证
"""

import requests

def test_signature_verification_service():
    """测试签名验证服务"""
    print("🔐 测试支付宝签名验证服务")
    print("=" * 60)
    
    try:
        from app.services.alipay_service import AlipayService
        
        service = AlipayService()
        print(f"支付宝公钥配置: {'已配置' if service.alipay_public_key else '未配置'}")
        
        # 使用真实的支付宝回调数据进行测试
        real_callback_data = {
            'gmt_create': '2025-08-07 15:43:43',
            'charset': 'utf-8',
            'seller_email': '<EMAIL>',
            'subject': '购买FocuSee Pro',
            'sign': 'PM0lVeXarWGiB3ZiIo3MDN7oqF4fV05lKWszQD7kr+x2ZxCBdFxFdIhJMOlQEbVWneSQXL5vML7bqMFV/pAkU8A95BeRZ+tN55ObjaMgN6kkxpx01YfJSZEP2B1PczYGAwFhYFJltv+XV+aVV3SyBhM073omI0vMlHfrE9NgmHXo3NWoMpqAYZZtmWCory/zoGSGXsYr+Oq+Hwi7Mw1t4ZkLcf6v+81Sy6wI0EKVSxvgVmRruduhF0iNh2or2wiMsFvDY1VkJtQ/HNW6aHqlp4szjX1uGvjJIfqXNzT0akjtU9I9DmYy+WF4518J1BhonFhZ83ZbL+U5aKRbkkdTZw==',
            'buyer_id': '****************',
            'body': '用户4购买FocuSee Pro',
            'invoice_amount': '98.99',
            'notify_id': '2025080701222154351188780506968604',
            'fund_bill_list': '[{"amount":"98.99","fundChannel":"ALIPAYACCOUNT"}]',
            'notify_type': 'trade_status_sync',
            'trade_status': 'TRADE_SUCCESS',
            'receipt_amount': '98.99',
            'buyer_pay_amount': '98.99',
            'app_id': '****************',
            'sign_type': 'RSA2',
            'seller_id': '****************',
            'gmt_payment': '2025-08-07 15:43:50',
            'notify_time': '2025-08-07 15:43:51',
            'version': '1.0',
            'out_trade_no': 'PAY17545526179196',
            'total_amount': '98.99',
            'trade_no': '2025080722001488780508293873',
            'auth_app_id': '****************',
            'buyer_logon_id': '<EMAIL>',
            'point_amount': '0.00'
        }
        
        print("测试真实支付宝回调数据的签名验证...")
        result = service.verify_notify(real_callback_data)
        
        if result:
            print("✅ 真实回调数据签名验证通过")
        else:
            print("❌ 真实回调数据签名验证失败")
            
        return result
        
    except ImportError as e:
        print(f"❌ 依赖库导入失败: {str(e)}")
        print("请安装: pip install pycryptodome")
        return False
    except Exception as e:
        print(f"❌ 签名验证测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_callback_with_real_signature():
    """测试真实签名的回调处理"""
    print(f"\n🔔 测试真实签名的回调处理...")
    
    # 使用真实的支付宝回调数据
    real_callback_data = {
        'gmt_create': '2025-08-07 15:43:43',
        'charset': 'utf-8',
        'seller_email': '<EMAIL>',
        'subject': '购买FocuSee Pro',
        'sign': 'PM0lVeXarWGiB3ZiIo3MDN7oqF4fV05lKWszQD7kr+x2ZxCBdFxFdIhJMOlQEbVWneSQXL5vML7bqMFV/pAkU8A95BeRZ+tN55ObjaMgN6kkxpx01YfJSZEP2B1PczYGAwFhYFJltv+XV+aVV3SyBhM073omI0vMlHfrE9NgmHXo3NWoMpqAYZZtmWCory/zoGSGXsYr+Oq+Hwi7Mw1t4ZkLcf6v+81Sy6wI0EKVSxvgVmRruduhF0iNh2or2wiMsFvDY1VkJtQ/HNW6aHqlp4szjX1uGvjJIfqXNzT0akjtU9I9DmYy+WF4518J1BhonFhZ83ZbL+U5aKRbkkdTZw==',
        'buyer_id': '****************',
        'body': '用户4购买FocuSee Pro',
        'invoice_amount': '98.99',
        'notify_id': '2025080701222154351188780506968604',
        'fund_bill_list': '[{"amount":"98.99","fundChannel":"ALIPAYACCOUNT"}]',
        'notify_type': 'trade_status_sync',
        'trade_status': 'TRADE_SUCCESS',
        'receipt_amount': '98.99',
        'buyer_pay_amount': '98.99',
        'app_id': '****************',
        'sign_type': 'RSA2',
        'seller_id': '****************',
        'gmt_payment': '2025-08-07 15:43:50',
        'notify_time': '2025-08-07 15:43:51',
        'version': '1.0',
        'out_trade_no': 'PAY17545526179196',  # 使用真实的订单号
        'total_amount': '98.99',
        'trade_no': '2025080722001488780508293873',
        'auth_app_id': '****************',
        'buyer_logon_id': '<EMAIL>',
        'point_amount': '0.00'
    }
    
    try:
        print("发送真实回调数据到服务器...")
        response = requests.post(
            "http://localhost:8008/api/payment/alipay/notify",
            data=real_callback_data,
            timeout=10
        )
        
        print(f"回调响应状态码: {response.status_code}")
        print(f"回调响应内容: {response.text}")
        
        if response.status_code == 200:
            if response.text == "success":
                print("✅ 回调处理成功")
                return True
            elif response.text == "fail":
                print("❌ 回调处理失败（可能是签名验证失败）")
                return False
        else:
            print(f"❌ 回调请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 回调测试异常: {str(e)}")
        return False

def check_dependencies():
    """检查依赖库"""
    print(f"\n📦 检查依赖库...")
    
    try:
        import Crypto
        print("✅ pycryptodome 已安装")
        
        from Crypto.PublicKey import RSA
        from Crypto.Signature import PKCS1_v1_5
        from Crypto.Hash import SHA256
        print("✅ 签名验证相关模块可用")
        
        return True
        
    except ImportError as e:
        print(f"❌ 依赖库缺失: {str(e)}")
        print("请安装: pip install pycryptodome")
        return False

def main():
    """主函数"""
    print("🚀 支付宝签名验证测试")
    print("此测试将验证新的签名验证实现是否正确")
    
    # 测试结果
    results = []
    
    # 1. 检查依赖库
    results.append(("依赖库检查", check_dependencies()))
    
    # 2. 测试签名验证服务
    results.append(("签名验证服务", test_signature_verification_service()))
    
    # 3. 测试回调处理
    results.append(("回调处理", test_callback_with_real_signature()))
    
    # 显示测试结果
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("-" * 40)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} - {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    # 给出建议
    print(f"\n💡 测试总结:")
    if success_count == len(results):
        print("🎉 签名验证完全正常！")
        print("\n✨ 验证效果:")
        print("- ✅ 依赖库正确安装")
        print("- ✅ 签名验证算法正确")
        print("- ✅ 真实回调数据验证通过")
        print("- ✅ 回调处理流程正常")
        
        print(f"\n🎯 现在可以:")
        print("1. 正确验证支付宝回调签名")
        print("2. 安全处理支付通知")
        print("3. 防止伪造回调攻击")
        print("4. 确保支付数据真实性")
        
    elif success_count >= 2:
        print("🎉 签名验证基本正常！")
        print("\n✨ 部分功能正常:")
        print("- 签名验证算法实现正确")
        print("- 可能个别测试数据问题")
        
    else:
        print("⚠️  签名验证仍有问题:")
        if results[0][1] == False:
            print("- 请先安装依赖: pip install pycryptodome")
        else:
            print("- 检查支付宝公钥配置")
            print("- 查看服务器日志详细错误")
    
    print(f"\n📋 关于签名验证:")
    print("- 使用标准的RSA+SHA256算法")
    print("- 按照支付宝官方文档实现")
    print("- 需要正确的支付宝公钥")
    print("- 验证通过才会处理回调")
    
    print(f"\n🔧 如果仍有问题:")
    print("1. 确认支付宝公钥格式正确")
    print("2. 检查公钥是否是最新的")
    print("3. 验证APP_ID是否匹配")
    print("4. 查看详细的错误日志")
    
    return success_count >= 2

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
