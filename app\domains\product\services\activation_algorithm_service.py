from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
from app.models.activation_algorithm import ActivationAlgorithm
from app.schemas.activation_algorithm import ActivationAlgorithmCreate, ActivationAlgorithmUpdate
import logging
import hashlib
import hmac
import time
import base64
import re

logger = logging.getLogger(__name__)

class ActivationAlgorithmService:
    """激活码算法管理服务"""
    
    def create_algorithm(
        self, 
        db: Session, 
        algorithm_data: ActivationAlgorithmCreate
    ) -> Optional[ActivationAlgorithm]:
        """创建新算法"""
        try:
            # 如果设置为默认算法，先取消其他默认算法
            if algorithm_data.is_default:
                db.query(ActivationAlgorithm).filter(
                    ActivationAlgorithm.is_default == True
                ).update({"is_default": False})
            
            # 创建新算法
            algorithm = ActivationAlgorithm(
                name=algorithm_data.name,
                description=algorithm_data.description,
                algorithm_code=algorithm_data.algorithm_code,
                is_active=algorithm_data.is_active,
                is_default=algorithm_data.is_default,
                version=algorithm_data.version,
                author=algorithm_data.author
            )
            
            db.add(algorithm)
            db.commit()
            db.refresh(algorithm)
            
            logger.info(f"创建算法成功: {algorithm.name}")
            return algorithm
            
        except Exception as e:
            logger.error(f"创建算法失败: {e}")
            db.rollback()
            return None
    
    def get_algorithms(
        self, 
        db: Session, 
        skip: int = 0, 
        limit: int = 20,
        name: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Dict[str, Any]:
        """获取算法列表"""
        try:
            query = db.query(ActivationAlgorithm)
            
            # 筛选条件
            if name:
                query = query.filter(ActivationAlgorithm.name.contains(name))
            if is_active is not None:
                query = query.filter(ActivationAlgorithm.is_active == is_active)
            
            # 获取总数
            total = query.count()
            
            # 分页查询
            algorithms = query.order_by(
                desc(ActivationAlgorithm.is_default),
                desc(ActivationAlgorithm.created_at)
            ).offset(skip).limit(limit).all()
            
            return {
                "items": algorithms,
                "total": total,
                "skip": skip,
                "limit": limit
            }
            
        except Exception as e:
            logger.error(f"获取算法列表失败: {e}")
            return {
                "items": [],
                "total": 0,
                "skip": skip,
                "limit": limit
            }
    
    def get_algorithm_by_id(self, db: Session, algorithm_id: int) -> Optional[ActivationAlgorithm]:
        """根据ID获取算法"""
        try:
            return db.query(ActivationAlgorithm).filter(
                ActivationAlgorithm.id == algorithm_id
            ).first()
        except Exception as e:
            logger.error(f"获取算法失败: {e}")
            return None
    
    def update_algorithm(
        self, 
        db: Session, 
        algorithm_id: int, 
        algorithm_data: ActivationAlgorithmUpdate
    ) -> Optional[ActivationAlgorithm]:
        """更新算法"""
        try:
            algorithm = self.get_algorithm_by_id(db, algorithm_id)
            if not algorithm:
                return None
            
            # 如果设置为默认算法，先取消其他默认算法
            if algorithm_data.is_default and not algorithm.is_default:
                db.query(ActivationAlgorithm).filter(
                    and_(
                        ActivationAlgorithm.is_default == True,
                        ActivationAlgorithm.id != algorithm_id
                    )
                ).update({"is_default": False})
            
            # 更新字段
            update_data = algorithm_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(algorithm, field, value)
            
            db.commit()
            db.refresh(algorithm)
            
            logger.info(f"更新算法成功: {algorithm.name}")
            return algorithm
            
        except Exception as e:
            logger.error(f"更新算法失败: {e}")
            db.rollback()
            return None
    
    def delete_algorithm(self, db: Session, algorithm_id: int) -> bool:
        """删除算法"""
        try:
            algorithm = self.get_algorithm_by_id(db, algorithm_id)
            if not algorithm:
                return False
            
            # 不能删除默认算法
            if algorithm.is_default:
                logger.warning(f"不能删除默认算法: {algorithm.name}")
                return False
            
            db.delete(algorithm)
            db.commit()
            
            logger.info(f"删除算法成功: {algorithm.name}")
            return True
            
        except Exception as e:
            logger.error(f"删除算法失败: {e}")
            db.rollback()
            return False
    
    def get_active_algorithms(self, db: Session) -> List[ActivationAlgorithm]:
        """获取所有启用的算法"""
        try:
            return db.query(ActivationAlgorithm).filter(
                ActivationAlgorithm.is_active == True
            ).order_by(
                desc(ActivationAlgorithm.is_default),
                ActivationAlgorithm.name
            ).all()
        except Exception as e:
            logger.error(f"获取启用算法失败: {e}")
            return []
    
    def get_default_algorithm(self, db: Session) -> Optional[ActivationAlgorithm]:
        """获取默认算法"""
        try:
            return db.query(ActivationAlgorithm).filter(
                and_(
                    ActivationAlgorithm.is_active == True,
                    ActivationAlgorithm.is_default == True
                )
            ).first()
        except Exception as e:
            logger.error(f"获取默认算法失败: {e}")
            return None
    
    def execute_algorithm(
        self, 
        algorithm: ActivationAlgorithm, 
        machine_id: str, 
        days: int = 30
    ) -> Optional[str]:
        """执行算法生成激活码"""
        try:
            # 创建安全的执行环境
            safe_globals = {
                '__builtins__': {
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'list': list,
                    'dict': dict,
                    'tuple': tuple,
                    'range': range,
                    'enumerate': enumerate,
                    'zip': zip,
                    'min': min,
                    'max': max,
                    'sum': sum,
                    'abs': abs,
                    'round': round,
                    # 不提供__import__，所有需要的模块都预先导入到全局环境中
                },
                'hashlib': hashlib,
                'hmac': hmac,
                'time': time,
                'base64': base64,
                're': re,
            }
            
            # 创建局部变量
            local_vars = {
                'machine_id': machine_id,
                'days': days,
                'activation_code': None
            }
            
            # 执行算法代码
            exec(algorithm.algorithm_code, safe_globals, local_vars)
            
            # 返回生成的激活码
            return local_vars.get('activation_code')
            
        except Exception as e:
            logger.error(f"执行算法失败: {e}")
            return None
    
    def validate_algorithm_code(self, algorithm_code: str) -> Dict[str, Any]:
        """验证算法代码"""
        try:
            # 基本语法检查
            compile(algorithm_code, '<string>', 'exec')
            
            # 检查必要的变量
            if 'activation_code' not in algorithm_code:
                return {
                    "valid": False,
                    "error": "算法代码必须设置 activation_code 变量"
                }
            
            # 检查危险操作
            dangerous_keywords = [
                'import os', 'import sys', 'import subprocess',
                'open(', 'file(', 'exec(', 'eval(',
                '__import__', 'globals()', 'locals()',
                'setattr', 'getattr', 'delattr',
                'import socket', 'import urllib', 'import requests'
            ]
            
            for keyword in dangerous_keywords:
                if keyword in algorithm_code:
                    return {
                        "valid": False,
                        "error": f"算法代码包含危险操作: {keyword}"
                    }
            
            return {"valid": True, "error": None}
            
        except SyntaxError as e:
            return {
                "valid": False,
                "error": f"语法错误: {str(e)}"
            }
        except Exception as e:
            return {
                "valid": False,
                "error": f"验证失败: {str(e)}"
            }
