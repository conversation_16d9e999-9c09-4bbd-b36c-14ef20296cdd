# 支付宝沙箱配置指南

## 🏖️ 沙箱环境配置

支付宝沙箱环境是专门用于开发和测试的环境，可以模拟真实的支付流程而不产生真实的资金流动。

## 🔧 配置步骤

### 1. 获取沙箱应用信息

1. **访问支付宝开放平台**
   - 地址：https://open.alipay.com/
   - 登录您的支付宝账号

2. **进入沙箱环境**
   - 点击"开发者中心"
   - 选择"研发服务"
   - 点击"沙箱环境"

3. **获取沙箱应用信息**
   - 沙箱应用会自动创建
   - 记录以下信息：
     - APPID（沙箱应用ID）
     - 应用私钥
     - 支付宝公钥

### 2. 生成密钥对

1. **下载密钥生成工具**
   - 访问：https://opendocs.alipay.com/common/02kipl
   - 下载"支付宝开放平台开发助手"

2. **生成RSA2密钥**
   - 打开开发助手
   - 选择"生成密钥"
   - 密钥格式：RSA2
   - 密钥长度：2048

3. **配置密钥**
   - 复制应用公钥到沙箱应用设置
   - 保存应用私钥（用于配置）
   - 获取支付宝公钥（用于验签）

### 3. 配置环境变量

编辑项目根目录的 `.env` 文件：

```env
# 支付宝沙箱配置
ALIPAY_APP_ID=你的沙箱APPID
ALIPAY_APP_PRIVATE_KEY=你的应用私钥
ALIPAY_PUBLIC_KEY=支付宝公钥
ALIPAY_GATEWAY_URL=https://openapi-sandbox.dl.alipaydev.com/gateway.do
ALIPAY_NOTIFY_URL=http://localhost:8008/api/payment/alipay/notify
ALIPAY_RETURN_URL=http://localhost:8008/user/payment

# 开启调试模式
DEBUG=True
```

### 4. 密钥格式说明

**应用私钥格式**（去掉头尾和换行符）：
```
原始格式：
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
...
-----END PRIVATE KEY-----

配置格式（去掉头尾和换行符）：
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
```

**支付宝公钥格式**（去掉头尾和换行符）：
```
原始格式：
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
...
-----END PUBLIC KEY-----

配置格式（去掉头尾和换行符）：
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
```

## 🧪 沙箱测试

### 1. 沙箱账号

支付宝会为您提供测试账号：

**买家账号**：
- 账号：沙箱提供的测试账号
- 密码：沙箱提供的测试密码
- 支付密码：沙箱提供的支付密码

**商家账号**：
- 账号：您的沙箱应用对应的商家账号
- 可以查看交易记录

### 2. 测试流程

#### 扫码支付测试
1. 启动应用：`python run.py`
2. 访问：http://localhost:8008/user/payment
3. 选择产品，点击"立即购买"
4. 选择"扫码支付"
5. 使用支付宝APP扫描二维码
6. 使用沙箱买家账号登录并支付

#### 订单码支付测试
1. 在支付页面选择"订单码支付"
2. 使用支付宝APP生成付款码
3. 输入18位付款码数字
4. 完成支付测试

### 3. 验证配置

运行配置测试：

```bash
# 测试支付宝配置
python test_simple_alipay.py
```

预期输出：
```
✅ AlipayService 导入成功，SDK可用: True
使用支付宝沙箱环境: https://openapi-sandbox.dl.alipaydev.com/gateway.do
✅ AlipayService 初始化成功
✅ 当面付测试: True
```

## 🔍 常见问题

### Q1: 沙箱应用在哪里找？
**答案**：
- 登录支付宝开放平台
- 进入"开发者中心" → "研发服务" → "沙箱环境"
- 沙箱应用会自动创建

### Q2: 密钥格式错误怎么办？
**答案**：
- 确保去掉 `-----BEGIN PRIVATE KEY-----` 和 `-----END PRIVATE KEY-----`
- 去掉所有换行符，保持为一行
- 检查是否有多余的空格

### Q3: 网关地址配置错误？
**答案**：
- 沙箱环境：`https://openapi-sandbox.dl.alipaydev.com/gateway.do`
- 生产环境：`https://openapi.alipay.com/gateway.do`
- 确保URL完整且正确

### Q4: 签名验证失败？
**答案**：
- 检查应用私钥是否正确
- 确认支付宝公钥是否是最新的
- 验证密钥格式是否正确

### Q5: 沙箱账号在哪里获取？
**答案**：
- 在沙箱环境页面可以看到测试账号
- 包括买家账号和商家账号
- 账号信息会定期更新

## 📋 配置检查清单

- [ ] 已获取沙箱APPID
- [ ] 已生成RSA2密钥对
- [ ] 已在沙箱应用中配置应用公钥
- [ ] 已获取支付宝公钥
- [ ] 已正确配置.env文件
- [ ] 密钥格式已验证（去掉头尾和换行符）
- [ ] 网关地址已配置为沙箱地址
- [ ] 已获取沙箱测试账号
- [ ] 已运行配置测试脚本
- [ ] 已测试扫码支付流程

## 🚀 完整配置示例

```env
# 支付宝沙箱完整配置示例
ALIPAY_APP_ID=2021000000000000
ALIPAY_APP_PRIVATE_KEY=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
ALIPAY_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
ALIPAY_GATEWAY_URL=https://openapi-sandbox.dl.alipaydev.com/gateway.do
ALIPAY_NOTIFY_URL=http://localhost:8008/api/payment/alipay/notify
ALIPAY_RETURN_URL=http://localhost:8008/user/payment
DEBUG=True
```

## 🎯 测试建议

1. **先在沙箱环境充分测试**
2. **验证所有支付流程**
3. **测试异常情况处理**
4. **确认回调通知正常**
5. **验证订单状态更新**

配置完成后，您就可以在沙箱环境中进行真实的支付流程测试了！

## 📞 技术支持

如遇问题，请检查：
1. 支付宝开放平台文档
2. 沙箱环境配置页面
3. 系统日志输出
4. 网络连接状态

**沙箱环境配置完成后，您就可以进行真实的支付测试了！** 🎉
