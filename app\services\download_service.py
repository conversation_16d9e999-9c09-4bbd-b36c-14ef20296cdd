from sqlalchemy.orm import Session
from app.models.user import User
from app.models.download_log import DownloadLog
from typing import List, Optional
import os
import logging

logger = logging.getLogger(__name__)

class DownloadService:
    @staticmethod
    def verify_download_permission(db: Session, user_id: str, license_key: str) -> bool:
        """验证下载权限"""
        try:
            user = db.query(User).filter(
                User.user_id == user_id,
                User.license_key == license_key,
                User.is_active == True
            ).first()
            
            return user is not None
            
        except Exception as e:
            logger.error(f"Error verifying download permission: {str(e)}")
            return False
    
    @staticmethod
    def get_file_path(filename: str, downloads_dir: str) -> Optional[str]:
        """获取文件路径"""
        file_path = os.path.join(downloads_dir, filename)
        
        # 安全检查：确保文件在下载目录内
        if not os.path.abspath(file_path).startswith(os.path.abspath(downloads_dir)):
            logger.warning(f"Potential path traversal attempt: {filename}")
            return None
            
        if os.path.exists(file_path) and os.path.isfile(file_path):
            return file_path
        else:
            logger.warning(f"File not found: {file_path}")
            return None
    
    @staticmethod
    def log_download(db: Session, user_id: str, license_key: str, filename: str, 
                    ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> bool:
        """记录下载行为"""
        try:
            download_log = DownloadLog(
                user_id=user_id,
                license_key=license_key,
                file_name=filename,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.add(download_log)
            db.commit()
            
            logger.info(f"Download logged: user={user_id}, file={filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error logging download: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def get_download_stats(db: Session, limit: int = 100) -> List[DownloadLog]:
        """获取下载统计"""
        try:
            return db.query(DownloadLog).order_by(DownloadLog.download_time.desc()).limit(limit).all()
        except Exception as e:
            logger.error(f"Error getting download stats: {str(e)}")
            return []
