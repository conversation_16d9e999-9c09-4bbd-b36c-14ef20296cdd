#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FocuSee激活码生成器 - 完整重写版本
用于为用户生成机器绑定的激活码，包含完整的日志显示功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import tkinter.font as tkFont
import hashlib
import hmac
import time
import base64
from datetime import datetime
import platform

class ActivationCodeGenerator:
    def __init__(self):
        self.master_key = "FocuSee_Master_2025_Secret_Key_V1"
        self.hmac_key = hashlib.sha256((self.master_key + "_HMAC").encode()).digest()[:32]

    def generate_activation_code(self, machine_id, days=30):
        """生成激活码"""
        try:
            current_time = int(time.time())
            machine_short = machine_id.upper().strip()[:8]
            time_short = str(current_time)[-4:]

            # 构造数据：机器码|天数|时间戳
            compact_data = f"{machine_short}|{days}|{time_short}"

            # 计算校验码
            hmac_hash = hmac.new(self.hmac_key, compact_data.encode(), hashlib.sha256).hexdigest()[:8]

            # 最终数据
            final_data = compact_data + "|" + hmac_hash

            # Base64编码并格式化
            encoded = base64.b64encode(final_data.encode()).decode().rstrip('=')
            formatted_code = '-'.join([encoded[i:i+4] for i in range(0, len(encoded), 4)])

            return formatted_code

        except Exception as e:
            print(f"生成激活码失败: {e}")
            return None

class GeneratorApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FocuSee激活码生成器")
        self.root.geometry("600x700")
        self.root.resizable(True, True)

        # 设置中文字体
        self.setup_fonts()

        self.generator = ActivationCodeGenerator()
        self.current_code = ""  # 保存当前生成的激活码

        self.setup_ui()
        self.center_window()

    def setup_fonts(self):
        """设置中文字体"""
        try:
            # 尝试不同的中文字体
            chinese_fonts = ["微软雅黑", "SimHei", "黑体", "宋体", "Arial Unicode MS"]

            self.default_font = None
            self.title_font = None
            self.code_font = None
            self.log_font = None

            # 查找可用的中文字体
            available_fonts = tkFont.families()

            for font_name in chinese_fonts:
                if font_name in available_fonts:
                    self.default_font = (font_name, 10)
                    self.title_font = (font_name, 16, "bold")
                    self.code_font = (font_name, 12, "bold")
                    self.log_font = (font_name, 9)
                    break

            # 如果没有找到中文字体，使用系统默认字体
            if self.default_font is None:
                if platform.system() == "Windows":
                    self.default_font = ("Microsoft YaHei", 10)
                    self.title_font = ("Microsoft YaHei", 16, "bold")
                    self.code_font = ("Microsoft YaHei", 12, "bold")
                    self.log_font = ("Microsoft YaHei", 9)
                else:
                    self.default_font = ("Arial", 10)
                    self.title_font = ("Arial", 16, "bold")
                    self.code_font = ("Arial", 12, "bold")
                    self.log_font = ("Arial", 9)

        except Exception as e:
            print(f"字体设置失败: {e}")
            # 使用默认字体
            self.default_font = ("Arial", 10)
            self.title_font = ("Arial", 16, "bold")
            self.code_font = ("Arial", 12, "bold")
            self.log_font = ("Arial", 9)

    def center_window(self):
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")

    def setup_ui(self):
        # 主容器
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(main_frame, text="FocuSee激活码生成器",
                              font=self.title_font, fg="blue")
        title_label.pack(pady=(0, 20))

        # 输入区域
        input_frame = tk.LabelFrame(main_frame, text="输入信息", padx=15, pady=15, font=self.default_font)
        input_frame.pack(fill=tk.X, pady=(0, 15))

        # 机器码输入
        tk.Label(input_frame, text="用户机器码:", font=self.default_font).pack(anchor=tk.W)
        self.machine_entry = tk.Entry(input_frame, font=self.default_font, width=30)
        self.machine_entry.pack(fill=tk.X, pady=(5, 10))

        # 有效期输入
        days_frame = tk.Frame(input_frame)
        days_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(days_frame, text="有效期:", font=self.default_font).pack(side=tk.LEFT)
        self.days_var = tk.StringVar(value="30")
        days_entry = tk.Entry(days_frame, textvariable=self.days_var, width=8, font=self.default_font)
        days_entry.pack(side=tk.LEFT, padx=(10, 5))
        tk.Label(days_frame, text="天", font=self.default_font).pack(side=tk.LEFT)

        # 快捷按钮
        tk.Button(days_frame, text="7天", command=lambda: self.days_var.set("7"), width=4, font=self.default_font).pack(side=tk.LEFT, padx=(20, 5))
        tk.Button(days_frame, text="30天", command=lambda: self.days_var.set("30"), width=4, font=self.default_font).pack(side=tk.LEFT, padx=(0, 5))
        tk.Button(days_frame, text="365天", command=lambda: self.days_var.set("365"), width=5, font=self.default_font).pack(side=tk.LEFT)

        # 生成按钮
        generate_btn = tk.Button(input_frame, text="生成激活码", command=self.generate_code,
                               bg="green", fg="white", font=self.code_font, height=2)
        generate_btn.pack(pady=15)

        # 生成结果区域
        result_frame = tk.LabelFrame(main_frame, text="生成结果", padx=15, pady=15, font=self.default_font)
        result_frame.pack(fill=tk.X, pady=(0, 15))

        # 激活码显示标签
        tk.Label(result_frame, text="激活码:", font=self.default_font).pack(anchor=tk.W)

        # 激活码显示框 - 使用Text控件，更可靠
        code_frame = tk.Frame(result_frame)
        code_frame.pack(fill=tk.X, pady=(5, 10))

        self.code_text = tk.Text(code_frame, height=3, font=self.code_font,
                                bg="lightyellow", fg="darkblue", wrap=tk.WORD,
                                relief=tk.SUNKEN, bd=2)
        self.code_text.pack(fill=tk.X)

        # 插入初始提示
        self.code_text.insert("1.0", "点击上方'生成激活码'按钮后，激活码将显示在这里...")
        self.code_text.config(state=tk.DISABLED)

        # 复制按钮
        copy_btn = tk.Button(result_frame, text="复制激活码", command=self.copy_code,
                           bg="blue", fg="white", font=self.default_font)
        copy_btn.pack(pady=(0, 10))

        # 操作日志区域
        log_frame = tk.LabelFrame(main_frame, text="操作日志", padx=15, pady=15, font=self.default_font)
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, font=self.log_font,
                                                 bg="white", fg="black", wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 添加初始日志
        self.log_message("激活码生成器已启动")
        self.log_message("请输入用户机器码和有效期，然后点击生成按钮")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

        # 强制更新界面
        self.root.update_idletasks()

    def generate_code(self):
        """生成激活码"""
        machine_id = self.machine_entry.get().strip()
        if not machine_id:
            self.log_message("错误: 请输入用户机器码")
            messagebox.showerror("错误", "请输入用户机器码")
            return

        try:
            days = int(self.days_var.get())
            if days <= 0:
                raise ValueError("天数必须大于0")
        except ValueError:
            self.log_message("错误: 请输入有效的天数")
            messagebox.showerror("错误", "请输入有效的天数")
            return

        self.log_message(f"开始生成激活码 - 机器码: {machine_id.upper()}, 有效期: {days}天")

        # 生成激活码
        activation_code = self.generator.generate_activation_code(machine_id, days)

        if activation_code:
            # 保存当前激活码
            self.current_code = activation_code

            # 显示激活码
            self.code_text.config(state=tk.NORMAL)
            self.code_text.delete("1.0", tk.END)

            # 插入激活码内容
            display_text = f"生成的激活码:\n{activation_code}\n\n长度: {len(activation_code)} 字符"
            self.code_text.insert("1.0", display_text)
            self.code_text.config(state=tk.DISABLED)

            # 计算过期时间
            expire_time = time.time() + (days * 24 * 60 * 60)
            expire_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(expire_time))

            # 记录详细日志
            self.log_message(f"激活码生成成功!")
            self.log_message(f"  机器码: {machine_id.upper()}")
            self.log_message(f"  有效期: {days} 天")
            self.log_message(f"  过期时间: {expire_date}")
            self.log_message(f"  激活码: {activation_code}")
            self.log_message(f"  激活码长度: {len(activation_code)} 字符")
            self.log_message("请将激活码发送给用户使用")
            self.log_message("-" * 50)

            # 显示成功消息
            messagebox.showinfo("生成成功", f"激活码生成成功!\n\n激活码: {activation_code}\n\n有效期: {days}天\n过期时间: {expire_date}")

        else:
            self.log_message("错误: 生成激活码失败")
            messagebox.showerror("错误", "生成激活码失败")

    def copy_code(self):
        """复制激活码到剪贴板"""
        if self.current_code:
            try:
                self.root.clipboard_clear()
                self.root.clipboard_append(self.current_code)
                self.log_message(f"激活码已复制到剪贴板: {self.current_code}")
                messagebox.showinfo("复制成功", f"激活码已复制到剪贴板:\n{self.current_code}")
            except Exception as e:
                self.log_message(f"复制失败: {e}")
                messagebox.showerror("复制失败", "无法复制到剪贴板")
        else:
            self.log_message("没有可复制的激活码")
            messagebox.showwarning("提示", "请先生成激活码")

    def run(self):
        self.root.mainloop()

def main():
    app = GeneratorApp()
    app.run()

if __name__ == "__main__":
    main()