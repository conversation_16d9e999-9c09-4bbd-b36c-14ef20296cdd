from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from typing import Optional
import logging

from app.database import get_db
from app.schemas.download import VerifyRequest, DownloadStatsRequest, VerifyResponse
from app.services.auth_service import AuthService
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()

# 新的请求模型
class OrderVerifyRequest(BaseModel):
    order_number: str
    user_id: str
    product_code: Optional[str] = None

class LicenseVerifyRequest(BaseModel):
    license_code: str
    user_id: str
    product_code: Optional[str] = None
    consume_quota: bool = False

class ProductVerifyRequest(BaseModel):
    user_id: str
    product_code: str

class MixedVerifyRequest(BaseModel):
    user_id: str
    license_code: Optional[str] = None
    order_number: Optional[str] = None
    product_code: Optional[str] = None
    consume_quota: bool = False

class QuotaConsumeRequest(BaseModel):
    license_code: str
    user_id: str
    product_code: Optional[str] = None

def get_client_info(request: Request):
    """获取客户端信息"""
    return {
        "ip_address": request.client.host,
        "user_agent": request.headers.get("user-agent")
    }

@router.post("/verify", response_model=VerifyResponse)
async def verify_user(
    request: VerifyRequest,
    http_request: Request,
    db: Session = Depends(get_db)
):
    """用户验证接口"""
    try:
        # 获取客户端IP
        client_ip = http_request.client.host
        if not request.ip_address:
            request.ip_address = client_ip
        
        # 验证用户
        is_valid = AuthService.verify_user(db, request)
        
        if is_valid:
            return VerifyResponse(status="success")
        else:
            return VerifyResponse(status="Invalid user ID or license key")
            
    except Exception as e:
        logger.error(f"Error in verify_user: {str(e)}")
        return VerifyResponse(status=f"Server error: {str(e)}")

@router.post("/download_stats")
async def log_download_stats(
    request: DownloadStatsRequest,
    http_request: Request,
    db: Session = Depends(get_db)
):
    """下载统计接口"""
    try:
        # 获取客户端IP和User-Agent
        client_ip = http_request.client.host
        user_agent = http_request.headers.get("user-agent")
        
        if not request.ip_address:
            request.ip_address = client_ip
        
        # 记录下载统计
        success = AuthService.log_download_stats(db, request, user_agent)
        
        if success:
            return {"status": "success"}
        else:
            raise HTTPException(status_code=500, detail="Failed to log download stats")
            
    except Exception as e:
        logger.error(f"Error in log_download_stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")

@router.post("/verify-order")
async def verify_by_order(
    request_data: OrderVerifyRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """通过订单号验证授权"""
    try:
        client_info = get_client_info(request)
        auth_service = AuthService(db)

        success, result = auth_service.verify_by_order_number(
            order_number=request_data.order_number,
            user_id=request_data.user_id,
            product_code=request_data.product_code,
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"]
        )

        if success:
            return {
                "success": True,
                "message": "订单验证成功",
                "data": result
            }
        else:
            return {
                "success": False,
                "message": result.get("error", "验证失败"),
                "data": None
            }

    except Exception as e:
        logger.error(f"Error in verify_by_order: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.post("/verify-license")
async def verify_by_license(
    request_data: LicenseVerifyRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """通过授权码验证授权"""
    try:
        client_info = get_client_info(request)
        auth_service = AuthService(db)

        success, result = auth_service.verify_by_license_code(
            license_code=request_data.license_code,
            user_id=request_data.user_id,
            product_code=request_data.product_code,
            consume_quota=request_data.consume_quota,
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"]
        )

        if success:
            return {
                "success": True,
                "message": "授权码验证成功",
                "data": result
            }
        else:
            return {
                "success": False,
                "message": result.get("error", "验证失败"),
                "data": None
            }

    except Exception as e:
        logger.error(f"Error in verify_by_license: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.post("/verify-product")
async def verify_by_product(
    request_data: ProductVerifyRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """通过产品类型验证授权"""
    try:
        client_info = get_client_info(request)
        auth_service = AuthService(db)

        success, result = auth_service.verify_by_product_type(
            user_id=request_data.user_id,
            product_code=request_data.product_code,
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"]
        )

        if success:
            return {
                "success": True,
                "message": "产品授权验证成功",
                "data": result
            }
        else:
            return {
                "success": False,
                "message": result.get("error", "验证失败"),
                "data": None
            }

    except Exception as e:
        logger.error(f"Error in verify_by_product: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.post("/verify-mixed")
async def verify_mixed(
    request_data: MixedVerifyRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """混合授权验证"""
    try:
        client_info = get_client_info(request)
        auth_service = AuthService(db)

        success, result = auth_service.verify_mixed_auth(
            user_id=request_data.user_id,
            license_code=request_data.license_code,
            order_number=request_data.order_number,
            product_code=request_data.product_code,
            consume_quota=request_data.consume_quota,
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"]
        )

        if success:
            return {
                "success": True,
                "message": "混合授权验证成功",
                "data": result
            }
        else:
            return {
                "success": False,
                "message": result.get("error", "验证失败"),
                "data": None
            }

    except Exception as e:
        logger.error(f"Error in verify_mixed: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.post("/consume-quota")
async def consume_quota(
    request_data: QuotaConsumeRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """消费API配额"""
    try:
        client_info = get_client_info(request)
        auth_service = AuthService(db)

        success, result = auth_service.verify_by_license_code(
            license_code=request_data.license_code,
            user_id=request_data.user_id,
            product_code=request_data.product_code,
            consume_quota=True,  # 强制消费配额
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"]
        )

        if success:
            return {
                "success": True,
                "message": "配额消费成功",
                "data": {
                    "license_code": result["license_code"],
                    "used_api_calls": result["used_api_calls"],
                    "remaining_calls": result["remaining_calls"],
                    "max_api_calls": result["max_api_calls"]
                }
            }
        else:
            return {
                "success": False,
                "message": result.get("error", "配额消费失败"),
                "data": None
            }

    except Exception as e:
        logger.error(f"Error in consume_quota: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.get("/license/{license_code}/status")
async def get_license_status(
    license_code: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """获取授权码状态"""
    try:
        from app.models import License

        license_obj = db.query(License).filter(License.license_code == license_code).first()
        if not license_obj:
            return {
                "success": False,
                "message": "授权码不存在",
                "data": None
            }

        return {
            "success": True,
            "message": "获取状态成功",
            "data": {
                "license_code": license_obj.license_code,
                "status": license_obj.status.value,
                "product": {
                    "id": license_obj.product.id,
                    "name": license_obj.product.name,
                    "code": license_obj.product.code
                },
                "user_id": license_obj.user_id,
                "max_api_calls": license_obj.max_api_calls,
                "used_api_calls": license_obj.used_api_calls,
                "remaining_calls": license_obj.max_api_calls - license_obj.used_api_calls if license_obj.max_api_calls > 0 else -1,
                "expire_date": license_obj.expire_date.isoformat() if license_obj.expire_date else None,
                "created_at": license_obj.created_at.isoformat() if license_obj.created_at else None
            }
        }

    except Exception as e:
        logger.error(f"Error in get_license_status: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
