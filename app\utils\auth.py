from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.database import get_db
from app.services.admin_service import AdminService
from app.services.agent_service import AgentService
from app.models.admin_user import AdminUser
from app.models.agent import Agent
from app.models.user_profile import UserProfile
import logging

logger = logging.getLogger(__name__)

security = HTTPBearer()

class AuthUser:
    """认证用户信息"""
    def __init__(self, user_id: int, username: str, user_type: str, user_data: dict = None):
        self.user_id = user_id
        self.username = username
        self.user_type = user_type  # 'admin', 'agent', 'user'
        self.user_data = user_data or {}

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> AuthUser:
    """获取当前认证用户（支持管理员、代理商、普通用户）"""
    try:
        # 验证token
        payload = AdminService.verify_access_token(credentials.credentials)
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        username = payload.get("sub")
        if not username:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 判断用户类型并获取用户信息
        if username.startswith("agent:"):
            # 代理商用户
            agent_username = username.replace("agent:", "")
            agent = db.query(Agent).filter(Agent.username == agent_username).first()
            if not agent or not agent.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Agent not found or inactive"
                )
            return AuthUser(
                user_id=agent.id,
                username=agent.username,
                user_type="agent",
                user_data={
                    "company_name": agent.company_name,
                    "contact_name": agent.contact_name,
                    "contact_email": agent.contact_email
                }
            )
        
        elif username.startswith("user:"):
            # 普通用户
            user_username = username.replace("user:", "")
            user = db.query(UserProfile).filter(UserProfile.username == user_username).first()
            if not user or not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found or inactive"
                )
            return AuthUser(
                user_id=user.id,
                username=user.username,
                user_type="user",
                user_data={
                    "email": user.email,
                    "phone": user.phone,
                    "full_name": user.full_name,
                    "user_type": user.user_type
                }
            )
        
        else:
            # 管理员用户
            admin = db.query(AdminUser).filter(AdminUser.username == username).first()
            if not admin or not admin.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Admin not found or inactive"
                )
            return AuthUser(
                user_id=admin.id,
                username=admin.username,
                user_type="admin",
                user_data={
                    "email": admin.email,
                    "role": admin.role
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_current_user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_admin(current_user: AuthUser = Depends(get_current_user)) -> AuthUser:
    """获取当前管理员用户"""
    if current_user.user_type != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user

async def get_current_agent(current_user: AuthUser = Depends(get_current_user)) -> AuthUser:
    """获取当前代理商用户"""
    if current_user.user_type != "agent":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Agent access required"
        )
    return current_user

async def get_current_regular_user(current_user: AuthUser = Depends(get_current_user)) -> AuthUser:
    """获取当前普通用户"""
    if current_user.user_type != "user":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User access required"
        )
    return current_user

async def get_admin_or_agent(current_user: AuthUser = Depends(get_current_user)) -> AuthUser:
    """获取管理员或代理商用户"""
    if current_user.user_type not in ["admin", "agent"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin or agent access required"
        )
    return current_user
