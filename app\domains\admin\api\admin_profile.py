from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional
from app.database import get_db
from app.models.admin_user import AdminUser
from app.services.admin_service import AdminService

router = APIRouter()

class AdminProfileUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    full_name: Optional[str] = None

class AdminPasswordUpdate(BaseModel):
    current_password: str
    new_password: str

class AdminProfileResponse(BaseModel):
    id: int
    username: str
    email: Optional[str] = None
    phone: Optional[str] = None
    full_name: Optional[str] = None
    is_active: bool
    
    class Config:
        from_attributes = True

@router.put("/profile", response_model=AdminProfileResponse)
async def update_admin_profile(
    profile_data: AdminProfileUpdate,
    db: Session = Depends(get_db)
):
    """更新管理员个人信息"""
    try:
        # TODO: 从session中获取当前管理员ID
        # 这里暂时使用默认管理员
        admin = db.query(AdminUser).filter(AdminUser.username == "admin").first()
        if not admin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Admin not found"
            )
        
        # 更新字段
        update_data = profile_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(admin, field):
                setattr(admin, field, value)
        
        db.commit()
        db.refresh(admin)
        
        return admin
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update profile: {str(e)}"
        )

@router.put("/password")
async def update_admin_password(
    password_data: AdminPasswordUpdate,
    db: Session = Depends(get_db)
):
    """修改管理员密码"""
    try:
        # TODO: 从session中获取当前管理员ID
        # 这里暂时使用默认管理员
        admin = db.query(AdminUser).filter(AdminUser.username == "admin").first()
        if not admin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Admin not found"
            )
        
        # 验证当前密码
        if not AdminService.verify_password(password_data.current_password, admin.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # 更新密码
        admin.password_hash = AdminService.get_password_hash(password_data.new_password)
        db.commit()
        
        return {"success": True, "message": "Password updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update password: {str(e)}"
        )
