from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc
from app.models.machine_activation import MachineActivation
# 延迟导入避免循环依赖
import logging
import hashlib
import hmac
import time
import base64
from datetime import datetime

logger = logging.getLogger(__name__)

class MachineActivationService:
    """机器码激活码生成服务"""
    
    def __init__(self):
        # 使用与activation_code_generator.py相同的密钥
        self.master_key = "FocuSee_Master_2025_Secret_Key_V1"
        self.hmac_key = hashlib.sha256((self.master_key + "_HMAC").encode()).digest()[:32]
    
    def generate_activation_code(self, machine_id: str, days: int = 30) -> Optional[str]:
        """
        生成激活码
        基于activation_code_generator.py的逻辑
        """
        try:
            current_time = int(time.time())
            machine_short = machine_id.upper().strip()[:8]
            time_short = str(current_time)[-4:]

            # 构造数据：机器码|天数|时间戳
            compact_data = f"{machine_short}|{days}|{time_short}"

            # 计算校验码
            hmac_hash = hmac.new(self.hmac_key, compact_data.encode(), hashlib.sha256).hexdigest()[:8]

            # 最终数据
            final_data = compact_data + "|" + hmac_hash

            # Base64编码并格式化
            encoded = base64.b64encode(final_data.encode()).decode().rstrip('=')
            formatted_code = '-'.join([encoded[i:i+4] for i in range(0, len(encoded), 4)])

            return formatted_code

        except Exception as e:
            logger.error(f"生成激活码失败: {e}")
            return None
    
    def create_machine_activation(
        self,
        db: Session,
        machine_id: str,
        days: int = 30,
        notes: Optional[str] = None,
        algorithm_id: Optional[int] = None
    ) -> Optional[MachineActivation]:
        """
        创建机器码激活码记录
        """
        try:
            # 生成激活码 - 暂时使用内置算法，避免循环导入
            activation_code = self.generate_activation_code(machine_id, days)

            if not activation_code:
                logger.error("激活码生成失败")
                return None
            
            # 计算过期时间戳
            current_time = int(time.time())
            expire_timestamp = current_time + (days * 24 * 60 * 60)
            
            # 创建记录
            machine_activation = MachineActivation(
                machine_id=machine_id.upper().strip(),
                activation_code=activation_code,
                days=days,
                expire_timestamp=expire_timestamp,
                generation_timestamp=current_time,
                notes=notes
            )
            
            db.add(machine_activation)
            db.commit()
            db.refresh(machine_activation)
            
            logger.info(f"机器码激活码记录创建成功: {machine_activation.id}")
            return machine_activation
            
        except Exception as e:
            logger.error(f"创建机器码激活码记录失败: {e}")
            db.rollback()
            return None
    
    def get_machine_activations(
        self, 
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        machine_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取机器码激活码记录列表
        """
        try:
            query = db.query(MachineActivation)
            
            # 按机器码筛选
            if machine_id:
                query = query.filter(MachineActivation.machine_id.like(f"%{machine_id.upper()}%"))
            
            # 获取总数
            total = query.count()
            
            # 分页查询
            activations = query.order_by(desc(MachineActivation.created_at)).offset(skip).limit(limit).all()
            
            return {
                "items": activations,
                "total": total,
                "skip": skip,
                "limit": limit
            }
            
        except Exception as e:
            logger.error(f"获取机器码激活码记录失败: {e}")
            return {
                "items": [],
                "total": 0,
                "skip": skip,
                "limit": limit
            }
    
    def get_machine_activation_by_id(self, db: Session, activation_id: int) -> Optional[MachineActivation]:
        """
        根据ID获取机器码激活码记录
        """
        try:
            return db.query(MachineActivation).filter(MachineActivation.id == activation_id).first()
        except Exception as e:
            logger.error(f"获取机器码激活码记录失败: {e}")
            return None
    
    def update_machine_activation(
        self, 
        db: Session, 
        activation_id: int, 
        notes: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Optional[MachineActivation]:
        """
        更新机器码激活码记录
        """
        try:
            activation = db.query(MachineActivation).filter(MachineActivation.id == activation_id).first()
            if not activation:
                logger.error(f"机器码激活码记录不存在: {activation_id}")
                return None
            
            if notes is not None:
                activation.notes = notes
            if is_active is not None:
                activation.is_active = is_active
            
            db.commit()
            db.refresh(activation)
            
            logger.info(f"机器码激活码记录更新成功: {activation_id}")
            return activation
            
        except Exception as e:
            logger.error(f"更新机器码激活码记录失败: {e}")
            db.rollback()
            return None
    
    def delete_machine_activation(self, db: Session, activation_id: int) -> bool:
        """
        删除机器码激活码记录
        """
        try:
            activation = db.query(MachineActivation).filter(MachineActivation.id == activation_id).first()
            if not activation:
                logger.error(f"机器码激活码记录不存在: {activation_id}")
                return False
            
            db.delete(activation)
            db.commit()
            
            logger.info(f"机器码激活码记录删除成功: {activation_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除机器码激活码记录失败: {e}")
            db.rollback()
            return False
    
    def get_statistics(self, db: Session) -> Dict[str, Any]:
        """
        获取统计信息
        """
        try:
            total_count = db.query(MachineActivation).count()
            active_count = db.query(MachineActivation).filter(MachineActivation.is_active == True).count()
            
            # 获取当前时间戳
            current_timestamp = int(time.time())
            
            # 统计未过期的激活码
            valid_count = db.query(MachineActivation).filter(
                MachineActivation.is_active == True,
                MachineActivation.expire_timestamp > current_timestamp
            ).count()
            
            # 统计已过期的激活码
            expired_count = db.query(MachineActivation).filter(
                MachineActivation.expire_timestamp <= current_timestamp
            ).count()
            
            return {
                "total_count": total_count,
                "active_count": active_count,
                "valid_count": valid_count,
                "expired_count": expired_count
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                "total_count": 0,
                "active_count": 0,
                "valid_count": 0,
                "expired_count": 0
            }
