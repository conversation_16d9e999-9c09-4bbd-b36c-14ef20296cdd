from sqlalchemy.orm import Session
from app.models.admin_user import AdminUser
from app.models.user import User
from app.models.download_log import DownloadLog
from app.schemas.user import UserCreate, UserUpdate
from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional, List
import logging

logger = logging.getLogger(__name__)

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AdminService:
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希"""
        return pwd_context.hash(password)
    
    @staticmethod
    def authenticate_admin(db: Session, username: str, password: str) -> Optional[AdminUser]:
        """管理员认证"""
        admin = db.query(AdminUser).filter(AdminUser.username == username).first()
        if not admin:
            return None
        if not AdminService.verify_password(password, admin.password_hash):
            return None
        return admin
    
    @staticmethod
    def create_access_token(data: dict, secret_key: str, algorithm: str, expires_delta: Optional[timedelta] = None):
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=15)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=algorithm)
        return encoded_jwt

    @staticmethod
    def verify_access_token(token: str):
        """验证访问令牌"""
        try:
            from app.config import settings
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            return payload
        except jwt.PyJWTError:
            return None
    
    @staticmethod
    def create_admin_user(db: Session, username: str, password: str) -> AdminUser:
        """创建管理员用户"""
        hashed_password = AdminService.get_password_hash(password)
        admin_user = AdminUser(username=username, password_hash=hashed_password)
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        return admin_user
    
    @staticmethod
    def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[User]:
        """获取用户列表"""
        return db.query(User).offset(skip).limit(limit).all()
    
    @staticmethod
    def create_user(db: Session, user: UserCreate) -> User:
        """创建用户"""
        user_data = user.dict()

        # 处理密码哈希
        if user_data.get('password'):
            user_data['password_hash'] = AdminService.get_password_hash(user_data.pop('password'))

        db_user = User(**user_data)
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user
    
    @staticmethod
    def update_user(db: Session, user_id: str, user_update: UserUpdate) -> Optional[User]:
        """更新用户"""
        db_user = db.query(User).filter(User.user_id == user_id).first()
        if db_user:
            update_data = user_update.dict(exclude_unset=True)

            # 处理密码更新
            if 'password' in update_data and update_data['password']:
                update_data['password_hash'] = AdminService.get_password_hash(update_data.pop('password'))

            for field, value in update_data.items():
                setattr(db_user, field, value)
            db.commit()
            db.refresh(db_user)
        return db_user
    
    @staticmethod
    def delete_user(db: Session, user_id: str) -> bool:
        """删除用户"""
        db_user = db.query(User).filter(User.user_id == user_id).first()
        if db_user:
            db.delete(db_user)
            db.commit()
            return True
        return False
