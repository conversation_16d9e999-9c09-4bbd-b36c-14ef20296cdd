from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.schemas.product import ProductCreate, ProductUpdate, ProductResponse
from app.services.product_service import ProductService
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
async def create_product(
    product_data: ProductCreate,
    db: Session = Depends(get_db)
    # TODO: 添加权限检查
):
    """创建产品"""
    # TODO: 检查权限
    # permission_checker.require_permission(current_user_id, "product.create")
    
    product = ProductService.create_product(db, product_data)
    if not product:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to create product. Product code may already exist."
        )
    
    return product

@router.get("/", response_model=List[ProductResponse])
async def get_products(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_active: Optional[bool] = Query(None),
    keyword: Optional[str] = Query(None),
    db: Session = Depends(get_db)
    # TODO: 添加权限检查
):
    """获取产品列表"""
    # TODO: 检查权限
    # permission_checker.require_permission(current_user_id, "product.view")
    
    if keyword:
        products = ProductService.search_products(db, keyword, skip, limit)
    else:
        products = ProductService.get_products(db, skip, limit, is_active)
    
    return products

@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: int,
    db: Session = Depends(get_db)
    # TODO: 添加权限检查
):
    """获取单个产品"""
    # TODO: 检查权限
    # permission_checker.require_permission(current_user_id, "product.view")
    
    product = ProductService.get_product_by_id(db, product_id)
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
    
    return product

@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: int,
    product_data: ProductUpdate,
    db: Session = Depends(get_db)
):
    """更新产品"""
    # TODO: 检查权限

    product = ProductService.update_product(db, product_id, product_data)
    if not product:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update product. Product may not exist or code conflicts."
        )

    return product

@router.delete("/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_product(
    product_id: int,
    db: Session = Depends(get_db)
):
    """删除产品"""
    # TODO: 检查权限

    success = ProductService.delete_product(db, product_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )

@router.post("/{product_id}/toggle-status", response_model=ProductResponse)
async def toggle_product_status(
    product_id: int,
    db: Session = Depends(get_db)
):
    """切换产品状态（启用/禁用）"""
    product = ProductService.get_product_by_id(db, product_id)
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )

    # 切换状态
    product.is_active = not product.is_active
    db.commit()
    db.refresh(product)

    return product

@router.get("/stats/count")
async def get_product_count(
    is_active: Optional[bool] = Query(None),
    db: Session = Depends(get_db)
):
    """获取产品统计"""
    # TODO: 检查权限

    count = ProductService.get_product_count(db, is_active)
    return {"count": count}
