from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from app.models.license import LicenseStatus

class LicenseBase(BaseModel):
    license_code: str
    product_id: int
    max_api_calls: int = -1
    expire_date: Optional[datetime] = None
    notes: Optional[str] = None

class LicenseCreate(BaseModel):
    license_code: Optional[str] = None
    product_id: int
    max_api_calls: int = -1
    expire_date: Optional[datetime] = None
    notes: Optional[str] = None
    order_id: Optional[int] = None
    agent_id: Optional[int] = None
    user_id: Optional[str] = None
    status: LicenseStatus = LicenseStatus.INACTIVE

class LicenseUpdate(BaseModel):
    product_id: Optional[int] = None
    user_id: Optional[str] = None
    max_api_calls: Optional[int] = None
    expire_date: Optional[datetime] = None
    status: Optional[LicenseStatus] = None
    notes: Optional[str] = None

class LicenseResponse(LicenseBase):
    id: int
    order_id: Optional[int] = None
    agent_id: Optional[int] = None
    user_id: Optional[str] = None
    used_api_calls: int
    status: LicenseStatus
    device_info: Optional[str] = None
    activated_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class LicenseVerifyRequest(BaseModel):
    license_code: str
    user_id: Optional[str] = None
    device_info: Optional[str] = None

class LicenseActivateRequest(BaseModel):
    license_code: str
    user_id: str
    device_info: Optional[str] = None
