from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class UserApiKey(Base):
    __tablename__ = "user_api_keys"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(100), nullable=False, comment="用户ID")
    name = Column(String(100), nullable=False, comment="密钥名称")
    key_hash = Column(String(255), nullable=False, unique=True, comment="API密钥哈希")
    key_prefix = Column(String(20), nullable=False, comment="密钥前缀（用于显示）")
    description = Column(Text, comment="密钥描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    last_used = Column(DateTime(timezone=True), comment="最后使用时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<UserApiKey(user_id='{self.user_id}', name='{self.name}', is_active={self.is_active})>"
