#!/usr/bin/env python3
"""
FocuSee Registration System 启动脚本
"""

import uvicorn
import os
from app.config import settings

def main():
    """启动应用"""
    print("=" * 50)
    print("FocuSee Registration System")
    print("=" * 50)
    print(f"App Name: {settings.app_name}")
    print(f"Version: {settings.app_version}")
    print(f"Debug Mode: {settings.debug}")
    print("=" * 50)

    # 检查环境变量
    if not os.path.exists(".env"):
        print("Warning: .env file not found. Using default settings.")
        print("Please create .env file and configure your database settings.")

    # 创建下载目录
    os.makedirs(settings.downloads_dir, exist_ok=True)
    print(f"Downloads directory: {os.path.abspath(settings.downloads_dir)}")

    print("\nStarting server...")
    print("Access the admin panel at: http://localhost:8008")
    print("Default admin credentials:")
    print(f"  Username: {settings.admin_username}")
    print(f"  Password: {settings.admin_password}")
    print("\nAPI Endpoints:")
    print("  POST /api/verify - User verification")
    print("  POST /api/download_stats - Download statistics")
    print("  GET /downloads/{filename} - File download")
    print("=" * 50)

    # 启动服务器
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8008,
        reload=settings.debug,
        log_level="debug"
    )

if __name__ == "__main__":
    main()
