import json
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.payment_order import PaymentOrder, PaymentMethod, PaymentStatus
from app.models.payment_log import PaymentLog, PaymentLogType
from app.models.product import Product
from app.models.agent import Agent
from app.services.alipay_service import AlipayService
from app.utils.logger import setup_logging

logger = setup_logging()

class PaymentService:
    """支付服务"""
    
    def __init__(self):
        self.alipay_service = AlipayService()
    
    @staticmethod
    def generate_order_no() -> str:
        """生成支付订单号"""
        import time
        import random
        timestamp = str(int(time.time()))
        random_num = str(random.randint(1000, 9999))
        return f"PAY{timestamp}{random_num}"
    
    def create_face_to_face_payment(self,
                                  db: Session,
                                  user_id: str,
                                  product_id: int,
                                  amount: Optional[float] = None,
                                  agent_id: Optional[int] = None,
                                  timeout_minutes: int = 30) -> Dict[str, Any]:
        """
        创建当面付支付订单
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            product_id: 产品ID
            agent_id: 代理商ID（可选）
            timeout_minutes: 超时时间（分钟）
            
        Returns:
            创建结果
        """
        try:
            logger.info(f"开始创建当面付支付订单 - 用户: {user_id}, 产品: {product_id}, 代理: {agent_id}, 超时: {timeout_minutes}分钟")

            # 获取产品信息
            product = db.query(Product).filter(Product.id == product_id).first()
            if not product:
                logger.warning(f"产品不存在 - 产品ID: {product_id}")
                return {"success": False, "error": "产品不存在"}

            if not product.is_active:
                logger.warning(f"产品已下架 - 产品ID: {product_id}, 产品名: {product.name}")
                return {"success": False, "error": "产品已下架"}

            logger.info(f"产品信息 - ID: {product.id}, 名称: {product.name}, 价格: {product.price}")

            # 生成订单号
            order_no = self.generate_order_no()
            logger.info(f"生成订单号: {order_no}")
            
            # 创建支付订单记录
            # 使用传入的金额，如果没有传入则使用产品价格
            final_amount = amount if amount is not None else product.price

            payment_order = PaymentOrder(
                order_no=order_no,
                user_id=user_id,
                product_id=product_id,
                agent_id=agent_id,
                payment_method=PaymentMethod.ALIPAY_FACE_TO_FACE,
                amount=final_amount,
                currency="CNY",
                status=PaymentStatus.PENDING,
                subject=f"购买{product.name}",
                body=f"用户{user_id}购买{product.name}",
                expire_time=datetime.now() + timedelta(minutes=timeout_minutes),
                alipay_out_trade_no=order_no
            )
            
            db.add(payment_order)
            db.commit()
            db.refresh(payment_order)
            
            # 调用支付宝当面付接口
            alipay_result = self.alipay_service.create_face_to_face_payment(
                out_trade_no=order_no,
                total_amount=float(product.price),
                subject=payment_order.subject,
                body=payment_order.body,
                timeout_express=f"{timeout_minutes}m"
            )
            
            # 记录日志
            self._log_payment_operation(
                db, payment_order.id, PaymentLogType.CREATE,
                request_data={"product_id": product_id, "user_id": user_id},
                response_data=alipay_result,
                is_success="Y" if alipay_result.get("success") else "N"
            )
            
            if alipay_result.get("success"):
                # 更新支付订单
                payment_order.alipay_qr_code = alipay_result.get("qr_code")
                db.commit()
                
                return {
                    "success": True,
                    "order_no": order_no,
                    "qr_code": alipay_result.get("qr_code"),
                    "qr_image": alipay_result.get("qr_image"),
                    "amount": float(final_amount),
                    "expire_time": payment_order.expire_time.isoformat(),
                    "product_name": product.name
                }
            else:
                # 更新订单状态为失败
                payment_order.status = PaymentStatus.FAILED
                db.commit()
                
                return {
                    "success": False,
                    "error": alipay_result.get("error", "创建支付订单失败")
                }
                
        except Exception as e:
            logger.error(f"创建当面付支付订单失败: {str(e)}")
            db.rollback()
            return {"success": False, "error": f"创建支付订单失败: {str(e)}"}
    
    def create_order_code_payment(self,
                                db: Session,
                                user_id: str,
                                product_id: int,
                                auth_code: str,
                                agent_id: Optional[int] = None) -> Dict[str, Any]:
        """
        创建订单码支付
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            product_id: 产品ID
            auth_code: 支付授权码
            agent_id: 代理商ID（可选）
            
        Returns:
            支付结果
        """
        try:
            # 获取产品信息
            product = db.query(Product).filter(Product.id == product_id).first()
            if not product:
                return {"success": False, "error": "产品不存在"}
            
            if not product.is_active:
                return {"success": False, "error": "产品已下架"}
            
            # 生成订单号
            order_no = self.generate_order_no()
            
            # 创建支付订单记录
            payment_order = PaymentOrder(
                order_no=order_no,
                user_id=user_id,
                product_id=product_id,
                agent_id=agent_id,
                payment_method=PaymentMethod.ALIPAY_ORDER_CODE,
                amount=product.price,
                currency="CNY",
                status=PaymentStatus.PENDING,
                subject=f"购买{product.name}",
                body=f"用户{user_id}购买{product.name}",
                alipay_out_trade_no=order_no,
                alipay_order_code=auth_code
            )
            
            db.add(payment_order)
            db.commit()
            db.refresh(payment_order)
            
            # 调用支付宝订单码支付接口
            alipay_result = self.alipay_service.create_order_code_payment(
                out_trade_no=order_no,
                total_amount=float(product.price),
                subject=payment_order.subject,
                auth_code=auth_code,
                body=payment_order.body
            )
            
            # 记录日志
            self._log_payment_operation(
                db, payment_order.id, PaymentLogType.CREATE,
                request_data={"product_id": product_id, "user_id": user_id, "auth_code": auth_code},
                response_data=alipay_result,
                is_success="Y" if alipay_result.get("success") else "N"
            )
            
            if alipay_result.get("success"):
                # 支付成功，更新订单状态
                payment_order.status = PaymentStatus.PAID
                payment_order.alipay_trade_no = alipay_result.get("trade_no")
                payment_order.paid_at = datetime.now()
                db.commit()
                
                return {
                    "success": True,
                    "order_no": order_no,
                    "trade_no": alipay_result.get("trade_no"),
                    "amount": float(product.price),
                    "product_name": product.name,
                    "paid_at": payment_order.paid_at.isoformat()
                }
            else:
                # 支付失败，更新订单状态
                payment_order.status = PaymentStatus.FAILED
                db.commit()
                
                return {
                    "success": False,
                    "error": alipay_result.get("error", "支付失败")
                }
                
        except Exception as e:
            logger.error(f"订单码支付失败: {str(e)}")
            db.rollback()
            return {"success": False, "error": f"支付失败: {str(e)}"}
    
    def query_payment_status(self, db: Session, order_no: str) -> Dict[str, Any]:
        """
        查询支付状态

        Args:
            db: 数据库会话
            order_no: 订单号

        Returns:
            支付状态
        """
        try:
            logger.info(f"开始查询支付状态 - 订单号: {order_no}")

            # 查询支付订单
            payment_order = db.query(PaymentOrder).filter(
                PaymentOrder.order_no == order_no
            ).first()

            if not payment_order:
                logger.warning(f"订单不存在 - 订单号: {order_no}")
                return {"success": False, "error": "订单不存在"}

            logger.info(f"找到订单 - ID: {payment_order.id}, 状态: {payment_order.status.value}, 用户: {payment_order.user_id}")
            
            # 如果订单已经是最终状态，直接返回
            if payment_order.status in [PaymentStatus.PAID, PaymentStatus.CANCELLED,
                                      PaymentStatus.EXPIRED, PaymentStatus.REFUNDED]:
                logger.info(f"订单已是最终状态 - 状态: {payment_order.status.value}, 直接返回")
                return {
                    "success": True,
                    "order_no": order_no,
                    "status": payment_order.status.value,
                    "amount": float(payment_order.amount),
                    "paid_at": payment_order.paid_at.isoformat() if payment_order.paid_at else None
                }
            
            # 查询支付宝支付状态
            logger.info(f"调用支付宝查询接口 - 订单号: {order_no}, 支付宝交易号: {payment_order.alipay_trade_no}")
            alipay_result = self.alipay_service.query_payment_status(
                out_trade_no=order_no,
                trade_no=payment_order.alipay_trade_no
            )
            logger.info(f"支付宝查询结果: {alipay_result}")
            
            # 记录查询日志
            self._log_payment_operation(
                db, payment_order.id, PaymentLogType.QUERY,
                request_data={"order_no": order_no},
                response_data=alipay_result,
                is_success="Y" if alipay_result.get("success") else "N"
            )
            
            if alipay_result.get("success"):
                trade_status = alipay_result.get("trade_status")
                logger.info(f"支付宝查询成功 - 交易状态: {trade_status}")

                # 更新订单状态
                old_status = payment_order.status.value
                if trade_status == "TRADE_SUCCESS":
                    payment_order.status = PaymentStatus.PAID
                    payment_order.alipay_trade_no = alipay_result.get("trade_no")
                    payment_order.paid_at = datetime.now()
                    logger.info(f"订单状态更新: {old_status} -> PAID")
                elif trade_status == "TRADE_CLOSED":
                    payment_order.status = PaymentStatus.CANCELLED
                    logger.info(f"订单状态更新: {old_status} -> CANCELLED")
                elif trade_status == "TRADE_FINISHED":
                    payment_order.status = PaymentStatus.PAID
                    payment_order.alipay_trade_no = alipay_result.get("trade_no")
                    payment_order.paid_at = datetime.now()
                    logger.info(f"订单状态更新: {old_status} -> PAID (FINISHED)")
                else:
                    logger.info(f"交易状态无需更新订单状态: {trade_status}")

                try:
                    db.commit()
                    logger.info("订单状态更新已提交到数据库")
                except Exception as commit_error:
                    logger.error(f"提交订单状态更新失败: {str(commit_error)}")
                    db.rollback()
                    raise

                return {
                    "success": True,
                    "order_no": order_no,
                    "status": payment_order.status.value,
                    "trade_status": trade_status,
                    "amount": float(payment_order.amount),
                    "paid_at": payment_order.paid_at.isoformat() if payment_order.paid_at else None,
                    "alipay_trade_no": payment_order.alipay_trade_no
                }
            else:
                error_msg = alipay_result.get("error", "查询失败")
                logger.warning(f"支付宝查询失败: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            logger.error(f"查询支付状态异常 - 订单号: {order_no}, 错误: {str(e)}")
            logger.exception("查询支付状态异常详情:")
            db.rollback()
            return {"success": False, "error": f"查询失败: {str(e)}"}
    
    def handle_alipay_notify(self, db: Session, notify_data: Dict[str, Any]) -> bool:
        """
        处理支付宝异步通知
        
        Args:
            db: 数据库会话
            notify_data: 通知数据
            
        Returns:
            处理结果
        """
        try:
            # 验证通知签名
            if not self.alipay_service.verify_notify(notify_data):
                logger.warning("支付宝通知签名验证失败")
                return False
            
            out_trade_no = notify_data.get("out_trade_no")
            trade_status = notify_data.get("trade_status")
            trade_no = notify_data.get("trade_no")
            
            # 查询支付订单
            payment_order = db.query(PaymentOrder).filter(
                PaymentOrder.order_no == out_trade_no
            ).first()
            
            if not payment_order:
                logger.warning(f"支付订单不存在: {out_trade_no}")
                return False
            
            # 记录通知日志
            self._log_payment_operation(
                db, payment_order.id, PaymentLogType.NOTIFY,
                request_data=notify_data,
                response_data={"processed": True},
                is_success="Y",
                alipay_trade_no=trade_no
            )
            
            # 更新订单状态
            if trade_status == "TRADE_SUCCESS":
                payment_order.status = PaymentStatus.PAID
                payment_order.alipay_trade_no = trade_no
                payment_order.paid_at = datetime.now()
            elif trade_status == "TRADE_CLOSED":
                payment_order.status = PaymentStatus.CANCELLED
            elif trade_status == "TRADE_FINISHED":
                payment_order.status = PaymentStatus.PAID
            
            db.commit()
            
            logger.info(f"支付宝通知处理成功: {out_trade_no}, 状态: {trade_status}")
            return True
            
        except Exception as e:
            logger.error(f"处理支付宝通知失败: {str(e)}")
            db.rollback()
            return False
    
    def _log_payment_operation(self,
                             db: Session,
                             payment_order_id: int,
                             log_type: PaymentLogType,
                             request_data: Optional[Dict] = None,
                             response_data: Optional[Dict] = None,
                             is_success: str = "Y",
                             alipay_trade_no: Optional[str] = None,
                             error_code: Optional[str] = None,
                             error_message: Optional[str] = None,
                             ip_address: Optional[str] = None,
                             user_agent: Optional[str] = None):
        """记录支付操作日志"""
        try:
            payment_log = PaymentLog(
                payment_order_id=payment_order_id,
                log_type=log_type,
                request_data=json.dumps(request_data, ensure_ascii=False) if request_data else None,
                response_data=json.dumps(response_data, ensure_ascii=False) if response_data else None,
                alipay_trade_no=alipay_trade_no,
                is_success=is_success,
                error_code=error_code,
                error_message=error_message,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.add(payment_log)
            db.commit()
            
        except Exception as e:
            logger.error(f"记录支付日志失败: {str(e)}")
    
    def get_user_payment_orders(self, db: Session, user_id: str, 
                              skip: int = 0, limit: int = 20) -> List[PaymentOrder]:
        """获取用户的支付订单列表"""
        return db.query(PaymentOrder).filter(
            PaymentOrder.user_id == user_id
        ).order_by(PaymentOrder.created_at.desc()).offset(skip).limit(limit).all()
