# FocuSee授权管理系统 - 快速启动指南

## 系统概述

FocuSee授权管理系统是一个完整的软件授权管理平台，支持三种用户角色：
- **管理员**: 系统管理、产品管理、代理商管理
- **代理商**: 授权码分发、订单管理
- **普通用户**: 查看个人授权、API使用统计

## 快速启动

### 1. 启动应用
```bash
python run.py
```

应用将在 http://localhost:8008 启动

### 2. 访问不同角色的界面

#### 管理员界面
- **登录页面**: http://localhost:8008/
- **默认账户**: admin / admin123
- **功能**: 
  - 产品管理: http://localhost:8008/admin/products
  - 订单管理: http://localhost:8008/admin/orders
  - 授权码管理: http://localhost:8008/admin/licenses
  - 代理商管理: http://localhost:8008/admin/agents
  - 系统监控: http://localhost:8008/admin/system

#### 代理商界面
- **登录页面**: http://localhost:8008/agent/login
- **测试账户**: 需要管理员创建
- **功能**:
  - 仪表板: http://localhost:8008/agent/dashboard
  - 授权码分发: http://localhost:8008/agent/licenses

#### 用户界面
- **登录/注册页面**: http://localhost:8008/user/login
- **功能**:
  - 用户仪表板: http://localhost:8008/user/dashboard
  - 查看个人授权和API使用统计

### 3. API文档
- **Swagger文档**: http://localhost:8008/docs
- **ReDoc文档**: http://localhost:8008/redoc

## 基本使用流程

### 管理员操作流程

1. **登录管理员账户**
   - 访问 http://localhost:8008/
   - 使用 admin / admin123 登录

2. **创建产品**
   - 进入产品管理页面
   - 点击"添加产品"
   - 填写产品信息（名称、代码、描述等）

3. **创建代理商**
   - 进入代理商管理页面
   - 点击"添加代理商"
   - 填写代理商信息（用户名、公司名称、联系方式等）

4. **为代理商分配产品授权**
   - 在代理商列表中点击"产品授权"
   - 点击"添加授权"
   - 选择产品、设置最大授权数和过期时间

### 代理商操作流程

1. **登录代理商账户**
   - 访问 http://localhost:8008/agent/login
   - 使用管理员创建的代理商账户登录

2. **查看授权产品**
   - 在仪表板查看已授权的产品
   - 查看授权使用情况

3. **生成授权码**
   - 进入授权码管理页面
   - 点击"生成授权码"
   - 选择产品、填写用户ID、设置API限制等
   - 生成的授权码可以分发给最终用户

### 用户操作流程

1. **注册/登录用户账户**
   - 访问 http://localhost:8008/user/login
   - 注册新账户或使用现有账户登录

2. **查看个人授权**
   - 在仪表板查看拥有的授权码
   - 查看API使用统计

## API使用示例

### 授权码验证API
```bash
# 验证授权码
curl -X POST "http://localhost:8008/api/licenses/verify" \
  -H "Content-Type: application/json" \
  -d '{
    "license_code": "FS-PRO-2024-ABCD1234",
    "user_id": "user123",
    "device_info": "Windows 10"
  }'
```

### 授权码激活API
```bash
# 激活授权码
curl -X POST "http://localhost:8008/api/licenses/activate" \
  -H "Content-Type: application/json" \
  -d '{
    "license_code": "FS-PRO-2024-ABCD1234",
    "user_id": "user123",
    "device_info": "Windows 10"
  }'
```

### 订单验证API
```bash
# 验证订单
curl -X POST "http://localhost:8008/api/orders/verify" \
  -H "Content-Type: application/json" \
  -d '{
    "order_number": "ORD-2024-001",
    "user_id": "user123"
  }'
```

## 数据库初始化

系统启动时会自动创建以下初始数据：

### 默认角色
- **admin**: 管理员角色，拥有所有权限
- **agent**: 代理商角色，可以管理授权码和订单
- **user**: 普通用户角色，只能查看个人信息

### 默认管理员账户
- **用户名**: admin
- **密码**: admin123
- **角色**: 管理员

### 示例产品
- **FocuSee Pro**: 专业版产品
- **FocuSee Basic**: 基础版产品

## 常见问题

### Q: 如何重置管理员密码？
A: 可以通过数据库直接修改，或者修改 `migrate.py` 文件中的初始化代码重新运行迁移。

### Q: 如何添加新的权限？
A: 在 `app/utils/permissions.py` 文件中添加新的权限定义，然后在相应的API接口中使用。

### Q: 如何自定义授权码格式？
A: 修改 `app/services/license_service.py` 文件中的 `generate_license_code` 方法。

### Q: 如何配置邮件通知？
A: 系统预留了邮件通知接口，可以在服务层中集成邮件发送功能。

### Q: 如何部署到生产环境？
A: 
1. 修改数据库配置为生产环境配置
2. 设置环境变量（SECRET_KEY等）
3. 使用 Gunicorn 或 uWSGI 部署
4. 配置 Nginx 反向代理
5. 设置 HTTPS 证书

## 技术支持

如果在使用过程中遇到问题，可以：
1. 查看系统日志
2. 访问API文档了解接口详情
3. 检查数据库连接和配置
4. 查看浏览器控制台错误信息

## 下一步开发建议

1. **添加缓存层**: 使用Redis提高性能
2. **集成消息队列**: 处理异步任务
3. **添加监控告警**: 集成Prometheus + Grafana
4. **完善测试用例**: 添加单元测试和集成测试
5. **优化前端体验**: 添加更多交互功能
6. **API限流**: 添加API调用频率限制
7. **数据备份**: 实现自动数据备份功能
