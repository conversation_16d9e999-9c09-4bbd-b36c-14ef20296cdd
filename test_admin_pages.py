#!/usr/bin/env python3
"""
测试admin页面
"""

import requests

def test_admin_pages():
    """测试admin页面"""
    print("🔍 测试admin页面...")
    
    pages = [
        ("/admin", "admin主页"),
        ("/admin/payment-debug", "支付调试页面"),
        ("/admin/products", "产品管理"),
        ("/admin/orders", "订单管理")
    ]
    
    for url, name in pages:
        try:
            response = requests.get(f"http://localhost:8008{url}", timeout=5)
            print(f"{name}: {response.status_code}")
            
            if response.status_code == 500:
                print(f"  错误内容: {response.text[:200]}...")
                
        except Exception as e:
            print(f"{name}: 请求失败 - {str(e)}")

if __name__ == "__main__":
    test_admin_pages()
