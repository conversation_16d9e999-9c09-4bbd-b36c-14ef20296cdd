from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.schemas.agent import AgentCreate, AgentUpdate, AgentResponse, AgentPasswordUpdate
from app.services.agent_service import AgentService
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=AgentResponse, status_code=status.HTTP_201_CREATED)
async def create_agent(
    agent_data: AgentCreate,
    db: Session = Depends(get_db)
):
    """创建代理商"""
    agent = AgentService.create_agent(db, agent_data)
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to create agent. Username may already exist."
        )
    
    return agent

@router.get("/", response_model=List[AgentResponse])
async def get_agents(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_active: Optional[bool] = Query(None),
    keyword: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取代理商列表"""
    if keyword:
        agents = AgentService.search_agents(db, keyword, skip, limit)
    else:
        agents = AgentService.get_agents(db, skip, limit, is_active)
    
    return agents

@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent(
    agent_id: int,
    db: Session = Depends(get_db)
):
    """获取单个代理商"""
    agent = AgentService.get_agent_by_id(db, agent_id)
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )
    
    return agent

@router.put("/{agent_id}", response_model=AgentResponse)
async def update_agent(
    agent_id: int,
    agent_data: AgentUpdate,
    db: Session = Depends(get_db)
):
    """更新代理商"""
    agent = AgentService.update_agent(db, agent_id, agent_data)
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update agent. Agent may not exist or username conflicts."
        )
    
    return agent

@router.delete("/{agent_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_agent(
    agent_id: int,
    db: Session = Depends(get_db)
):
    """删除代理商"""
    success = AgentService.delete_agent(db, agent_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )

@router.get("/stats/count")
async def get_agent_count(
    is_active: Optional[bool] = Query(None),
    db: Session = Depends(get_db)
):
    """获取代理商统计"""
    count = AgentService.get_agent_count(db, is_active)
    return {"count": count}

@router.post("/{agent_id}/toggle-status", response_model=AgentResponse)
async def toggle_agent_status(
    agent_id: int,
    db: Session = Depends(get_db)
):
    """切换代理商状态（启用/禁用）"""
    agent = AgentService.get_agent_by_id(db, agent_id)
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )

    # 切换状态
    agent.is_active = not agent.is_active
    db.commit()
    db.refresh(agent)

    logger.info(f"Agent {agent.username} status toggled to {'active' if agent.is_active else 'inactive'}")
    return agent

@router.put("/{agent_id}/password", status_code=status.HTTP_204_NO_CONTENT)
async def update_agent_password(
    agent_id: int,
    password_data: AgentPasswordUpdate,
    db: Session = Depends(get_db)
):
    """更新代理商密码"""
    success = AgentService.update_agent_password(
        db, agent_id, password_data.old_password, password_data.new_password
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update password. Agent may not exist or old password is incorrect."
        )
