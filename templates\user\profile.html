{% extends "user/base_new.html" %}

{% block title %}个人设置 - FocuSee 用户中心{% endblock %}

{% block content %}
<div class="page-title">个人设置</div>

<div class="content-container">
    <el-row :gutter="20">
        <!-- 左侧个人信息 -->
        <el-col :span="16">
            <el-card style="margin-bottom: 20px;">
                <template #header>
                    <span>基本信息</span>
                </template>
                
                <el-form 
                    :model="profileForm" 
                    :rules="profileRules"
                    ref="profileFormRef"
                    label-width="100px">
                    
                    <el-form-item label="用户名">
                        <el-input v-model="profileForm.username" disabled>
                            <template #suffix>
                                <el-text type="info" size="small">用户名不可修改</el-text>
                            </template>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="姓名" prop="full_name">
                        <el-input 
                            v-model="profileForm.full_name" 
                            placeholder="请输入真实姓名">
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="邮箱" prop="email">
                        <el-input 
                            v-model="profileForm.email" 
                            placeholder="请输入邮箱地址"
                            type="email">
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="手机号" prop="phone">
                        <el-input 
                            v-model="profileForm.phone" 
                            placeholder="请输入手机号码">
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="用户类型">
                        <el-tag :type="getUserTypeTag(profileForm.user_type)">
                            {{ "{{ getUserTypeText(profileForm.user_type) }}" }}
                        </el-tag>
                    </el-form-item>
                    
                    <el-form-item label="注册时间">
                        <el-text>{{ "{{ formatDate(profileForm.created_at) }}" }}</el-text>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button 
                            type="primary" 
                            @click="updateProfile"
                            :loading="updating">
                            保存修改
                        </el-button>
                        <el-button @click="resetProfile">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 修改密码 -->
            <el-card style="margin-bottom: 20px;">
                <template #header>
                    <span>修改密码</span>
                </template>
                
                <el-form 
                    :model="passwordForm" 
                    :rules="passwordRules"
                    ref="passwordFormRef"
                    label-width="100px">
                    
                    <el-form-item label="当前密码" prop="old_password">
                        <el-input 
                            v-model="passwordForm.old_password" 
                            type="password" 
                            placeholder="请输入当前密码"
                            show-password>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="新密码" prop="new_password">
                        <el-input 
                            v-model="passwordForm.new_password" 
                            type="password" 
                            placeholder="请输入新密码"
                            show-password>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="确认密码" prop="confirm_password">
                        <el-input 
                            v-model="passwordForm.confirm_password" 
                            type="password" 
                            placeholder="请再次输入新密码"
                            show-password>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button 
                            type="primary" 
                            @click="changePassword"
                            :loading="changingPassword">
                            修改密码
                        </el-button>
                        <el-button @click="resetPasswordForm">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- API密钥管理 -->
            <el-card>
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>API密钥管理</span>
                        <el-button type="primary" size="small" @click="generateApiKey">
                            <el-icon><Plus /></el-icon>
                            生成新密钥
                        </el-button>
                    </div>
                </template>
                
                <el-table :data="apiKeys" style="width: 100%">
                    <el-table-column prop="name" label="密钥名称" width="150">
                        <template #default="scope">
                            <el-text>{{ "{{ scope.row.name }}" }}</el-text>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="key" label="密钥" min-width="200">
                        <template #default="scope">
                            <div style="display: flex; align-items: center;">
                                <el-text 
                                    style="font-family: monospace; margin-right: 10px;"
                                    v-if="scope.row.visible">
                                    {{ "{{ scope.row.key }}" }}
                                </el-text>
                                <el-text 
                                    style="margin-right: 10px;"
                                    v-else>
                                    ••••••••••••••••••••••••••••••••
                                </el-text>
                                <el-button 
                                    size="small" 
                                    @click="toggleKeyVisibility(scope.row)"
                                    link>
                                    <el-icon>
                                        <View v-if="!scope.row.visible" />
                                        <Hide v-else />
                                    </el-icon>
                                </el-button>
                                <el-button 
                                    size="small" 
                                    @click="copyApiKey(scope.row.key)"
                                    link>
                                    <el-icon><CopyDocument /></el-icon>
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="status" label="状态" width="100">
                        <template #default="scope">
                            <el-tag 
                                :type="scope.row.is_active ? 'success' : 'danger'"
                                size="small">
                                {{ "{{ scope.row.is_active ? '启用' : '禁用' }}" }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="last_used" label="最后使用" width="150">
                        <template #default="scope">
                            <span v-if="scope.row.last_used">{{ "{{ formatDate(scope.row.last_used) }}" }}</span>
                            <el-text v-else type="info">从未使用</el-text>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="created_at" label="创建时间" width="150">
                        <template #default="scope">
                            {{ "{{ formatDate(scope.row.created_at) }}" }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="scope">
                            <el-button 
                                :type="scope.row.is_active ? 'warning' : 'success'"
                                size="small" 
                                @click="toggleApiKeyStatus(scope.row)"
                                link>
                                {{ "{{ scope.row.is_active ? '禁用' : '启用' }}" }}
                            </el-button>
                            <el-button 
                                type="danger" 
                                size="small" 
                                @click="deleteApiKey(scope.row)"
                                link>
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </el-col>

        <!-- 右侧统计信息 -->
        <el-col :span="8">
            <!-- 账户统计 -->
            <el-card style="margin-bottom: 20px;">
                <template #header>
                    <span>账户统计</span>
                </template>
                
                <div class="stat-item">
                    <div class="stat-label">授权总数</div>
                    <div class="stat-value">{{ "{{ accountStats.totalLicenses }}" }}</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-label">激活授权</div>
                    <div class="stat-value">{{ "{{ accountStats.activeLicenses }}" }}</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-label">API调用总数</div>
                    <div class="stat-value">{{ "{{ accountStats.totalApiCalls }}" }}</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-label">本月API调用</div>
                    <div class="stat-value">{{ "{{ accountStats.monthlyApiCalls }}" }}</div>
                </div>
            </el-card>

            <!-- 最近活动 -->
            <el-card style="margin-bottom: 20px;">
                <template #header>
                    <span>最近活动</span>
                </template>
                
                <el-timeline>
                    <el-timeline-item
                        v-for="activity in recentActivities"
                        :key="activity.id"
                        :timestamp="formatDate(activity.timestamp)"
                        :type="getActivityType(activity.type)">
                        {{ "{{ activity.description }}" }}
                    </el-timeline-item>
                </el-timeline>
                
                <div v-if="recentActivities.length === 0" style="text-align: center; color: #909399;">
                    暂无活动记录
                </div>
            </el-card>

            <!-- 安全设置 -->
            <el-card>
                <template #header>
                    <span>安全设置</span>
                </template>
                
                <div class="security-item">
                    <div class="security-label">
                        <el-icon><Lock /></el-icon>
                        密码强度
                    </div>
                    <div class="security-value">
                        <el-tag :type="getPasswordStrengthType()">
                            {{ "{{ getPasswordStrengthText() }}" }}
                        </el-tag>
                    </div>
                </div>
                
                <div class="security-item">
                    <div class="security-label">
                        <el-icon><Message /></el-icon>
                        邮箱验证
                    </div>
                    <div class="security-value">
                        <el-tag :type="profileForm.email ? 'success' : 'warning'">
                            {{ "{{ profileForm.email ? '已验证' : '未验证' }}" }}
                        </el-tag>
                    </div>
                </div>
                
                <div class="security-item">
                    <div class="security-label">
                        <el-icon><Phone /></el-icon>
                        手机验证
                    </div>
                    <div class="security-value">
                        <el-tag :type="profileForm.phone ? 'success' : 'warning'">
                            {{ "{{ profileForm.phone ? '已验证' : '未验证' }}" }}
                        </el-tag>
                    </div>
                </div>
                
                <div class="security-item">
                    <div class="security-label">
                        <el-icon><Calendar /></el-icon>
                        最后登录
                    </div>
                    <div class="security-value">
                        <el-text size="small">{{ "{{ formatDate(accountStats.lastLogin) }}" }}</el-text>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
</div>

<!-- 生成API密钥对话框 -->
<el-dialog v-model="showApiKeyDialog" title="生成API密钥" width="500px">
    <el-form :model="apiKeyForm" label-width="100px">
        <el-form-item label="密钥名称" required>
            <el-input 
                v-model="apiKeyForm.name" 
                placeholder="请输入密钥名称">
            </el-input>
        </el-form-item>
        <el-form-item label="描述">
            <el-input 
                v-model="apiKeyForm.description" 
                type="textarea" 
                :rows="3"
                placeholder="密钥用途描述（可选）">
            </el-input>
        </el-form-item>
    </el-form>
    <template #footer>
        <el-button @click="showApiKeyDialog = false">取消</el-button>
        <el-button type="primary" @click="handleGenerateApiKey" :loading="generatingKey">生成</el-button>
    </template>
</el-dialog>

<style scoped>
.content-container {
    max-width: 1200px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #606266;
    font-size: 14px;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
}

.security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.security-item:last-child {
    border-bottom: none;
}

.security-label {
    display: flex;
    align-items: center;
    color: #606266;
    font-size: 14px;
}

.security-label .el-icon {
    margin-right: 8px;
}

.security-value {
    text-align: right;
}
</style>
{% endblock %}

{% block data %}
profileForm: {
    username: '',
    full_name: '',
    email: '',
    phone: '',
    user_type: '',
    created_at: ''
},
originalProfile: {},
updating: false,
passwordForm: {
    old_password: '',
    new_password: '',
    confirm_password: ''
},
changingPassword: false,
apiKeys: [],
showApiKeyDialog: false,
apiKeyForm: {
    name: '',
    description: ''
},
generatingKey: false,
accountStats: {
    totalLicenses: 0,
    activeLicenses: 0,
    totalApiCalls: 0,
    monthlyApiCalls: 0,
    lastLogin: ''
},
recentActivities: [],
profileRules: {
    email: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
    phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
},
passwordRules: {
    old_password: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
    ],
    new_password: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ],
    confirm_password: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        { validator: this.validateConfirmPassword, trigger: 'blur' }
    ]
}
{% endblock %}

{% block methods %}
async loadUserProfile() {
    try {
        const response = await fetch('/api/user-auth/me');
        if (response.ok) {
            const data = await response.json();
            this.profileForm = { ...data };
            this.originalProfile = { ...data };
        }
    } catch (error) {
        console.error('加载用户信息失败:', error);
        this.$message.error('加载用户信息失败');
    }
},

async loadAccountStats() {
    try {
        const response = await fetch('/api/user-auth/stats');
        if (response.ok) {
            this.accountStats = await response.json();
        }
    } catch (error) {
        console.error('加载账户统计失败:', error);
    }
},

async loadRecentActivities() {
    try {
        const response = await fetch('/api/user-auth/activities');
        if (response.ok) {
            this.recentActivities = await response.json();
        }
    } catch (error) {
        console.error('加载活动记录失败:', error);
    }
},

async loadApiKeys() {
    try {
        const response = await fetch('/api/user-auth/api-keys');
        if (response.ok) {
            const data = await response.json();
            this.apiKeys = data.map(key => ({ ...key, visible: false }));
        }
    } catch (error) {
        console.error('加载API密钥失败:', error);
    }
},

async updateProfile() {
    try {
        await this.$refs.profileFormRef.validate();
        
        this.updating = true;
        const response = await fetch('/api/user-auth/profile', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                full_name: this.profileForm.full_name,
                email: this.profileForm.email,
                phone: this.profileForm.phone
            })
        });
        
        if (response.ok) {
            this.$message.success('个人信息更新成功');
            this.originalProfile = { ...this.profileForm };
        } else {
            const error = await response.json();
            this.$message.error(error.detail || '更新失败');
        }
    } catch (error) {
        console.error('更新个人信息失败:', error);
        this.$message.error('更新失败');
    } finally {
        this.updating = false;
    }
},

resetProfile() {
    this.profileForm = { ...this.originalProfile };
},

async changePassword() {
    try {
        await this.$refs.passwordFormRef.validate();
        
        this.changingPassword = true;
        const response = await fetch('/api/user-auth/password', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                old_password: this.passwordForm.old_password,
                new_password: this.passwordForm.new_password
            })
        });
        
        if (response.ok) {
            this.$message.success('密码修改成功');
            this.resetPasswordForm();
        } else {
            const error = await response.json();
            this.$message.error(error.detail || '密码修改失败');
        }
    } catch (error) {
        console.error('修改密码失败:', error);
        this.$message.error('修改密码失败');
    } finally {
        this.changingPassword = false;
    }
},

resetPasswordForm() {
    this.passwordForm = {
        old_password: '',
        new_password: '',
        confirm_password: ''
    };
    this.$refs.passwordFormRef?.clearValidate();
},

validateConfirmPassword(rule, value, callback) {
    if (value !== this.passwordForm.new_password) {
        callback(new Error('两次输入的密码不一致'));
    } else {
        callback();
    }
},

generateApiKey() {
    this.apiKeyForm = { name: '', description: '' };
    this.showApiKeyDialog = true;
},

async handleGenerateApiKey() {
    if (!this.apiKeyForm.name) {
        this.$message.error('请输入密钥名称');
        return;
    }
    
    this.generatingKey = true;
    try {
        const response = await fetch('/api/user-auth/api-keys', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.apiKeyForm)
        });
        
        if (response.ok) {
            this.$message.success('API密钥生成成功');
            this.showApiKeyDialog = false;
            this.loadApiKeys();
        } else {
            const error = await response.json();
            this.$message.error(error.detail || '生成失败');
        }
    } catch (error) {
        console.error('生成API密钥失败:', error);
        this.$message.error('生成失败');
    } finally {
        this.generatingKey = false;
    }
},

toggleKeyVisibility(apiKey) {
    apiKey.visible = !apiKey.visible;
},

async copyApiKey(key) {
    try {
        await navigator.clipboard.writeText(key);
        this.$message.success('API密钥已复制到剪贴板');
    } catch (error) {
        this.$message.error('复制失败');
    }
},

async toggleApiKeyStatus(apiKey) {
    try {
        const response = await fetch(`/api/user-auth/api-keys/${apiKey.id}/toggle`, {
            method: 'PUT'
        });
        
        if (response.ok) {
            apiKey.is_active = !apiKey.is_active;
            this.$message.success(`API密钥已${apiKey.is_active ? '启用' : '禁用'}`);
        } else {
            this.$message.error('操作失败');
        }
    } catch (error) {
        console.error('切换API密钥状态失败:', error);
        this.$message.error('操作失败');
    }
},

async deleteApiKey(apiKey) {
    try {
        await this.$confirm('确定要删除这个API密钥吗？', '确认删除', {
            type: 'warning'
        });
        
        const response = await fetch(`/api/user-auth/api-keys/${apiKey.id}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            this.$message.success('API密钥删除成功');
            this.loadApiKeys();
        } else {
            this.$message.error('删除失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除API密钥失败:', error);
            this.$message.error('删除失败');
        }
    }
},

getUserTypeTag(userType) {
    const tags = {
        'admin': 'danger',
        'agent': 'warning',
        'regular': 'success'
    };
    return tags[userType] || 'info';
},

getUserTypeText(userType) {
    const texts = {
        'admin': '管理员',
        'agent': '代理商',
        'regular': '普通用户'
    };
    return texts[userType] || userType;
},

getActivityType(type) {
    const types = {
        'login': 'primary',
        'license_activate': 'success',
        'api_call': 'info',
        'payment': 'warning'
    };
    return types[type] || 'primary';
},

getPasswordStrengthType() {
    // 简单的密码强度判断逻辑
    return 'success'; // 这里可以根据实际情况实现
},

getPasswordStrengthText() {
    return '中等'; // 这里可以根据实际情况实现
},

formatDate(dateStr) {
    if (!dateStr) return '';
    return new Date(dateStr).toLocaleString('zh-CN');
}
{% endblock %}

{% block mounted %}
this.loadUserProfile();
this.loadAccountStats();
this.loadRecentActivities();
this.loadApiKeys();
{% endblock %}
