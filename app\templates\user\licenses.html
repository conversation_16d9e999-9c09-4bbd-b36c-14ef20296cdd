{% extends "user/base_new.html" %}

{% block title %}我的授权 - FocuSee 用户中心{% endblock %}

{% block content %}
<div class="page-title">我的授权</div>

<div class="content-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
            <el-card class="stats-card">
                <div class="stats-content">
                    <div class="stats-icon">
                        <el-icon color="#409eff" size="24"><Key /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{{ "{{ licenseStats.total }}" }}</div>
                        <div class="stats-label">总授权数</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card class="stats-card">
                <div class="stats-content">
                    <div class="stats-icon">
                        <el-icon color="#67c23a" size="24"><CircleCheck /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{{ "{{ licenseStats.active }}" }}</div>
                        <div class="stats-label">激活授权</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card class="stats-card">
                <div class="stats-content">
                    <div class="stats-icon">
                        <el-icon color="#f56c6c" size="24"><Clock /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{{ "{{ licenseStats.expiring }}" }}</div>
                        <div class="stats-label">即将过期</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card class="stats-card">
                <div class="stats-content">
                    <div class="stats-icon">
                        <el-icon color="#e6a23c" size="24"><DataAnalysis /></el-icon>
                    </div>
                    <div class="stats-info">
                        <div class="stats-number">{{ "{{ licenseStats.totalApiCalls }}" }}</div>
                        <div class="stats-label">总API调用</div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card style="margin-bottom: 20px;">
        <el-row :gutter="20">
            <el-col :span="8">
                <el-input
                    v-model="searchForm.keyword"
                    placeholder="搜索授权码或产品名称"
                    clearable
                    @input="handleSearch">
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
            </el-col>
            <el-col :span="6">
                <el-select v-model="searchForm.status" placeholder="授权状态" clearable @change="handleSearch">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="激活" value="active"></el-option>
                    <el-option label="未激活" value="inactive"></el-option>
                    <el-option label="已过期" value="expired"></el-option>
                    <el-option label="已暂停" value="suspended"></el-option>
                </el-select>
            </el-col>
            <el-col :span="6">
                <el-select v-model="searchForm.product" placeholder="产品类型" clearable @change="handleSearch">
                    <el-option label="全部产品" value=""></el-option>
                    <el-option 
                        v-for="product in products" 
                        :key="product.id" 
                        :label="product.name" 
                        :value="product.id">
                    </el-option>
                </el-select>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="refreshData">
                    <el-icon><Refresh /></el-icon>
                    刷新
                </el-button>
            </el-col>
        </el-row>
    </el-card>

    <!-- 授权列表 -->
    <el-card>
        <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>授权列表</span>
                <el-button type="primary" size="small" @click="showActivateDialog = true">
                    <el-icon><Plus /></el-icon>
                    激活新授权
                </el-button>
            </div>
        </template>

        <el-table 
            :data="licenses" 
            v-loading="loading"
            style="width: 100%"
            @sort-change="handleSortChange">
            
            <el-table-column prop="license_code" label="授权码" width="200" show-overflow-tooltip>
                <template #default="scope">
                    <el-text type="primary" style="font-family: monospace;">
                        {{ "{{ scope.row.license_code }}" }}
                    </el-text>
                </template>
            </el-table-column>
            
            <el-table-column prop="product_name" label="产品" width="150">
                <template #default="scope">
                    <el-tag type="info">{{ "{{ scope.row.product_name }}" }}</el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                    <el-tag 
                        :type="getStatusType(scope.row.status)"
                        size="small">
                        {{ "{{ getStatusText(scope.row.status) }}" }}
                    </el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="used_api_calls" label="API使用" width="120" sortable="custom">
                <template #default="scope">
                    <div>
                        <div>{{ "{{ scope.row.used_api_calls }}" }} / {{ "{{ scope.row.max_api_calls === -1 ? '无限制' : scope.row.max_api_calls }}" }}</div>
                        <el-progress 
                            v-if="scope.row.max_api_calls !== -1"
                            :percentage="Math.round((scope.row.used_api_calls / scope.row.max_api_calls) * 100)"
                            :stroke-width="4"
                            :show-text="false"
                            :color="getProgressColor(scope.row.used_api_calls, scope.row.max_api_calls)">
                        </el-progress>
                    </div>
                </template>
            </el-table-column>
            
            <el-table-column prop="expire_date" label="过期时间" width="150" sortable="custom">
                <template #default="scope">
                    <div v-if="scope.row.expire_date">
                        <div>{{ "{{ formatDate(scope.row.expire_date) }}" }}</div>
                        <el-text 
                            :type="getExpireType(scope.row.expire_date)" 
                            size="small">
                            {{ "{{ getExpireText(scope.row.expire_date) }}" }}
                        </el-text>
                    </div>
                    <el-text v-else type="info">永久有效</el-text>
                </template>
            </el-table-column>
            
            <el-table-column prop="activated_at" label="激活时间" width="150">
                <template #default="scope">
                    <span v-if="scope.row.activated_at">{{ "{{ formatDate(scope.row.activated_at) }}" }}</span>
                    <el-text v-else type="info">未激活</el-text>
                </template>
            </el-table-column>
            
            <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                    <el-button 
                        type="primary" 
                        size="small" 
                        @click="viewLicenseDetail(scope.row)"
                        link>
                        详情
                    </el-button>
                    <el-button 
                        v-if="scope.row.status === 'inactive'"
                        type="success" 
                        size="small" 
                        @click="activateLicense(scope.row)"
                        link>
                        激活
                    </el-button>
                    <el-button 
                        v-if="scope.row.status === 'active'"
                        type="warning" 
                        size="small" 
                        @click="renewLicense(scope.row)"
                        link>
                        续期
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div style="margin-top: 20px; text-align: center;">
            <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handlePageChange">
            </el-pagination>
        </div>
    </el-card>
</div>

<!-- 激活授权对话框 -->
<el-dialog v-model="showActivateDialog" title="激活新授权" width="500px">
    <el-form :model="activateForm" label-width="100px">
        <el-form-item label="授权码">
            <el-input 
                v-model="activateForm.license_code" 
                placeholder="请输入授权码"
                style="font-family: monospace;">
            </el-input>
        </el-form-item>
        <el-form-item label="设备信息">
            <el-input 
                v-model="activateForm.device_info" 
                type="textarea" 
                :rows="3"
                placeholder="设备信息（可选）">
            </el-input>
        </el-form-item>
    </el-form>
    <template #footer>
        <el-button @click="showActivateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleActivate" :loading="activating">激活</el-button>
    </template>
</el-dialog>

<!-- 授权详情对话框 -->
<el-dialog v-model="showDetailDialog" title="授权详情" width="600px">
    <div v-if="selectedLicense">
        <el-descriptions :column="2" border>
            <el-descriptions-item label="授权码">
                <el-text style="font-family: monospace;">{{ "{{ selectedLicense.license_code }}" }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="产品">
                <el-tag>{{ "{{ selectedLicense.product_name }}" }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(selectedLicense.status)">
                    {{ "{{ getStatusText(selectedLicense.status) }}" }}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="API使用">
                {{ "{{ selectedLicense.used_api_calls }}" }} / {{ "{{ selectedLicense.max_api_calls === -1 ? '无限制' : selectedLicense.max_api_calls }}" }}
            </el-descriptions-item>
            <el-descriptions-item label="过期时间">
                <span v-if="selectedLicense.expire_date">{{ "{{ formatDate(selectedLicense.expire_date) }}" }}</span>
                <span v-else>永久有效</span>
            </el-descriptions-item>
            <el-descriptions-item label="激活时间">
                <span v-if="selectedLicense.activated_at">{{ "{{ formatDate(selectedLicense.activated_at) }}" }}</span>
                <span v-else>未激活</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
                {{ "{{ formatDate(selectedLicense.created_at) }}" }}
            </el-descriptions-item>
            <el-descriptions-item label="设备信息" :span="2">
                <pre v-if="selectedLicense.device_info">{{ "{{ selectedLicense.device_info }}" }}</pre>
                <span v-else>无</span>
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
                <span v-if="selectedLicense.notes">{{ "{{ selectedLicense.notes }}" }}</span>
                <span v-else>无</span>
            </el-descriptions-item>
        </el-descriptions>
    </div>
</el-dialog>

<style scoped>
.content-container {
    max-width: 1200px;
}

.stats-card {
    height: 100px;
}

.stats-content {
    display: flex;
    align-items: center;
    height: 100%;
}

.stats-icon {
    margin-right: 15px;
}

.stats-info {
    flex: 1;
}

.stats-number {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
    line-height: 1;
}

.stats-label {
    font-size: 14px;
    color: #909399;
    margin-top: 5px;
}
</style>
{% endblock %}

{% block data %}
licenseStats: {
    total: 0,
    active: 0,
    expiring: 0,
    totalApiCalls: 0
},
licenses: [],
products: [],
loading: false,
searchForm: {
    keyword: '',
    status: '',
    product: ''
},
pagination: {
    page: 1,
    size: 20,
    total: 0
},
showActivateDialog: false,
showDetailDialog: false,
selectedLicense: null,
activateForm: {
    license_code: '',
    device_info: ''
},
activating: false
{% endblock %}

{% block methods %}
async loadLicenseStats() {
    try {
        const response = await fetch('/api/user-auth/licenses/stats');
        if (response.ok) {
            this.licenseStats = await response.json();
        }
    } catch (error) {
        console.error('加载授权统计失败:', error);
    }
},

async loadLicenses() {
    this.loading = true;
    try {
        const params = new URLSearchParams({
            page: this.pagination.page,
            size: this.pagination.size,
            ...this.searchForm
        });
        
        const response = await fetch(`/api/user-auth/licenses?${params}`);
        if (response.ok) {
            const data = await response.json();
            this.licenses = data.licenses || [];
            this.pagination.total = data.total || 0;
        }
    } catch (error) {
        console.error('加载授权列表失败:', error);
        this.$message.error('加载授权列表失败');
    } finally {
        this.loading = false;
    }
},

async loadProducts() {
    try {
        const response = await fetch('/api/products');
        if (response.ok) {
            this.products = await response.json();
        }
    } catch (error) {
        console.error('加载产品列表失败:', error);
    }
},

handleSearch() {
    this.pagination.page = 1;
    this.loadLicenses();
},

handleSortChange(sort) {
    // 处理排序
    this.loadLicenses();
},

handlePageChange(page) {
    this.pagination.page = page;
    this.loadLicenses();
},

handleSizeChange(size) {
    this.pagination.size = size;
    this.pagination.page = 1;
    this.loadLicenses();
},

refreshData() {
    this.loadLicenseStats();
    this.loadLicenses();
},

viewLicenseDetail(license) {
    this.selectedLicense = license;
    this.showDetailDialog = true;
},

async activateLicense(license) {
    this.activateForm.license_code = license.license_code;
    this.showActivateDialog = true;
},

async handleActivate() {
    if (!this.activateForm.license_code) {
        this.$message.error('请输入授权码');
        return;
    }
    
    this.activating = true;
    try {
        const response = await fetch('/api/user-auth/licenses/activate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.activateForm)
        });
        
        if (response.ok) {
            this.$message.success('授权激活成功');
            this.showActivateDialog = false;
            this.activateForm = { license_code: '', device_info: '' };
            this.refreshData();
        } else {
            const error = await response.json();
            this.$message.error(error.detail || '激活失败');
        }
    } catch (error) {
        console.error('激活失败:', error);
        this.$message.error('激活失败');
    } finally {
        this.activating = false;
    }
},

renewLicense(license) {
    this.$message.info('续期功能开发中...');
},

getStatusType(status) {
    const types = {
        'active': 'success',
        'inactive': 'info',
        'expired': 'danger',
        'suspended': 'warning'
    };
    return types[status] || 'info';
},

getStatusText(status) {
    const texts = {
        'active': '激活',
        'inactive': '未激活',
        'expired': '已过期',
        'suspended': '已暂停'
    };
    return texts[status] || status;
},

getProgressColor(used, max) {
    if (max === -1) return '#409eff';
    const percentage = (used / max) * 100;
    if (percentage >= 90) return '#f56c6c';
    if (percentage >= 70) return '#e6a23c';
    return '#67c23a';
},

getExpireType(expireDate) {
    if (!expireDate) return 'info';
    const days = this.getDaysUntilExpire(expireDate);
    if (days < 0) return 'danger';
    if (days <= 7) return 'warning';
    return 'success';
},

getExpireText(expireDate) {
    if (!expireDate) return '';
    const days = this.getDaysUntilExpire(expireDate);
    if (days < 0) return '已过期';
    if (days === 0) return '今天过期';
    if (days <= 7) return `${days}天后过期`;
    return `${days}天后过期`;
},

getDaysUntilExpire(expireDate) {
    const expire = new Date(expireDate);
    const now = new Date();
    const diffTime = expire - now;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
},

formatDate(dateStr) {
    if (!dateStr) return '';
    return new Date(dateStr).toLocaleString('zh-CN');
}
{% endblock %}

{% block mounted %}
this.loadLicenseStats();
this.loadLicenses();
this.loadProducts();
{% endblock %}
