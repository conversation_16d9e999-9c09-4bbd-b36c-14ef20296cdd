from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum

class UserType(enum.Enum):
    REGULAR = "regular"      # 普通用户
    AGENT = "agent"          # 代理商
    ADMIN = "admin"          # 管理员

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(100), unique=True, index=True, nullable=False, comment="用户ID")
    username = Column(String(50), unique=True, index=True, nullable=True, comment="用户名（用于登录）")
    license_key = Column(String(255), nullable=True, comment="授权码（可为空）")
    user_type = Column(Enum(UserType), default=UserType.REGULAR, comment="用户类型")
    password_hash = Column(String(255), comment="密码哈希（用于登录）")
    email = Column(String(100), comment="邮箱")
    phone = Column(String(20), comment="电话")
    full_name = Column(String(100), comment="全名")
    is_active = Column(Boolean, default=True, comment="是否激活")
    description = Column(Text, comment="用户描述")
    last_login_at = Column(DateTime(timezone=True), comment="最后登录时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    user_roles = relationship("UserRole", foreign_keys="UserRole.user_id", primaryjoin="User.user_id == UserRole.user_id")

    def __repr__(self):
        return f"<User(user_id='{self.user_id}', user_type='{self.user_type.value}', is_active={self.is_active})>"
