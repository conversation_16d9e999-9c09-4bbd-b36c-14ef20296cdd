<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}用户中心{% endblock %} - FocuSee</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .layout-container {
            height: 100vh;
        }
        
        .header {
            background: #fff;
            border-bottom: 1px solid #e6e6e6;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            font-size: 20px;
            font-weight: 600;
            color: #74b9ff;
        }
        
        .sidebar {
            background: #2d3748;
            overflow: hidden;
        }
        
        .main-content {
            background: #f0f2f5;
            padding: 20px;
            overflow-y: auto;
        }
        
        .content-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .page-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e6e6e6;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container class="layout-container">
            <!-- 顶部导航 -->
            <el-header class="header" height="60px">
                <div class="logo">
                    <el-icon><User /></el-icon>
                    FocuSee 用户中心
                </div>
                <div>
                    <el-dropdown @command="handleCommand">
                        <span class="el-dropdown-link">
                            <el-icon><User /></el-icon>
                            <span v-text="userInfo.username"></span>
                            <el-icon class="el-icon--right"><arrow-down /></el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </el-header>
            
            <el-container>
                <!-- 侧边栏 -->
                <el-aside class="sidebar" width="200px">
                    <el-menu
                        :default-active="currentRoute"
                        class="el-menu-vertical"
                        background-color="#2d3748"
                        text-color="#bfcbd9"
                        active-text-color="#74b9ff">
                        
                        <el-menu-item index="/user/dashboard" @click="navigate('/user/dashboard')">
                            <el-icon><Odometer /></el-icon>
                            <span>仪表板</span>
                        </el-menu-item>
                        <el-menu-item index="/user/licenses" @click="navigate('/user/licenses')">
                            <el-icon><Key /></el-icon>
                            <span>我的授权</span>
                        </el-menu-item>
                        <el-menu-item index="/user/payment" @click="navigate('/user/payment')">
                            <el-icon><CreditCard /></el-icon>
                            <span>支付中心</span>
                        </el-menu-item>
                        <el-menu-item index="/user/api-usage" @click="navigate('/user/api-usage')">
                            <el-icon><DataAnalysis /></el-icon>
                            <span>API使用统计</span>
                        </el-menu-item>
                        <el-menu-item index="/user/profile" @click="navigate('/user/profile')">
                            <el-icon><Setting /></el-icon>
                            <span>个人设置</span>
                        </el-menu-item>
                    </el-menu>
                </el-aside>
                
                <!-- 主内容区 -->
                <el-main class="main-content">
                    <div class="content-card">
                        <div class="page-header">
                            <h1 class="page-title">{% block page_title %}仪表板{% endblock %}</h1>
                        </div>
                        {% block content %}{% endblock %}
                    </div>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                const currentRoute = ref(window.location.pathname);
                const userInfo = ref({
                    username: '加载中...'
                });
                
                // 检查登录状态
                const checkAuth = () => {
                    const token = localStorage.getItem('user_token');
                    if (!token) {
                        window.location.href = '/user/login';
                        return false;
                    }
                    
                    const savedUserInfo = localStorage.getItem('user_info');
                    if (savedUserInfo) {
                        userInfo.value = JSON.parse(savedUserInfo);
                    }
                    
                    return true;
                };
                
                const navigate = (path) => {
                    window.location.href = path;
                };
                
                const handleCommand = (command) => {
                    if (command === 'profile') {
                        navigate('/user/profile');
                    } else if (command === 'logout') {
                        ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            localStorage.removeItem('user_token');
                            localStorage.removeItem('user_info');
                            ElMessage.success('已退出登录');
                            window.location.href = '/user/login';
                        });
                    }
                };
                
                onMounted(() => {
                    checkAuth();
                });
                
                return {
                    currentRoute,
                    userInfo,
                    navigate,
                    handleCommand
                };
            }
        });

        // 注册 Element Plus 图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus);
        app.mount('#app');
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
