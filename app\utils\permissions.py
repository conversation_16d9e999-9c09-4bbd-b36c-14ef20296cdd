from functools import wraps
from typing import List, Union
from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session
from app.database import get_db
from app.services.permission_service import PermissionService
import logging

logger = logging.getLogger(__name__)

def require_permissions(permissions: Union[str, List[str]], require_all: bool = True):
    """
    权限装饰器
    
    Args:
        permissions: 需要的权限，可以是单个权限字符串或权限列表
        require_all: 是否需要所有权限（True）还是任一权限（False）
    """
    if isinstance(permissions, str):
        permissions = [permissions]
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取当前用户ID和数据库会话
            current_user_id = kwargs.get('current_user_id')
            db = kwargs.get('db')
            
            if not current_user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Database session not available"
                )
            
            # 获取用户权限
            user_permissions = PermissionService.get_user_permissions(db, current_user_id)
            
            # 检查权限
            if require_all:
                # 需要所有权限
                missing_permissions = [p for p in permissions if p not in user_permissions]
                if missing_permissions:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Missing required permissions: {', '.join(missing_permissions)}"
                    )
            else:
                # 需要任一权限
                if not any(p in user_permissions for p in permissions):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Missing any of required permissions: {', '.join(permissions)}"
                    )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

def check_user_permission(user_id: str, permission: str, db: Session) -> bool:
    """检查用户权限的辅助函数"""
    return PermissionService.check_permission(db, user_id, permission)

def get_current_user_permissions(user_id: str, db: Session) -> List[str]:
    """获取当前用户权限的辅助函数"""
    return PermissionService.get_user_permissions(db, user_id)

class PermissionChecker:
    """权限检查器类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def has_permission(self, user_id: str, permission: str) -> bool:
        """检查用户是否有指定权限"""
        return PermissionService.check_permission(self.db, user_id, permission)
    
    def has_any_permission(self, user_id: str, permissions: List[str]) -> bool:
        """检查用户是否有任一权限"""
        user_permissions = PermissionService.get_user_permissions(self.db, user_id)
        return any(p in user_permissions for p in permissions)
    
    def has_all_permissions(self, user_id: str, permissions: List[str]) -> bool:
        """检查用户是否有所有权限"""
        user_permissions = PermissionService.get_user_permissions(self.db, user_id)
        return all(p in user_permissions for p in permissions)
    
    def require_permission(self, user_id: str, permission: str):
        """要求用户有指定权限，否则抛出异常"""
        if not self.has_permission(user_id, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permission: {permission}"
            )
    
    def require_any_permission(self, user_id: str, permissions: List[str]):
        """要求用户有任一权限，否则抛出异常"""
        if not self.has_any_permission(user_id, permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing any of required permissions: {', '.join(permissions)}"
            )
    
    def require_all_permissions(self, user_id: str, permissions: List[str]):
        """要求用户有所有权限，否则抛出异常"""
        if not self.has_all_permissions(user_id, permissions):
            missing = [p for p in permissions if not self.has_permission(user_id, p)]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {', '.join(missing)}"
            )

def get_permission_checker(db: Session = Depends(get_db)) -> PermissionChecker:
    """获取权限检查器的依赖注入函数"""
    return PermissionChecker(db)
