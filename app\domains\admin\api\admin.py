from fastapi import APIRouter, Depends, HTTPException, status, Request, Form
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from app.database import get_db
from app.schemas.admin import AdminLogin, Token, AdminResponse
from app.schemas.user import UserCreate, UserUpdate, UserResponse
from app.services.admin_service import AdminService
from app.services.download_service import DownloadService
from app.models.admin_user import AdminUser
from app.config import settings
from jose import JWTError, jwt
from datetime import timed<PERSON>ta
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()
templates = Jinja2Templates(directory="app/templates")

def get_current_admin(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    """获取当前管理员"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(credentials.credentials, settings.secret_key, algorithms=[settings.algorithm])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    admin = db.query(AdminUser).filter(AdminUser.username == username).first()
    if admin is None:
        raise credentials_exception
    return admin

@router.post("/admin/login", response_model=Token)
async def login_admin(admin_login: AdminLogin, db: Session = Depends(get_db)):
    """管理员登录"""
    admin = AdminService.authenticate_admin(db, admin_login.username, admin_login.password)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = AdminService.create_access_token(
        data={"sub": admin.username}, 
        secret_key=settings.secret_key,
        algorithm=settings.algorithm,
        expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/admin/users", response_model=List[UserResponse])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取用户列表"""
    # TODO: 添加session验证替代JWT认证
    users = AdminService.get_users(db, skip=skip, limit=limit)
    return users

@router.post("/admin/users", response_model=UserResponse)
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db)
):
    """创建用户"""
    # TODO: 添加session验证替代JWT认证
    return AdminService.create_user(db, user)

@router.put("/admin/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    db: Session = Depends(get_db)
):
    """更新用户"""
    # TODO: 添加session验证替代JWT认证
    user = AdminService.update_user(db, user_id, user_update)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.delete("/admin/users/{user_id}")
async def delete_user(
    user_id: str,
    db: Session = Depends(get_db)
):
    """删除用户"""
    # TODO: 添加session验证替代JWT认证
    success = AdminService.delete_user(db, user_id)
    if not success:
        raise HTTPException(status_code=404, detail="User not found")
    return {"message": "User deleted successfully"}

@router.get("/admin/download-stats")
async def get_download_stats(
    limit: int = 100,
    current_admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取下载统计"""
    stats = DownloadService.get_download_stats(db, limit)
    return stats

@router.get("/system/stats")
async def get_system_stats(
    db: Session = Depends(get_db)
):
    """获取系统统计数据"""
    try:
        import psutil
        from datetime import datetime, timedelta
        from app.models.user import User
        from app.models.agent import Agent
        from app.models.license import License
        from app.models.api_call_log import ApiCallLog
        from app.models.download_log import DownloadLog
        from sqlalchemy import func, and_

        # 基础统计数据
        total_users = db.query(User).count()
        total_agents = db.query(Agent).count()
        total_licenses = db.query(License).count()
        active_licenses = db.query(License).filter(License.status == 'active').count()

        # API调用统计
        total_api_calls = db.query(ApiCallLog).count()
        today = datetime.now().date()
        today_api_calls = db.query(ApiCallLog).filter(
            func.date(ApiCallLog.call_time) == today
        ).count()

        # 错误率计算
        error_calls = db.query(ApiCallLog).filter(ApiCallLog.response_status >= 400).count()
        error_rate = round((error_calls / total_api_calls * 100) if total_api_calls > 0 else 0, 2)

        # 下载统计
        total_downloads = db.query(DownloadLog).count()
        today_downloads = db.query(DownloadLog).filter(
            func.date(DownloadLog.download_time) == today
        ).count()

        # 系统资源使用率
        memory_info = psutil.virtual_memory()
        memory_usage = round(memory_info.percent, 2)
        memory_total = round(memory_info.total / (1024**3), 2)  # GB
        memory_used = round(memory_info.used / (1024**3), 2)   # GB

        cpu_usage = round(psutil.cpu_percent(interval=1), 2)
        cpu_count = psutil.cpu_count()

        disk_info = psutil.disk_usage('/')
        disk_usage = round(disk_info.percent, 2)
        disk_total = round(disk_info.total / (1024**3), 2)  # GB
        disk_used = round(disk_info.used / (1024**3), 2)    # GB

        # 系统运行时间
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        uptime_str = f"{uptime.days}天 {uptime.seconds // 3600}小时 {(uptime.seconds // 60) % 60}分钟"

        # 最近7天的API调用趋势
        week_ago = datetime.now() - timedelta(days=7)
        api_trend = []
        for i in range(7):
            date = (datetime.now() - timedelta(days=6-i)).date()
            count = db.query(ApiCallLog).filter(
                func.date(ApiCallLog.call_time) == date
            ).count()
            api_trend.append({
                "date": date.strftime("%m-%d"),
                "count": count
            })

        return {
            "totalUsers": total_users,
            "totalAgents": total_agents,
            "totalLicenses": total_licenses,
            "activeLicenses": active_licenses,
            "totalApiCalls": total_api_calls,
            "todayApiCalls": today_api_calls,
            "totalDownloads": total_downloads,
            "todayDownloads": today_downloads,
            "errorRate": error_rate,
            "memoryUsage": memory_usage,
            "memoryTotal": memory_total,
            "memoryUsed": memory_used,
            "cpuUsage": cpu_usage,
            "cpuCount": cpu_count,
            "diskUsage": disk_usage,
            "diskTotal": disk_total,
            "diskUsed": disk_used,
            "uptime": uptime_str,
            "apiTrend": api_trend
        }
    except Exception as e:
        logger.error(f"Error getting system stats: {str(e)}")
        return {
            "totalUsers": 0,
            "totalAgents": 0,
            "totalLicenses": 0,
            "activeLicenses": 0,
            "totalApiCalls": 0,
            "todayApiCalls": 0,
            "totalDownloads": 0,
            "todayDownloads": 0,
            "errorRate": 0,
            "memoryUsage": 0,
            "memoryTotal": 0,
            "memoryUsed": 0,
            "cpuUsage": 0,
            "cpuCount": 0,
            "diskUsage": 0,
            "diskTotal": 0,
            "diskUsed": 0,
            "uptime": "0天 0小时 0分钟",
            "apiTrend": []
        }


@router.get("/system/logs")
async def get_system_logs(
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """获取系统日志"""
    try:
        from app.models.api_call_log import ApiCallLog
        from sqlalchemy import desc

        # 获取最近的API调用日志
        recent_logs = db.query(ApiCallLog).order_by(
            desc(ApiCallLog.call_time)
        ).limit(limit).all()

        logs = []
        for log in recent_logs:
            logs.append({
                "timestamp": log.call_time.isoformat() if log.call_time else "",
                "level": "ERROR" if log.response_status >= 400 else "INFO",
                "message": f"{log.method} {log.api_endpoint} - {log.response_status} ({log.response_time}ms)",
                "user_id": log.user_id,
                "ip_address": log.ip_address,
                "status_code": log.response_status
            })

        return {"logs": logs}
    except Exception as e:
        logger.error(f"Error getting system logs: {str(e)}")
        return {"logs": []}


@router.get("/system/api-stats")
async def get_api_stats(
    db: Session = Depends(get_db)
):
    """获取API统计数据"""
    try:
        from app.models.api_call_log import ApiCallLog
        from sqlalchemy import func, desc
        from datetime import datetime, timedelta

        # 获取最热门的API端点
        popular_apis = db.query(
            ApiCallLog.api_endpoint,
            func.count(ApiCallLog.id).label('count'),
            func.avg(ApiCallLog.response_time).label('avg_response_time')
        ).group_by(
            ApiCallLog.api_endpoint
        ).order_by(
            desc(func.count(ApiCallLog.id))
        ).limit(10).all()

        api_stats = []
        for api in popular_apis:
            api_stats.append({
                "endpoint": api.api_endpoint,
                "count": api.count,
                "avg_response_time": round(api.avg_response_time or 0, 2)
            })

        # 获取错误日志
        error_logs = db.query(ApiCallLog).filter(
            ApiCallLog.response_status >= 400
        ).order_by(
            desc(ApiCallLog.call_time)
        ).limit(20).all()

        errors = []
        for error in error_logs:
            errors.append({
                "timestamp": error.call_time.isoformat() if error.call_time else "",
                "error_type": f"HTTP {error.response_status}",
                "message": f"{error.method} {error.api_endpoint} - {error.error_message or 'Unknown error'}",
                "user_id": error.user_id,
                "ip_address": error.ip_address
            })

        return {
            "api_stats": api_stats,
            "error_logs": errors
        }
    except Exception as e:
        logger.error(f"Error getting API stats: {str(e)}")
        return {
            "api_stats": [],
            "error_logs": []
        }


@router.post("/system/generate-test-data")
async def generate_test_data(
    db: Session = Depends(get_db)
):
    """生成测试数据用于系统监控"""
    try:
        from app.models.api_call_log import ApiCallLog
        from app.models.license import License
        from datetime import datetime, timedelta
        import random

        # 获取一些现有的授权码
        licenses = db.query(License).limit(5).all()
        if not licenses:
            return {"message": "没有找到授权码，无法生成测试数据"}

        # API端点列表
        api_endpoints = [
            "/api/auth/verify",
            "/api/download_stats",
            "/api/licenses/check",
            "/api/products/list",
            "/api/auth/login",
            "/api/system/stats",
            "/api/orders/create",
            "/api/agents/list"
        ]

        # HTTP方法
        methods = ["GET", "POST", "PUT", "DELETE"]

        # 生成过去7天的测试数据
        base_time = datetime.now() - timedelta(days=7)

        test_logs = []
        for i in range(100):  # 生成100条测试记录
            license = random.choice(licenses)
            endpoint = random.choice(api_endpoints)
            method = random.choice(methods)

            # 随机时间（过去7天内）
            call_time = base_time + timedelta(
                days=random.randint(0, 7),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )

            # 随机响应状态（大部分成功）
            status_codes = [200, 200, 200, 200, 201, 400, 404, 500]
            status = random.choice(status_codes)

            log = ApiCallLog(
                license_id=license.id,
                user_id=f"test_user_{random.randint(1, 10)}",
                api_endpoint=endpoint,
                method=method,
                ip_address=f"192.168.1.{random.randint(1, 255)}",
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                response_status=status,
                response_time=random.randint(50, 2000),
                success=status < 400,
                error_message="Test error" if status >= 400 else None,
                call_time=call_time
            )
            test_logs.append(log)

        # 批量插入
        db.add_all(test_logs)
        db.commit()

        return {"message": f"成功生成 {len(test_logs)} 条测试数据"}

    except Exception as e:
        logger.error(f"Error generating test data: {str(e)}")
        db.rollback()
        return {"error": f"生成测试数据失败: {str(e)}"}

@router.get("/auth-logs")
async def get_auth_logs(
    page: int = 1,
    size: int = 20,
    action: str = None,
    result: str = None,
    user_id: str = None,
    order_number: str = None,
    db: Session = Depends(get_db)
):
    """获取授权日志"""
    try:
        from app.models import AuthLog, License, Product
        from sqlalchemy import desc, and_

        # 构建查询条件
        conditions = []
        if action:
            conditions.append(AuthLog.action == action)
        if result:
            conditions.append(AuthLog.result == result)
        if user_id:
            conditions.append(AuthLog.user_id.like(f"%{user_id}%"))
        if order_number:
            from app.models import Order
            conditions.append(AuthLog.order_id.in_(
                db.query(Order.id).filter(Order.order_number.like(f"%{order_number}%"))
            ))

        # 查询总数
        total_query = db.query(AuthLog)
        if conditions:
            total_query = total_query.filter(and_(*conditions))
        total = total_query.count()

        # 分页查询
        from app.models import Order
        logs_query = db.query(AuthLog).join(
            License, AuthLog.license_id == License.id, isouter=True
        ).join(
            Product, AuthLog.product_id == Product.id, isouter=True
        ).join(
            Order, AuthLog.order_id == Order.id, isouter=True
        )

        if conditions:
            logs_query = logs_query.filter(and_(*conditions))

        logs = logs_query.order_by(desc(AuthLog.created_at)).offset(
            (page - 1) * size
        ).limit(size).all()

        # 格式化日志数据
        log_data = []
        for log in logs:
            log_data.append({
                "id": log.id,
                "action": log.action.value,
                "result": log.result.value,
                "user_id": log.user_id,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "license_code": log.license.license_code if log.license else None,
                "product_name": log.product.name if log.product else None,
                "order_number": log.order.order_number if log.order else None,
                "request_params": log.request_params,
                "response_data": log.response_data,
                "error_message": log.error_message,
                "quota_before": log.quota_before,
                "quota_after": log.quota_after,
                "created_at": log.created_at.isoformat() if log.created_at else None
            })

        return {
            "logs": log_data,
            "total": total,
            "page": page,
            "size": size
        }

    except Exception as e:
        logger.error(f"Error getting auth logs: {str(e)}")
        return {
            "logs": [],
            "total": 0,
            "page": page,
            "size": size
        }

@router.get("/admin/licenses")
async def get_admin_licenses(
    page: int = 1,
    size: int = 20,
    status: str = None,
    product_id: int = None,
    user_id: str = None,
    search: str = None,
    db: Session = Depends(get_db)
):
    """获取授权码列表"""
    try:
        from app.models import License, Product, Order
        from sqlalchemy import desc, and_, or_

        # 构建查询条件
        conditions = []
        if status:
            conditions.append(License.status == status)
        if product_id:
            conditions.append(License.product_id == product_id)
        if user_id:
            conditions.append(License.user_id.like(f"%{user_id}%"))
        if search:
            conditions.append(or_(
                License.license_code.like(f"%{search}%"),
                Order.order_number.like(f"%{search}%")
            ))

        # 查询总数
        total_query = db.query(License).join(Product).join(Order, isouter=True)
        if conditions:
            total_query = total_query.filter(and_(*conditions))
        total = total_query.count()

        # 分页查询
        licenses_query = db.query(License).join(Product).join(Order, isouter=True)
        if conditions:
            licenses_query = licenses_query.filter(and_(*conditions))

        licenses = licenses_query.order_by(desc(License.created_at)).offset(
            (page - 1) * size
        ).limit(size).all()

        # 格式化授权码数据
        license_data = []
        for license_obj in licenses:
            license_data.append({
                "id": license_obj.id,
                "license_code": license_obj.license_code,
                "status": license_obj.status.value,
                "product_name": license_obj.product.name,
                "product_code": license_obj.product.code,
                "user_id": license_obj.user_id,
                "max_api_calls": license_obj.max_api_calls,
                "used_api_calls": license_obj.used_api_calls,
                "expire_date": license_obj.expire_date.isoformat() if license_obj.expire_date else None,
                "created_at": license_obj.created_at.isoformat() if license_obj.created_at else None
            })

        return {
            "licenses": license_data,
            "total": total,
            "page": page,
            "size": size
        }

    except Exception as e:
        logger.error(f"Error getting licenses: {str(e)}")
        return {
            "licenses": [],
            "total": 0,
            "page": page,
            "size": size
        }

@router.get("/auth-stats")
async def get_auth_stats(
    db: Session = Depends(get_db)
):
    """获取授权统计数据"""
    try:
        from app.models import AuthLog, License, LicenseStatus, AuthResult
        from sqlalchemy import func

        # 总验证次数
        total_verifications = db.query(AuthLog).count()

        # 成功率
        success_count = db.query(AuthLog).filter(AuthLog.result == AuthResult.SUCCESS).count()
        success_rate = round((success_count / total_verifications * 100) if total_verifications > 0 else 0, 2)

        # 活跃授权码数量
        active_licenses = db.query(License).filter(License.status == LicenseStatus.ACTIVE).count()

        # 配额使用率
        total_quota = db.query(func.sum(License.max_api_calls)).filter(
            License.max_api_calls > 0,
            License.status == LicenseStatus.ACTIVE
        ).scalar() or 0

        used_quota = db.query(func.sum(License.used_api_calls)).filter(
            License.max_api_calls > 0,
            License.status == LicenseStatus.ACTIVE
        ).scalar() or 0

        quota_usage = round((used_quota / total_quota * 100) if total_quota > 0 else 0, 2)

        return {
            "total_verifications": total_verifications,
            "success_rate": success_rate,
            "active_licenses": active_licenses,
            "quota_usage": quota_usage
        }

    except Exception as e:
        logger.error(f"Error getting auth stats: {str(e)}")
        return {
            "total_verifications": 0,
            "success_rate": 0,
            "active_licenses": 0,
            "quota_usage": 0
        }

@router.post("/admin/licenses/{license_id}/reset-quota")
async def reset_license_quota(
    license_id: int,
    db: Session = Depends(get_db)
):
    """重置授权码配额"""
    try:
        from app.models import License, AuthLog, AuthAction, AuthResult

        license_obj = db.query(License).filter(License.id == license_id).first()
        if not license_obj:
            return {"success": False, "message": "授权码不存在"}

        old_used = license_obj.used_api_calls
        license_obj.used_api_calls = 0

        # 记录操作日志
        auth_log = AuthLog(
            action=AuthAction.UPDATE_QUOTA,
            result=AuthResult.SUCCESS,
            license_id=license_id,
            product_id=license_obj.product_id,
            user_id=license_obj.user_id,
            quota_before=old_used,
            quota_after=0,
            request_params='{"action": "reset_quota"}',
            response_data=f'{{"old_used": {old_used}, "new_used": 0}}'
        )

        db.add(auth_log)
        db.commit()

        return {"success": True, "message": "配额重置成功"}

    except Exception as e:
        logger.error(f"Error resetting license quota: {str(e)}")
        db.rollback()
        return {"success": False, "message": "配额重置失败"}

@router.post("/admin/licenses/{license_id}/toggle-status")
async def toggle_license_status(
    license_id: int,
    db: Session = Depends(get_db)
):
    """切换授权码状态"""
    try:
        from app.models import License, LicenseStatus, AuthLog, AuthAction, AuthResult

        license_obj = db.query(License).filter(License.id == license_id).first()
        if not license_obj:
            return {"success": False, "message": "授权码不存在"}

        old_status = license_obj.status
        new_status = LicenseStatus.INACTIVE if old_status == LicenseStatus.ACTIVE else LicenseStatus.ACTIVE
        license_obj.status = new_status

        # 记录操作日志
        action = AuthAction.ACTIVATE_LICENSE if new_status == LicenseStatus.ACTIVE else AuthAction.DEACTIVATE_LICENSE
        auth_log = AuthLog(
            action=action,
            result=AuthResult.SUCCESS,
            license_id=license_id,
            product_id=license_obj.product_id,
            user_id=license_obj.user_id,
            request_params=f'{{"old_status": "{old_status.value}", "new_status": "{new_status.value}"}}',
            response_data=f'{{"status_changed": true}}'
        )

        db.add(auth_log)
        db.commit()

        action_name = "激活" if new_status == LicenseStatus.ACTIVE else "停用"
        return {"success": True, "message": f"授权码{action_name}成功"}

    except Exception as e:
        logger.error(f"Error toggling license status: {str(e)}")
        db.rollback()
        return {"success": False, "message": "状态切换失败"}
