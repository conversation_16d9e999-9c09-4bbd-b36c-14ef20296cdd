#!/usr/bin/env python3
"""
测试支付宝异步通知接口
"""

import requests
import json
from datetime import datetime

def test_notify_get():
    """测试GET请求（URL验证）"""
    print("1. 测试支付宝通知URL验证 (GET)...")
    
    try:
        # 测试本地接口
        response = requests.get("http://localhost:8008/api/payment/alipay/notify")
        print(f"   本地接口状态码: {response.status_code}")
        print(f"   本地接口响应: {response.text}")
        
        # 测试natapp接口
        response = requests.get("http://c57d4f98.natappfree.cc/api/payment/alipay/notify")
        print(f"   natapp接口状态码: {response.status_code}")
        print(f"   natapp接口响应: {response.text}")
        
        if response.status_code == 200 and response.text == "success":
            print("   ✅ GET接口验证成功")
            return True
        else:
            print("   ❌ GET接口验证失败")
            return False
            
    except Exception as e:
        print(f"   ❌ GET接口测试异常: {str(e)}")
        return False

def test_notify_post():
    """测试POST请求（模拟支付宝通知）"""
    print("\n2. 测试支付宝异步通知 (POST)...")
    
    # 模拟支付宝通知数据
    notify_data = {
        "gmt_create": "2025-08-07 12:30:00",
        "charset": "utf-8",
        "gmt_payment": "2025-08-07 12:30:05",
        "notify_time": "2025-08-07 12:30:10",
        "subject": "测试商品",
        "sign": "mock_sign_for_test",
        "buyer_id": "****************",
        "body": "测试订单",
        "invoice_amount": "0.01",
        "version": "1.0",
        "notify_id": "mock_notify_id_123",
        "fund_bill_list": '[{"amount":"0.01","fundChannel":"ALIPAYACCOUNT"}]',
        "notify_type": "trade_status_sync",
        "out_trade_no": "TEST_ORDER_123",
        "total_amount": "0.01",
        "trade_status": "TRADE_SUCCESS",
        "trade_no": "2025080722001400000000123",
        "auth_app_id": "****************",
        "receipt_amount": "0.01",
        "point_amount": "0.00",
        "app_id": "****************",
        "buyer_pay_amount": "0.01",
        "sign_type": "RSA2",
        "seller_id": "****************"
    }
    
    try:
        # 测试本地接口
        response = requests.post(
            "http://localhost:8008/api/payment/alipay/notify",
            data=notify_data
        )
        print(f"   本地接口状态码: {response.status_code}")
        print(f"   本地接口响应: {response.text}")
        
        # 测试natapp接口
        response = requests.post(
            "http://c57d4f98.natappfree.cc/api/payment/alipay/notify",
            data=notify_data
        )
        print(f"   natapp接口状态码: {response.status_code}")
        print(f"   natapp接口响应: {response.text}")
        
        if response.status_code == 200:
            print("   ✅ POST接口调用成功")
            return True
        else:
            print("   ❌ POST接口调用失败")
            return False
            
    except Exception as e:
        print(f"   ❌ POST接口测试异常: {str(e)}")
        return False

def test_notify_with_real_order():
    """使用真实订单测试通知"""
    print("\n3. 使用真实订单测试通知...")
    
    # 先创建一个真实订单
    try:
        response = requests.post(
            "http://localhost:8008/api/payment/face-to-face",
            json={
                "user_id": "test_notify_user",
                "product_id": 1,
                "timeout_minutes": 30
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                order_no = data.get("order_no")
                print(f"   创建测试订单成功: {order_no}")
                
                # 模拟该订单的支付成功通知
                notify_data = {
                    "gmt_create": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "charset": "utf-8",
                    "gmt_payment": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "notify_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "subject": "FocuSee Pro",
                    "sign": "mock_sign_for_real_order",
                    "buyer_id": "****************",
                    "body": f"用户test_notify_user购买FocuSee Pro",
                    "invoice_amount": "98.99",
                    "version": "1.0",
                    "notify_id": f"mock_notify_{int(datetime.now().timestamp())}",
                    "fund_bill_list": '[{"amount":"98.99","fundChannel":"ALIPAYACCOUNT"}]',
                    "notify_type": "trade_status_sync",
                    "out_trade_no": order_no,
                    "total_amount": "98.99",
                    "trade_status": "TRADE_SUCCESS",
                    "trade_no": f"2025080722001400000{int(datetime.now().timestamp())}",
                    "auth_app_id": "****************",
                    "receipt_amount": "98.99",
                    "point_amount": "0.00",
                    "app_id": "****************",
                    "buyer_pay_amount": "98.99",
                    "sign_type": "RSA2",
                    "seller_id": "****************"
                }
                
                # 发送通知
                response = requests.post(
                    "http://localhost:8008/api/payment/alipay/notify",
                    data=notify_data
                )
                
                print(f"   通知发送状态码: {response.status_code}")
                print(f"   通知发送响应: {response.text}")
                
                # 查询订单状态验证
                response = requests.post(
                    "http://localhost:8008/api/payment/query",
                    json={"order_no": order_no}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        print(f"   订单状态: {data.get('status')}")
                        if data.get('status') == 'paid':
                            print("   ✅ 通知处理成功，订单状态已更新为已支付")
                            return True
                        else:
                            print("   ⚠️  通知已处理，但订单状态未更新")
                            return False
                    else:
                        print(f"   ❌ 查询订单失败: {data.get('error')}")
                        return False
                
            else:
                print(f"   ❌ 创建订单失败: {data.get('error')}")
                return False
        else:
            print(f"   ❌ 创建订单请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 真实订单测试异常: {str(e)}")
        return False

def test_natapp_connectivity():
    """测试natapp连通性"""
    print("\n4. 测试natapp连通性...")
    
    try:
        # 测试natapp基本连通性
        response = requests.get("http://c57d4f98.natappfree.cc/", timeout=10)
        print(f"   natapp根路径状态码: {response.status_code}")
        
        # 测试API路径
        response = requests.get("http://c57d4f98.natappfree.cc/api/payment/test/products", timeout=10)
        print(f"   natapp API路径状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ natapp连通性正常")
            return True
        else:
            print("   ⚠️  natapp连通性异常")
            return False
            
    except Exception as e:
        print(f"   ❌ natapp连通性测试异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔔 支付宝异步通知接口测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试GET接口
    results.append(("GET接口验证", test_notify_get()))
    
    # 2. 测试POST接口
    results.append(("POST接口调用", test_notify_post()))
    
    # 3. 测试natapp连通性
    results.append(("natapp连通性", test_natapp_connectivity()))
    
    # 4. 测试真实订单通知
    results.append(("真实订单通知", test_notify_with_real_order()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("-" * 40)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} - {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    # 给出建议
    print("\n💡 测试总结:")
    if success_count == len(results):
        print("🎉 支付宝异步通知接口完全正常！")
        print("\n✨ 功能验证:")
        print("- ✅ GET接口支持URL验证")
        print("- ✅ POST接口支持异步通知")
        print("- ✅ natapp内网穿透正常")
        print("- ✅ 订单状态更新正常")
        
        print(f"\n🌐 配置信息:")
        print(f"- 通知URL: http://c57d4f98.natappfree.cc/api/payment/alipay/notify")
        print(f"- 跳转URL: http://c57d4f98.natappfree.cc/user/payment")
        print(f"- 本地服务: http://localhost:8008")
        
    else:
        print("⚠️  部分测试失败，请检查:")
        print("- 确保本地服务正在运行")
        print("- 检查natapp是否正常工作")
        print("- 验证URL配置是否正确")
        print("- 查看服务器日志获取详细错误")
    
    return success_count == len(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
