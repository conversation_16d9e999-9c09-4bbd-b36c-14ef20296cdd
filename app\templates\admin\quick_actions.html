{% extends "admin/base.html" %}

{% block title %}快速操作 - FocuSee 管理系统{% endblock %}
{% block page_title %}快速操作{% endblock %}

{% block data %}
    products: {{ products | tojson }},
    agents: {{ agents | tojson }},
    createOrderForm: {
        order_number: '',
        product_id: null,
        agent_id: null,
        quantity: 1,
        unit_price: null,
        total_price: null,
        status: 'PENDING',
        customer_info: '',
        notes: '',
        expire_date: null
    },
    createLicenseForm: {
        product_id: '',
        agent_id: '',
        max_api_calls: -1,
        expire_date: null,
        notes: ''
    },
    createMachineCodeForm: {
        machine_id: '',
        days: 30,
        notes: ''
    },
    createOrderDialogVisible: false,
    createLicenseDialogVisible: false,
    createMachineCodeDialogVisible: false,
    loading: false,
    isMobile: false,
    dialogWidth: '600px',
    createdOrderNumber: '',
    showOrderResult: false,
    createdLicenseCode: '',
    showLicenseResult: false,
    createdMachineCode: '',
    showMachineResult: false,
    rules: {
        product_id: [
            { required: true, message: '请选择产品', trigger: 'change' }
        ],
        quantity: [
            { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        expire_date: [
            { required: true, message: '请选择过期时间', trigger: 'change' }
        ]
    }
{% endblock %}

{% block content %}
<!-- 移动端提示 -->
<div class="content-card mobile-tip" style="margin-bottom: 20px; display: none;">
    <el-alert
        title="移动端优化"
        type="info"
        description="此页面已针对移动设备进行优化，支持触摸操作和响应式布局。"
        :closable="false"
        show-icon>
    </el-alert>
</div>

<div class="content-card">
    <el-row :gutter="20">
        <!-- 快速操作卡片 -->
        <el-col :xs="24" :sm="12" :md="8" v-for="action in quickActions" :key="action.key">
            <el-card class="quick-action-card" shadow="hover">
                <div class="action-icon" :style="{ background: action.color }">
                    <el-icon :size="32">
                        <component :is="action.icon"></component>
                    </el-icon>
                </div>
                <h3 v-text="action.title"></h3>
                <p v-text="action.description"></p>
                <el-button type="primary" size="small" style="margin-top: 12px;" @click="openDialog(action.key)" v-text="action.buttonText">
                </el-button>
            </el-card>
        </el-col>
    </el-row>

    <!-- 最近创建的码显示区域 -->
    <div v-if="hasRecentCodes" style="margin-top: 30px;">
        <el-card shadow="hover" style="border-radius: 12px;">
            <template #header>
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="display: flex; align-items: center;">
                        <el-icon size="20" color="#409eff" style="margin-right: 8px;"><Document /></el-icon>
                        <span style="font-weight: 500; color: #303133;">最近创建的码</span>
                    </div>
                    <el-button size="small" type="danger" plain @click="clearRecentCodes">
                        <el-icon><Delete /></el-icon>
                        清空
                    </el-button>
                </div>
            </template>

            <!-- 订单号 -->
            <div v-if="createdOrderNumber" style="margin-bottom: 16px; padding: 12px; background-color: #f0f9ff; border: 1px solid #409eff; border-radius: 8px;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="flex: 1;">
                        <div style="display: flex; align-items: center; margin-bottom: 4px;">
                            <el-icon color="#409eff" size="16" style="margin-right: 6px;"><Tickets /></el-icon>
                            <span style="font-weight: 500; color: #409eff;">订单号</span>
                        </div>
                        <div style="font-family: monospace; font-size: 14px; color: #303133; background-color: #ffffff; padding: 6px 10px; border-radius: 4px; border: 1px solid #dcdfe6; word-break: break-all;" v-text="createdOrderNumber"></div>
                    </div>
                    <el-button size="small" type="primary" plain @click="copyCode(createdOrderNumber, '订单号')" style="margin-left: 12px;">
                        <el-icon><CopyDocument /></el-icon>
                        复制
                    </el-button>
                </div>
            </div>

            <!-- 授权码 -->
            <div v-if="createdLicenseCode" style="margin-bottom: 16px; padding: 12px; background-color: #f0f9ff; border: 1px solid #67c23a; border-radius: 8px;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="flex: 1;">
                        <div style="display: flex; align-items: center; margin-bottom: 4px;">
                            <el-icon color="#67c23a" size="16" style="margin-right: 6px;"><Key /></el-icon>
                            <span style="font-weight: 500; color: #67c23a;">授权码</span>
                        </div>
                        <div style="font-family: monospace; font-size: 14px; color: #303133; background-color: #ffffff; padding: 6px 10px; border-radius: 4px; border: 1px solid #dcdfe6; word-break: break-all;" v-text="createdLicenseCode"></div>
                    </div>
                    <el-button size="small" type="success" plain @click="copyCode(createdLicenseCode, '授权码')" style="margin-left: 12px;">
                        <el-icon><CopyDocument /></el-icon>
                        复制
                    </el-button>
                </div>
            </div>

            <!-- 机器码激活码 -->
            <div v-if="createdMachineCode" style="margin-bottom: 0; padding: 12px; background-color: #fdf6ec; border: 1px solid #e6a23c; border-radius: 8px;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="flex: 1;">
                        <div style="display: flex; align-items: center; margin-bottom: 4px;">
                            <el-icon color="#e6a23c" size="16" style="margin-right: 6px;"><Cpu /></el-icon>
                            <span style="font-weight: 500; color: #e6a23c;">机器码激活码</span>
                        </div>
                        <div style="font-family: monospace; font-size: 14px; color: #303133; background-color: #ffffff; padding: 6px 10px; border-radius: 4px; border: 1px solid #dcdfe6; word-break: break-all;" v-text="createdMachineCode"></div>
                    </div>
                    <el-button size="small" type="warning" plain @click="copyCode(createdMachineCode, '机器码激活码')" style="margin-left: 12px;">
                        <el-icon><CopyDocument /></el-icon>
                        复制
                    </el-button>
                </div>
            </div>
        </el-card>
    </div>
</div>

<!-- 创建订单对话框 -->
<el-dialog v-model="createOrderDialogVisible" title="创建订单" :width="dialogWidth" :close-on-click-modal="false" :fullscreen="isMobile" class="mobile-dialog">
    <el-form :model="createOrderForm" :rules="rules" :label-width="isMobile ? '80px' : '100px'" ref="orderFormRef" :label-position="isMobile ? 'top' : 'right'">
        <el-form-item label="订单号">
            <el-input
                v-model="createOrderForm.order_number"
                placeholder="留空自动生成"
                :size="isMobile ? 'large' : 'default'"
                maxlength="50"
                show-word-limit />
        </el-form-item>
        <el-form-item label="产品" prop="product_id">
            <el-select
                v-model="createOrderForm.product_id"
                placeholder="请选择产品"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                @change="onProductChange"
                filterable>
                <el-option v-for="product in products" :key="product.id" :label="product.name" :value="product.id" />
            </el-select>
        </el-form-item>
        <el-form-item label="代理商">
            <el-select
                v-model="createOrderForm.agent_id"
                placeholder="请选择代理商（可选）"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                clearable
                filterable>
                <el-option v-for="agent in agents" :key="agent.id" :label="agent.name" :value="agent.id" />
            </el-select>
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
            <el-input-number
                v-model="createOrderForm.quantity"
                :min="1"
                :max="1000"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                controls-position="right"
                @change="calculateTotal" />
        </el-form-item>
        <el-form-item label="单价">
            <el-input-number
                v-model="createOrderForm.unit_price"
                :min="0"
                :precision="2"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                controls-position="right"
                @change="calculateTotal">
                <template #prepend>¥</template>
            </el-input-number>
        </el-form-item>
        <el-form-item label="总价">
            <el-input-number
                v-model="createOrderForm.total_price"
                :min="0"
                :precision="2"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                controls-position="right">
                <template #prepend>¥</template>
            </el-input-number>
        </el-form-item>
        <el-form-item label="状态">
            <el-select
                v-model="createOrderForm.status"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                placeholder="请选择订单状态"
                class="status-select">
                <el-option label="待处理" value="PENDING">
                    <div class="status-option">
                        <div class="status-main">
                            <el-tag type="warning" size="small" class="status-tag">
                                待处理
                            </el-tag>
                            <span class="status-description">订单已创建，等待处理</span>
                        </div>
                    </div>
                </el-option>
                <el-option label="已确认" value="CONFIRMED">
                    <div class="status-option">
                        <div class="status-main">
                            <el-tag type="primary" size="small" class="status-tag">
                                已确认
                            </el-tag>
                            <span class="status-description">订单已确认，准备发货</span>
                        </div>
                    </div>
                </el-option>
                <el-option label="已完成" value="COMPLETED">
                    <div class="status-option">
                        <div class="status-main">
                            <el-tag type="success" size="small" class="status-tag">
                                已完成
                            </el-tag>
                            <span class="status-description">订单已完成交付</span>
                        </div>
                    </div>
                </el-option>
                <el-option label="已取消" value="CANCELLED">
                    <div class="status-option">
                        <div class="status-main">
                            <el-tag type="info" size="small" class="status-tag">
                                已取消
                            </el-tag>
                            <span class="status-description">订单已取消</span>
                        </div>
                    </div>
                </el-option>
                <el-option label="已过期" value="EXPIRED">
                    <div class="status-option">
                        <div class="status-main">
                            <el-tag type="danger" size="small" class="status-tag">
                                已过期
                            </el-tag>
                            <span class="status-description">订单已过期失效</span>
                        </div>
                    </div>
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="过期时间" prop="expire_date">
            <el-date-picker
                v-model="createOrderForm.expire_date"
                type="datetime"
                placeholder="选择过期时间"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="new Date()"
                :disabled-date="disabledDate">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="客户信息">
            <el-input
                v-model="createOrderForm.customer_info"
                placeholder="请输入客户信息"
                :size="isMobile ? 'large' : 'default'"
                maxlength="200"
                show-word-limit />
        </el-form-item>
        <el-form-item label="备注">
            <el-input
                v-model="createOrderForm.notes"
                type="textarea"
                :rows="isMobile ? 4 : 3"
                placeholder="请输入备注信息"
                :size="isMobile ? 'large' : 'default'"
                maxlength="500"
                show-word-limit />
        </el-form-item>
    </el-form>
    <!-- 创建成功结果显示 -->
    <div v-if="showOrderResult" style="margin-bottom: 20px; padding: 16px; background-color: #f0f9ff; border: 1px solid #409eff; border-radius: 8px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px;">
            <el-icon color="#409eff" size="20" style="margin-right: 8px;"><SuccessFilled /></el-icon>
            <span style="font-weight: 500; color: #409eff;">订单创建成功！</span>
        </div>
        <div style="margin-bottom: 8px;">
            <span style="color: #606266;">订单号：</span>
            <span style="font-family: monospace; background-color: #f5f5f5; padding: 4px 8px; border-radius: 4px; color: #303133;" v-text="createdOrderNumber"></span>
            <el-button size="small" type="primary" plain style="margin-left: 8px;" @click="copyOrderNumber">
                <el-icon><CopyDocument /></el-icon>
                复制
            </el-button>
        </div>
        <div style="font-size: 12px; color: #909399;">
            订单已创建，您可以在订单管理页面查看详情
        </div>
    </div>

    <template #footer>
        <span class="dialog-footer" :class="{ 'mobile-footer': isMobile }">
            <el-button
                @click="closeOrderDialog"
                :size="isMobile ? 'large' : 'default'"
                :style="isMobile ? 'flex: 1; margin-right: 12px;' : ''">
                <span v-text="showOrderResult ? '关闭' : '取消'"></span>
            </el-button>
            <el-button
                v-if="!showOrderResult"
                type="primary"
                @click="createOrder"
                :loading="loading"
                :size="isMobile ? 'large' : 'default'"
                :style="isMobile ? 'flex: 1;' : ''">
                创建订单
            </el-button>
        </span>
    </template>
</el-dialog>

<!-- 创建授权码对话框 -->
<el-dialog v-model="createLicenseDialogVisible" title="创建授权码" :width="dialogWidth" :close-on-click-modal="false" :fullscreen="isMobile" class="mobile-dialog">
    <el-form :model="createLicenseForm" :label-width="isMobile ? '80px' : '100px'" ref="licenseFormRef" :label-position="isMobile ? 'top' : 'right'">
        <el-form-item label="产品" required>
            <el-select
                v-model="createLicenseForm.product_id"
                placeholder="请选择产品"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                filterable>
                <el-option v-for="product in products" :key="product.id" :label="product.name" :value="product.id" />
            </el-select>
        </el-form-item>
        <el-form-item label="代理商">
            <el-select
                v-model="createLicenseForm.agent_id"
                placeholder="请选择代理商（可选）"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                clearable
                filterable>
                <el-option v-for="agent in agents" :key="agent.id" :label="agent.name" :value="agent.id" />
            </el-select>
        </el-form-item>
        <el-form-item label="API调用限制">
            <el-input-number
                v-model="createLicenseForm.max_api_calls"
                :min="-1"
                :max="1000000"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                controls-position="right" />
            <div v-if="isMobile" style="font-size: 12px; color: #909399; margin-top: 4px;">
                -1表示无限制，0表示禁用，正数表示具体限制次数
            </div>
        </el-form-item>
        <el-form-item label="过期时间">
            <el-date-picker
                v-model="createLicenseForm.expire_date"
                type="datetime"
                placeholder="选择过期时间（可选）"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss" />
            <div v-if="isMobile" style="font-size: 12px; color: #909399; margin-top: 4px;">
                留空表示永不过期
            </div>
        </el-form-item>
        <el-form-item label="备注">
            <el-input
                v-model="createLicenseForm.notes"
                type="textarea"
                :rows="isMobile ? 4 : 3"
                placeholder="请输入备注信息"
                :size="isMobile ? 'large' : 'default'"
                maxlength="500"
                show-word-limit />
        </el-form-item>
    </el-form>

    <!-- 创建成功结果显示 -->
    <div v-if="showLicenseResult" style="margin-bottom: 20px; padding: 16px; background-color: #f0f9ff; border: 1px solid #67c23a; border-radius: 8px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px;">
            <el-icon color="#67c23a" size="20" style="margin-right: 8px;"><SuccessFilled /></el-icon>
            <span style="font-weight: 500; color: #67c23a;">授权码创建成功！</span>
        </div>
        <div style="margin-bottom: 8px;">
            <span style="color: #606266;">授权码：</span>
            <span style="font-family: monospace; background-color: #f5f5f5; padding: 4px 8px; border-radius: 4px; color: #303133;" v-text="createdLicenseCode"></span>
            <el-button size="small" type="success" plain style="margin-left: 8px;" @click="copyLicenseCode">
                <el-icon><CopyDocument /></el-icon>
                复制
            </el-button>
        </div>
        <div style="font-size: 12px; color: #909399;">
            授权码已创建，您可以在授权码管理页面查看详情
        </div>
    </div>

    <template #footer>
        <span class="dialog-footer" :class="{ 'mobile-footer': isMobile }">
            <el-button
                @click="closeLicenseDialog"
                :size="isMobile ? 'large' : 'default'"
                :style="isMobile ? 'flex: 1; margin-right: 12px;' : ''">
                <span v-text="showLicenseResult ? '关闭' : '取消'"></span>
            </el-button>
            <el-button
                v-if="!showLicenseResult"
                type="primary"
                @click="createLicense"
                :loading="loading"
                :size="isMobile ? 'large' : 'default'"
                :style="isMobile ? 'flex: 1;' : ''">
                创建授权码
            </el-button>
        </span>
    </template>
</el-dialog>

<!-- 创建机器码对话框 -->
<el-dialog v-model="createMachineCodeDialogVisible" title="创建机器码" :width="dialogWidth" :close-on-click-modal="false" :fullscreen="isMobile" class="mobile-dialog">
    <el-form :model="createMachineCodeForm" :label-width="isMobile ? '80px' : '100px'" ref="machineCodeFormRef" :label-position="isMobile ? 'top' : 'right'">
        <el-form-item label="机器码" required>
            <el-input
                v-model="createMachineCodeForm.machine_id"
                placeholder="请输入机器码"
                :size="isMobile ? 'large' : 'default'"
                maxlength="50"
                show-word-limit />
            <div style="margin-top: 8px;">
                <el-button
                    :size="isMobile ? 'default' : 'small'"
                    @click="generateMachineCode"
                    type="primary"
                    plain>
                    <el-icon><Refresh /></el-icon>
                    自动生成
                </el-button>
                <span style="color: #909399; font-size: 12px; margin-left: 8px; display: block; margin-top: 4px;">
                    或手动输入设备机器码（格式：XXXX-XXXX-XXXX-XXXX）
                </span>
            </div>
        </el-form-item>
        <el-form-item label="有效期（天）" required>
            <el-input-number
                v-model="createMachineCodeForm.days"
                :min="1"
                :max="3650"
                style="width: 100%;"
                :size="isMobile ? 'large' : 'default'"
                controls-position="right" />
            <div v-if="isMobile" style="font-size: 12px; color: #909399; margin-top: 4px;">
                常用：30天（试用）、365天（年费）、3650天（永久）
            </div>
        </el-form-item>
        <el-form-item label="备注">
            <el-input
                v-model="createMachineCodeForm.notes"
                type="textarea"
                :rows="isMobile ? 4 : 3"
                placeholder="请输入备注信息"
                :size="isMobile ? 'large' : 'default'"
                maxlength="500"
                show-word-limit />
        </el-form-item>
    </el-form>

    <!-- 创建成功结果显示 -->
    <div v-if="showMachineResult" style="margin-bottom: 20px; padding: 16px; background-color: #f0f9ff; border: 1px solid #e6a23c; border-radius: 8px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px;">
            <el-icon color="#e6a23c" size="20" style="margin-right: 8px;"><SuccessFilled /></el-icon>
            <span style="font-weight: 500; color: #e6a23c;">机器码激活码创建成功！</span>
        </div>
        <div style="margin-bottom: 8px;">
            <span style="color: #606266;">激活码：</span>
            <span style="font-family: monospace; background-color: #f5f5f5; padding: 4px 8px; border-radius: 4px; color: #303133;" v-text="createdMachineCode"></span>
            <el-button size="small" type="warning" plain style="margin-left: 8px;" @click="copyMachineCode">
                <el-icon><CopyDocument /></el-icon>
                复制
            </el-button>
        </div>
        <div style="font-size: 12px; color: #909399;">
            机器码激活码已创建，您可以在机器码激活码页面查看详情
        </div>
    </div>

    <template #footer>
        <span class="dialog-footer" :class="{ 'mobile-footer': isMobile }">
            <el-button
                @click="closeMachineDialog"
                :size="isMobile ? 'large' : 'default'"
                :style="isMobile ? 'flex: 1; margin-right: 12px;' : ''">
                <span v-text="showMachineResult ? '关闭' : '取消'"></span>
            </el-button>
            <el-button
                v-if="!showMachineResult"
                type="primary"
                @click="createMachineCode"
                :loading="loading"
                :size="isMobile ? 'large' : 'default'"
                :style="isMobile ? 'flex: 1;' : ''">
                创建机器码
            </el-button>
        </span>
    </template>
</el-dialog>

<style scoped>
.quick-action-card {
    text-align: center;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    border-radius: 12px;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.quick-action-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
.action-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: white;
}
.quick-action-card h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #303133;
}
.quick-action-card p {
    margin: 0;
    color: #606266;
    font-size: 14px;
    line-height: 1.4;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 16px;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .mobile-tip {
        display: block !important;
    }
    .quick-action-card {
        min-height: 240px;
        margin-bottom: 16px;
    }
    .action-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 12px;
    }
    .quick-action-card h3 {
        font-size: 16px;
        margin-bottom: 6px;
    }
    .quick-action-card p {
        font-size: 13px;
        padding: 0 12px;
    }
}

@media (max-width: 480px) {
    .quick-action-card {
        min-height: 200px;
        margin-bottom: 12px;
    }
    .action-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 10px;
    }
    .quick-action-card h3 {
        font-size: 15px;
    }
    .quick-action-card p {
        font-size: 12px;
        padding: 0 8px;
    }
}

/* 移动端对话框优化 */
@media (max-width: 768px) {
    .mobile-dialog .el-dialog {
        margin: 0 !important;
        width: 100% !important;
        height: 100% !important;
        max-width: none !important;
        border-radius: 0 !important;
    }
    .mobile-dialog .el-dialog__header {
        padding: 16px 20px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;
    }
    .mobile-dialog .el-dialog__body {
        padding: 20px;
        max-height: calc(100vh - 120px);
        overflow-y: auto;
    }
    .mobile-dialog .el-dialog__footer {
        padding: 16px 20px;
        background-color: #f5f7fa;
        border-top: 1px solid #e4e7ed;
        position: sticky;
        bottom: 0;
    }
    .mobile-dialog .el-form-item {
        margin-bottom: 20px;
    }
    .mobile-dialog .el-form-item__label {
        font-size: 14px;
        font-weight: 500;
        padding-bottom: 8px;
    }
    .mobile-dialog .el-input__inner,
    .mobile-dialog .el-textarea__inner,
    .mobile-dialog .el-select .el-input__inner {
        font-size: 16px;
        padding: 12px;
        min-height: 44px;
    }
    .mobile-dialog .el-button {
        min-height: 44px;
        font-size: 16px;
        padding: 12px 20px;
    }
    .mobile-dialog .el-input-number {
        width: 100%;
    }
    .mobile-dialog .el-input-number .el-input__inner {
        text-align: left;
    }
    .mobile-footer {
        display: flex !important;
        gap: 0 !important;
    }
    .mobile-footer .el-button {
        border-radius: 8px !important;
    }
}

/* 状态下拉框样式优化 */
.status-select .el-select-dropdown .el-option {
    height: auto !important;
    padding: 10px 16px !important;
    line-height: 1.4 !important;
}

.status-option {
    width: 100%;
}

.status-main {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-tag {
    flex-shrink: 0;
    font-size: 12px !important;
    padding: 2px 8px !important;
    border-radius: 4px !important;
}

.status-description {
    color: #606266;
    font-size: 12px;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.status-select .el-select-dropdown .el-option:hover {
    background-color: #f5f7fa;
}

.status-select .el-select-dropdown .el-option.selected {
    background-color: #ecf5ff;
    color: #409eff;
}

/* 移动端下拉框优化 */
@media (max-width: 768px) {
    .status-select .el-select-dropdown .el-option {
        padding: 12px 16px !important;
        min-height: 56px !important;
    }

    .status-main {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .status-tag {
        font-size: 11px !important;
        padding: 3px 8px !important;
    }

    .status-description {
        font-size: 11px !important;
        white-space: normal;
        line-height: 1.3;
    }
}
</style>
{% endblock %}

{% block computed %}
hasRecentCodes() {
    return this.createdOrderNumber || this.createdLicenseCode || this.createdMachineCode;
},
quickActions() {
    return [
        {
            key: 'order',
            title: '创建订单',
            description: '快速创建新的产品订单，支持选择产品、代理商和设置订单信息',
            icon: 'Document',
            color: 'linear-gradient(135deg, #409eff, #337ecc)',
            buttonText: '创建订单'
        },
        {
            key: 'license',
            title: '创建授权码',
            description: '生成新的产品授权码，可设置有效期、使用次数等参数',
            icon: 'Key',
            color: 'linear-gradient(135deg, #67c23a, #529b2e)',
            buttonText: '创建授权码'
        },
        {
            key: 'machine',
            title: '创建机器码',
            description: '生成机器码激活码，用于设备绑定和离线激活',
            icon: 'Cpu',
            color: 'linear-gradient(135deg, #e6a23c, #cf9236)',
            buttonText: '创建机器码'
        }
    ];
}
{% endblock %}

{% block methods %}
checkMobile() {
    this.isMobile = window.innerWidth <= 768;
    this.dialogWidth = this.isMobile ? '100%' : '600px';
},
openDialog(type) {
    this.checkMobile();
    switch(type) {
        case 'order':
            this.createOrderDialogVisible = true;
            break;
        case 'license':
            this.createLicenseDialogVisible = true;
            break;
        case 'machine':
            this.createMachineCodeDialogVisible = true;
            break;
    }
},
onProductChange(productId) {
    const product = this.products.find(p => p.id === productId);
    if (product) {
        this.createOrderForm.unit_price = product.price || 0;
        this.calculateTotal();
    }
},
calculateTotal() {
    if (this.createOrderForm.unit_price && this.createOrderForm.quantity) {
        this.createOrderForm.total_price = this.createOrderForm.unit_price * this.createOrderForm.quantity;
    }
},
disabledDate(time) {
    // 禁用过去的日期
    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
},
closeOrderDialog() {
    this.createOrderDialogVisible = false;
    this.showOrderResult = false;
    // 不清空 createdOrderNumber，保持在页面主区域显示
    this.resetOrderForm();
    if (this.$refs.orderFormRef) {
        this.$refs.orderFormRef.clearValidate();
    }
},
copyOrderNumber() {
    navigator.clipboard.writeText(this.createdOrderNumber).then(() => {
        ElMessage.success('订单号已复制到剪贴板');
    }).catch(() => {
        ElMessage.error('复制失败');
    });
},
closeLicenseDialog() {
    this.createLicenseDialogVisible = false;
    this.showLicenseResult = false;
    // 不清空 createdLicenseCode，保持在页面主区域显示
    this.resetLicenseForm();
    if (this.$refs.licenseFormRef) {
        this.$refs.licenseFormRef.clearValidate();
    }
},
copyLicenseCode() {
    navigator.clipboard.writeText(this.createdLicenseCode).then(() => {
        ElMessage.success('授权码已复制到剪贴板');
    }).catch(() => {
        ElMessage.error('复制失败');
    });
},
closeMachineDialog() {
    this.createMachineCodeDialogVisible = false;
    this.showMachineResult = false;
    // 不清空 createdMachineCode，保持在页面主区域显示
    this.resetMachineCodeForm();
    if (this.$refs.machineCodeFormRef) {
        this.$refs.machineCodeFormRef.clearValidate();
    }
},
copyMachineCode() {
    navigator.clipboard.writeText(this.createdMachineCode).then(() => {
        ElMessage.success('机器码激活码已复制到剪贴板');
    }).catch(() => {
        ElMessage.error('复制失败');
    });
},
copyCode(code, type) {
    navigator.clipboard.writeText(code).then(() => {
        ElMessage.success(`${type}已复制到剪贴板`);
    }).catch(() => {
        ElMessage.error('复制失败');
    });
},
clearRecentCodes() {
    this.createdOrderNumber = '';
    this.createdLicenseCode = '';
    this.createdMachineCode = '';
    ElMessage.success('已清空最近创建的码');
},
generateMachineCode() {
    // 生成随机机器码
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
        if (i > 0 && i % 4 === 0) result += '-';
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    this.createMachineCodeForm.machine_id = result;
},
async createOrder() {
    // 验证表单
    if (!this.$refs.orderFormRef) {
        ElMessage.error('表单引用错误');
        return;
    }

    try {
        await this.$refs.orderFormRef.validate();
    } catch (error) {
        ElMessage.error('请填写必填字段');
        return;
    }

    this.loading = true;
    try {
        // 准备订单数据
        const orderData = {
            order_number: this.createOrderForm.order_number || null,
            product_id: parseInt(this.createOrderForm.product_id),
            agent_id: this.createOrderForm.agent_id ? parseInt(this.createOrderForm.agent_id) : null,
            quantity: parseInt(this.createOrderForm.quantity),
            unit_price: parseFloat(this.createOrderForm.unit_price) || null,
            total_price: parseFloat(this.createOrderForm.total_price) || null,
            status: this.createOrderForm.status,
            customer_info: this.createOrderForm.customer_info || null,
            notes: this.createOrderForm.notes || null,
            expire_date: this.createOrderForm.expire_date,
            created_by_admin: true
        };

        console.log('发送订单数据:', orderData);

        const response = await fetch('/api/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });

        if (response.ok) {
            const result = await response.json();
            this.createdOrderNumber = result.order_number;
            this.showOrderResult = true;
            ElMessage.success('订单创建成功');
        } else {
            const error = await response.json();
            console.error('订单创建失败:', error);
            ElMessage.error(error.detail || '创建失败');
        }
    } catch (error) {
        console.error('网络错误:', error);
        ElMessage.error('网络错误');
    } finally {
        this.loading = false;
    }
},
async createLicense() {
    if (!this.createLicenseForm.product_id) {
        ElMessage.error('请选择产品');
        return;
    }

    this.loading = true;
    try {
        // 处理数据格式并确保类型正确
        const licenseData = {
            product_id: parseInt(this.createLicenseForm.product_id),
            agent_id: this.createLicenseForm.agent_id ? parseInt(this.createLicenseForm.agent_id) : null,
            max_api_calls: parseInt(this.createLicenseForm.max_api_calls),
            expire_date: this.createLicenseForm.expire_date || null,
            notes: this.createLicenseForm.notes || null
        };

        // 处理过期时间格式
        if (licenseData.expire_date) {
            licenseData.expire_date = new Date(licenseData.expire_date).toISOString();
        }

        console.log('发送授权码数据:', licenseData);

        const response = await fetch('/api/licenses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(licenseData)
        });

        if (response.ok) {
            const result = await response.json();
            this.createdLicenseCode = result.license_code;
            this.showLicenseResult = true;
            ElMessage.success('授权码创建成功');
        } else {
            const error = await response.json();
            console.error('授权码创建失败:', error);
            ElMessage.error(error.detail || '创建失败');
        }
    } catch (error) {
        console.error('网络错误:', error);
        ElMessage.error('网络错误');
    } finally {
        this.loading = false;
    }
},
async createMachineCode() {
    if (!this.createMachineCodeForm.machine_id) {
        ElMessage.error('请填写机器码');
        return;
    }

    this.loading = true;
    try {
        const response = await fetch('/api/machine-activation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.createMachineCodeForm)
        });

        if (response.ok) {
            const result = await response.json();
            this.createdMachineCode = result.activation_code;
            this.showMachineResult = true;
            ElMessage.success('机器码激活码创建成功');
        } else {
            const error = await response.json();
            ElMessage.error(error.detail || '创建失败');
        }
    } catch (error) {
        ElMessage.error('网络错误');
    } finally {
        this.loading = false;
    }
},
resetOrderForm() {
    this.createOrderForm = {
        order_number: '',
        product_id: null,
        agent_id: null,
        quantity: 1,
        unit_price: null,
        total_price: null,
        status: 'PENDING',
        customer_info: '',
        notes: '',
        expire_date: null
    };
},
resetLicenseForm() {
    this.createLicenseForm = {
        product_id: '',
        agent_id: '',
        max_api_calls: -1,
        expire_date: null,
        notes: ''
    };
},
resetMachineCodeForm() {
    this.createMachineCodeForm = {
        machine_id: '',
        days: 30,
        notes: ''
    };
}
{% endblock %}

{% block mounted %}
// 初始化移动端检测
this.checkMobile();

// 监听窗口大小变化
window.addEventListener('resize', () => {
    this.checkMobile();
});

// 监听设备方向变化（移动端）
window.addEventListener('orientationchange', () => {
    setTimeout(() => {
        this.checkMobile();
    }, 100);
});
{% endblock %}

{% block beforeUnmount %}
// 页面离开时清空最近创建的码
this.createdOrderNumber = '';
this.createdLicenseCode = '';
this.createdMachineCode = '';
{% endblock %}
