from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models import Agent
from app.schemas.agent import AgentCreate, AgentUpdate
from app.services.admin_service import AdminService
import logging

logger = logging.getLogger(__name__)

class AgentService:
    """代理商管理服务"""
    
    @staticmethod
    def create_agent(db: Session, agent_data: AgentCreate) -> Optional[Agent]:
        """创建代理商"""
        try:
            # 检查用户名是否已存在
            existing_agent = db.query(Agent).filter(Agent.username == agent_data.username).first()
            if existing_agent:
                logger.error(f"Agent username {agent_data.username} already exists")
                return None
            
            # 哈希密码
            password_hash = AdminService.get_password_hash(agent_data.password)
            
            agent = Agent(
                username=agent_data.username,
                password_hash=password_hash,
                company_name=agent_data.company_name,
                contact_name=agent_data.contact_name,
                contact_email=agent_data.contact_email,
                contact_phone=agent_data.contact_phone,
                address=agent_data.address,
                description=agent_data.description,
                is_active=agent_data.is_active
            )
            
            db.add(agent)
            db.commit()
            db.refresh(agent)
            
            logger.info(f"Agent created: {agent.username} ({agent.company_name})")
            return agent
            
        except Exception as e:
            logger.error(f"Error creating agent: {str(e)}")
            db.rollback()
            return None
    
    @staticmethod
    def get_agent_by_id(db: Session, agent_id: int) -> Optional[Agent]:
        """根据ID获取代理商"""
        try:
            return db.query(Agent).filter(Agent.id == agent_id).first()
        except Exception as e:
            logger.error(f"Error getting agent by id {agent_id}: {str(e)}")
            return None
    
    @staticmethod
    def get_agent_by_username(db: Session, username: str) -> Optional[Agent]:
        """根据用户名获取代理商"""
        try:
            return db.query(Agent).filter(Agent.username == username).first()
        except Exception as e:
            logger.error(f"Error getting agent by username {username}: {str(e)}")
            return None
    
    @staticmethod
    def get_agents(db: Session, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None) -> List[Agent]:
        """获取代理商列表"""
        try:
            query = db.query(Agent)
            
            if is_active is not None:
                query = query.filter(Agent.is_active == is_active)
            
            return query.offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error getting agents: {str(e)}")
            return []
    
    @staticmethod
    def update_agent(db: Session, agent_id: int, agent_data: AgentUpdate) -> Optional[Agent]:
        """更新代理商"""
        try:
            agent = db.query(Agent).filter(Agent.id == agent_id).first()
            if not agent:
                logger.error(f"Agent {agent_id} not found")
                return None
            
            # 检查用户名是否与其他代理商冲突
            if agent_data.username and agent_data.username != agent.username:
                existing_agent = db.query(Agent).filter(
                    and_(Agent.username == agent_data.username, Agent.id != agent_id)
                ).first()
                if existing_agent:
                    logger.error(f"Agent username {agent_data.username} already exists")
                    return None
            
            # 更新字段
            update_data = agent_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(agent, field, value)
            
            db.commit()
            db.refresh(agent)
            
            logger.info(f"Agent updated: {agent.username} ({agent.company_name})")
            return agent
            
        except Exception as e:
            logger.error(f"Error updating agent {agent_id}: {str(e)}")
            db.rollback()
            return None
    
    @staticmethod
    def delete_agent(db: Session, agent_id: int) -> bool:
        """删除代理商（软删除）"""
        try:
            agent = db.query(Agent).filter(Agent.id == agent_id).first()
            if not agent:
                logger.error(f"Agent {agent_id} not found")
                return False
            
            # 软删除：设置为不激活
            agent.is_active = False
            db.commit()
            
            logger.info(f"Agent deleted: {agent.username} ({agent.company_name})")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting agent {agent_id}: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def search_agents(db: Session, keyword: str, skip: int = 0, limit: int = 100) -> List[Agent]:
        """搜索代理商"""
        try:
            query = db.query(Agent).filter(
                and_(
                    Agent.is_active == True,
                    (Agent.username.contains(keyword) | 
                     Agent.company_name.contains(keyword) | 
                     Agent.contact_name.contains(keyword) |
                     Agent.contact_email.contains(keyword))
                )
            )
            
            return query.offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error searching agents with keyword {keyword}: {str(e)}")
            return []
    
    @staticmethod
    def get_agent_count(db: Session, is_active: Optional[bool] = None) -> int:
        """获取代理商总数"""
        try:
            query = db.query(Agent)
            
            if is_active is not None:
                query = query.filter(Agent.is_active == is_active)
            
            return query.count()
            
        except Exception as e:
            logger.error(f"Error getting agent count: {str(e)}")
            return 0
    
    @staticmethod
    def authenticate_agent(db: Session, username: str, password: str) -> Optional[Agent]:
        """代理商认证"""
        try:
            agent = db.query(Agent).filter(
                and_(Agent.username == username, Agent.is_active == True)
            ).first()
            
            if not agent:
                logger.warning(f"Agent {username} not found or inactive")
                return None
            
            if not AdminService.verify_password(password, agent.password_hash):
                logger.warning(f"Invalid password for agent {username}")
                return None
            
            logger.info(f"Agent authenticated: {username}")
            return agent
            
        except Exception as e:
            logger.error(f"Error authenticating agent {username}: {str(e)}")
            return None
    
    @staticmethod
    def update_agent_password(db: Session, agent_id: int, old_password: str, new_password: str) -> bool:
        """更新代理商密码"""
        try:
            agent = db.query(Agent).filter(Agent.id == agent_id).first()
            if not agent:
                logger.error(f"Agent {agent_id} not found")
                return False
            
            # 验证旧密码
            if not AdminService.verify_password(old_password, agent.password_hash):
                logger.error(f"Invalid old password for agent {agent_id}")
                return False
            
            # 更新密码
            agent.password_hash = AdminService.get_password_hash(new_password)
            db.commit()
            
            logger.info(f"Password updated for agent {agent.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating password for agent {agent_id}: {str(e)}")
            db.rollback()
            return False
