#!/usr/bin/env python3
"""
测试支付宝回调地址设置
"""

import requests
import json

def test_create_payment_with_notify():
    """测试创建支付订单时的回调地址设置"""
    print("🔍 测试支付宝回调地址设置")
    print("=" * 60)
    
    # 创建支付订单
    print("\n1. 创建支付订单...")
    response = requests.post(
        "http://localhost:8008/api/payment/face-to-face",
        json={
            "user_id": "test_notify_user",
            "product_id": 1,
            "timeout_minutes": 30
        }
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        if data.get("success"):
            order_no = data.get("order_no")
            qr_code = data.get("qr_code")
            print(f"✅ 订单创建成功: {order_no}")
            print(f"   二维码: {qr_code}")
            
            # 检查二维码是否是真实的支付宝链接
            if qr_code and qr_code.startswith("https://qr.alipay.com/"):
                print("✅ 获得真实支付宝二维码")
                print("📋 请检查服务器日志，应该看到:")
                print("   - '设置支付回调地址: http://c57d4f98.natappfree.cc/api/payment/alipay/notify'")
                print("   - '支付宝请求参数 - ... 回调: http://c57d4f98.natappfree.cc/api/payment/alipay/notify'")
            else:
                print("⚠️  使用模拟二维码，可能SDK配置有问题")
        else:
            print(f"❌ 订单创建失败: {data.get('error')}")
    else:
        print(f"❌ 请求失败: {response.status_code}")

def test_notify_endpoint():
    """测试回调端点是否可访问"""
    print(f"\n2. 测试回调端点可访问性...")
    
    # 测试GET请求（支付宝可能会先发GET请求验证URL）
    try:
        response = requests.get("http://localhost:8008/api/payment/alipay/notify", timeout=5)
        print(f"GET请求状态码: {response.status_code}")
        print(f"GET请求响应: {response.text}")
    except Exception as e:
        print(f"GET请求失败: {str(e)}")
    
    # 测试POST请求（模拟支付宝回调）
    try:
        test_notify_data = {
            "out_trade_no": "TEST_ORDER_123",
            "trade_status": "TRADE_SUCCESS",
            "total_amount": "0.01",
            "trade_no": "TEST_TRADE_NO_123"
        }
        
        response = requests.post(
            "http://localhost:8008/api/payment/alipay/notify",
            data=test_notify_data,
            timeout=5
        )
        print(f"POST请求状态码: {response.status_code}")
        print(f"POST请求响应: {response.text}")
        print("📋 请检查服务器日志，应该看到回调处理日志")
    except Exception as e:
        print(f"POST请求失败: {str(e)}")

def test_external_access():
    """测试外网访问"""
    print(f"\n3. 测试外网访问...")
    
    external_url = "http://c57d4f98.natappfree.cc/api/payment/alipay/notify"
    
    try:
        response = requests.get(external_url, timeout=10)
        print(f"外网GET访问状态码: {response.status_code}")
        print(f"外网GET访问响应: {response.text}")
        print("✅ 外网可以访问回调地址")
    except Exception as e:
        print(f"❌ 外网访问失败: {str(e)}")
        print("⚠️  这可能是回调不生效的原因！")

def check_alipay_config():
    """检查支付宝配置"""
    print(f"\n4. 检查支付宝配置...")
    
    try:
        from app.services.alipay_service import AlipayService
        
        service = AlipayService()
        print(f"APP_ID: {service.app_id}")
        print(f"网关地址: {service.gateway_url}")
        print(f"回调地址: {service.notify_url}")
        print(f"跳转地址: {service.return_url}")
        print(f"私钥配置: {'已配置' if service.app_private_key else '未配置'}")
        print(f"公钥配置: {'已配置' if service.alipay_public_key else '未配置'}")
        
        if service.notify_url == "http://c57d4f98.natappfree.cc/api/payment/alipay/notify":
            print("✅ 回调地址配置正确")
        else:
            print("❌ 回调地址配置错误")
            
    except Exception as e:
        print(f"❌ 检查配置失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 支付宝回调地址测试")
    print("此测试将验证回调地址是否正确设置和可访问")
    
    # 1. 测试创建支付订单
    test_create_payment_with_notify()
    
    # 2. 测试回调端点
    test_notify_endpoint()
    
    # 3. 测试外网访问
    test_external_access()
    
    # 4. 检查配置
    check_alipay_config()
    
    print(f"\n" + "=" * 60)
    print("📋 回调问题排查清单:")
    print("1. ✓ 检查.env中ALIPAY_NOTIFY_URL配置")
    print("2. ✓ 检查支付宝开放平台应用网关地址配置")
    print("3. ✓ 检查内网穿透是否正常工作")
    print("4. ✓ 检查回调端点是否可访问")
    print("5. ✓ 检查服务器日志中的回调设置信息")
    
    print(f"\n💡 可能的问题:")
    print("- 支付宝开放平台的应用网关地址未正确配置")
    print("- 内网穿透地址变化了")
    print("- 支付宝SDK版本问题导致notify_url未正确传递")
    print("- 沙箱环境的回调机制可能有延迟")
    
    print(f"\n🔧 建议操作:")
    print("1. 确认支付宝开放平台应用网关地址: http://c57d4f98.natappfree.cc")
    print("2. 检查内网穿透是否稳定")
    print("3. 查看支付宝开放平台的回调日志")
    print("4. 尝试手动触发回调测试")

if __name__ == "__main__":
    main()
