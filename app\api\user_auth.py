from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime, timedelta
from app.database import get_db
from app.models import User, License, Product, ApiCallLog, UserApiKey
from app.services.admin_service import AdminService
from app.utils.password import verify_password
import logging
import secrets
import string

logger = logging.getLogger(__name__)

router = APIRouter()

class UserLogin(BaseModel):
    username: str
    password: str

class UserRegister(BaseModel):
    username: str
    password: str
    email: str
    phone: str = None

class UserProfileUpdate(BaseModel):
    email: str = None
    phone: str = None
    full_name: str = None

class LicenseActivate(BaseModel):
    license_code: str
    device_info: str = None

class ApiKeyCreate(BaseModel):
    name: str
    description: str = None

class LicenseResponse(BaseModel):
    id: int
    license_code: str
    product_name: str
    status: str
    used_api_calls: int
    max_api_calls: int
    expire_date: Optional[datetime]
    activated_at: Optional[datetime]
    created_at: datetime
    device_info: Optional[str]
    notes: Optional[str]

class ApiLogResponse(BaseModel):
    id: int
    timestamp: datetime
    method: str
    api_path: str
    status_code: int
    response_time: int
    ip_address: str
    license_code: Optional[str]
    user_agent: Optional[str]
    request_params: Optional[dict]
    response_data: Optional[dict]
    error_message: Optional[str]

class ApiKeyResponse(BaseModel):
    id: int
    name: str
    key: str
    description: Optional[str]
    is_active: bool
    last_used: Optional[datetime]
    created_at: datetime

@router.post("/register")
async def user_register(
    register_data: UserRegister,
    db: Session = Depends(get_db)
):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == register_data.username).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )
        
        # 检查邮箱是否已存在
        existing_email = db.query(User).filter(User.email == register_data.email).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists"
            )
        
        # 创建新用户
        from app.utils.password import hash_password
        from app.models.user import UserType
        user = User(
            user_id=register_data.username,  # 使用用户名作为user_id
            username=register_data.username,
            password_hash=hash_password(register_data.password),
            email=register_data.email,
            phone=register_data.phone,
            user_type=UserType.REGULAR,
            is_active=True
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        logger.info(f"User {user.username} registered successfully")
        
        return {
            "message": "User registered successfully",
            "user_id": user.id,
            "username": user.username
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during user registration: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/login")
async def user_login(
    login_data: UserLogin,
    db: Session = Depends(get_db)
):
    """用户登录"""
    try:
        # 验证用户凭据
        user = db.query(User).filter(User.username == login_data.username).first()
        if not user or not verify_password(login_data.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is disabled"
            )
        
        # 生成JWT token
        from app.config import settings
        from datetime import timedelta
        token = AdminService.create_access_token(
            data={"sub": f"user:{user.username}", "user_id": user.id},
            secret_key=settings.secret_key,
            algorithm=settings.algorithm,
            expires_delta=timedelta(minutes=settings.access_token_expire_minutes)
        )
        
        logger.info(f"User {user.username} logged in successfully")
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "user_info": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "full_name": user.full_name,
                "user_type": user.user_type
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during user login: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/logout")
async def user_logout():
    """用户登出"""
    # 由于使用JWT，登出主要在客户端处理（删除token）
    return {"message": "Logged out successfully"}

@router.get("/me")
async def get_current_user(
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """获取当前用户信息"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "phone": user.phone,
        "full_name": user.full_name,
        "user_type": user.user_type,
        "is_active": user.is_active,
        "created_at": user.created_at
    }

@router.put("/profile")
async def update_user_profile(
    profile_data: UserProfileUpdate,
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """更新用户个人信息"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码
    
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # 更新用户信息
        if profile_data.email is not None:
            # 检查邮箱是否已被其他用户使用
            existing_email = db.query(User).filter(
                User.email == profile_data.email,
                User.id != user_id
            ).first()
            if existing_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already exists"
                )
            user.email = profile_data.email
        
        if profile_data.phone is not None:
            user.phone = profile_data.phone
        
        if profile_data.full_name is not None:
            user.full_name = profile_data.full_name
        
        db.commit()
        db.refresh(user)
        
        logger.info(f"User {user.username} profile updated successfully")
        
        return {
            "message": "Profile updated successfully",
            "user_info": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "full_name": user.full_name,
                "user_type": user.user_type
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/password")
async def change_password(
    old_password: str,
    new_password: str,
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """修改密码"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码
    
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # 验证旧密码
        if not verify_password(old_password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid old password"
            )
        
        # 更新密码
        from app.utils.password import hash_password
        user.password_hash = hash_password(new_password)
        
        db.commit()
        
        logger.info(f"User {user.username} password changed successfully")
        
        return {"message": "Password changed successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error changing password: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# 授权管理相关API
@router.get("/licenses/stats")
async def get_license_stats(
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """获取用户授权统计"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 获取用户的授权统计
        total_licenses = db.query(License).filter(License.user_id == user_id).count()
        active_licenses = db.query(License).filter(
            and_(License.user_id == user_id, License.status == 'active')
        ).count()

        # 即将过期的授权（7天内）
        expire_date = datetime.now() + timedelta(days=7)
        expiring_licenses = db.query(License).filter(
            and_(
                License.user_id == user_id,
                License.status == 'active',
                License.expire_date <= expire_date,
                License.expire_date > datetime.now()
            )
        ).count()

        # 总API调用数
        total_api_calls = db.query(func.sum(License.used_api_calls)).filter(
            License.user_id == user_id
        ).scalar() or 0

        return {
            "total": total_licenses,
            "active": active_licenses,
            "expiring": expiring_licenses,
            "totalApiCalls": total_api_calls
        }

    except Exception as e:
        logger.error(f"Error getting license stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/licenses")
async def get_user_licenses(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: str = Query("", description="搜索关键词"),
    status: str = Query("", description="授权状态"),
    product: str = Query("", description="产品ID"),
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """获取用户授权列表"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 构建查询
        query = db.query(License).join(Product).filter(License.user_id == user_id)

        # 搜索过滤
        if keyword:
            query = query.filter(
                or_(
                    License.license_code.contains(keyword),
                    Product.name.contains(keyword)
                )
            )

        if status:
            query = query.filter(License.status == status)

        if product:
            query = query.filter(License.product_id == product)

        # 总数
        total = query.count()

        # 分页
        licenses = query.offset((page - 1) * size).limit(size).all()

        # 格式化响应
        license_list = []
        for license in licenses:
            license_list.append({
                "id": license.id,
                "license_code": license.license_code,
                "product_name": license.product.name,
                "status": license.status,
                "used_api_calls": license.used_api_calls,
                "max_api_calls": license.max_api_calls,
                "expire_date": license.expire_date,
                "activated_at": license.activated_at,
                "created_at": license.created_at,
                "device_info": license.device_info,
                "notes": license.notes
            })

        return {
            "licenses": license_list,
            "total": total,
            "page": page,
            "size": size
        }

    except Exception as e:
        logger.error(f"Error getting user licenses: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/licenses/activate")
async def activate_license(
    activate_data: LicenseActivate,
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """激活授权码"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 查找授权码
        license = db.query(License).filter(
            License.license_code == activate_data.license_code
        ).first()

        if not license:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="授权码不存在"
            )

        if license.status == 'active':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="授权码已激活"
            )

        if license.status == 'expired':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="授权码已过期"
            )

        # 激活授权
        license.user_id = user_id
        license.status = 'active'
        license.activated_at = datetime.now()
        if activate_data.device_info:
            license.device_info = activate_data.device_info

        db.commit()

        logger.info(f"License {license.license_code} activated by user {user_id}")

        return {"message": "授权激活成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error activating license: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# API使用统计相关API
@router.get("/api-usage/stats")
async def get_api_usage_stats(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    license_id: Optional[int] = Query(None),
    product_id: Optional[int] = Query(None),
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """获取API使用统计"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 构建查询条件
        query = db.query(ApiCallLog).join(License).filter(License.user_id == user_id)

        # 日期过滤
        if start_date:
            query = query.filter(ApiCallLog.call_time >= datetime.fromisoformat(start_date))
        if end_date:
            query = query.filter(ApiCallLog.call_time <= datetime.fromisoformat(end_date))

        # 授权过滤
        if license_id:
            query = query.filter(ApiCallLog.license_id == license_id)

        # 产品过滤
        if product_id:
            query = query.filter(License.product_id == product_id)

        # 统计数据
        total_calls = query.count()
        success_calls = query.filter(ApiCallLog.response_status < 400).count()
        failed_calls = total_calls - success_calls

        # 平均响应时间
        avg_response_time = db.query(func.avg(ApiCallLog.response_time)).filter(
            ApiCallLog.id.in_(query.with_entities(ApiCallLog.id).subquery())
        ).scalar() or 0

        return {
            "totalCalls": total_calls,
            "successCalls": success_calls,
            "failedCalls": failed_calls,
            "avgResponseTime": round(avg_response_time, 2)
        }

    except Exception as e:
        logger.error(f"Error getting API usage stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/api-usage/logs")
async def get_api_usage_logs(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: str = Query("", description="搜索关键词"),
    status: str = Query("", description="状态过滤"),
    method: str = Query("", description="请求方法"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    license_id: Optional[int] = Query(None),
    product_id: Optional[int] = Query(None),
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """获取API调用日志"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 构建查询
        query = db.query(ApiCallLog).join(License).filter(License.user_id == user_id)

        # 搜索过滤
        if keyword:
            query = query.filter(
                or_(
                    ApiCallLog.api_endpoint.contains(keyword),
                    ApiCallLog.ip_address.contains(keyword)
                )
            )

        # 状态过滤
        if status == "success":
            query = query.filter(ApiCallLog.response_status < 400)
        elif status == "error":
            query = query.filter(ApiCallLog.response_status >= 400)

        # 请求方法过滤
        if method:
            query = query.filter(ApiCallLog.method == method)

        # 日期过滤
        if start_date:
            query = query.filter(ApiCallLog.call_time >= datetime.fromisoformat(start_date))
        if end_date:
            query = query.filter(ApiCallLog.call_time <= datetime.fromisoformat(end_date))

        # 授权和产品过滤
        if license_id:
            query = query.filter(ApiCallLog.license_id == license_id)
        if product_id:
            query = query.filter(License.product_id == product_id)

        # 总数
        total = query.count()

        # 分页和排序
        logs = query.order_by(ApiCallLog.call_time.desc()).offset((page - 1) * size).limit(size).all()

        # 格式化响应
        log_list = []
        for log in logs:
            log_list.append({
                "id": log.id,
                "timestamp": log.call_time,
                "method": log.method,
                "api_path": log.api_endpoint,
                "status_code": log.response_status,
                "response_time": log.response_time,
                "ip_address": log.ip_address,
                "license_code": log.license.license_code if log.license else None,
                "user_agent": log.user_agent,
                "request_params": log.request_data,
                "response_data": None,  # ApiCallLog 模型中没有这个字段
                "error_message": log.error_message
            })

        return {
            "logs": log_list,
            "total": total,
            "page": page,
            "size": size
        }

    except Exception as e:
        logger.error(f"Error getting API usage logs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# 个人设置相关API
@router.get("/stats")
async def get_user_stats(
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """获取用户账户统计"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 授权统计
        total_licenses = db.query(License).filter(License.user_id == user_id).count()
        active_licenses = db.query(License).filter(
            and_(License.user_id == user_id, License.status == 'active')
        ).count()

        # API调用统计
        total_api_calls = db.query(func.sum(License.used_api_calls)).filter(
            License.user_id == user_id
        ).scalar() or 0

        # 本月API调用
        start_of_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_api_calls = db.query(func.count(ApiCallLog.id)).join(License).filter(
            and_(
                License.user_id == user_id,
                ApiCallLog.timestamp >= start_of_month
            )
        ).scalar() or 0

        # 最后登录时间（这里简化处理）
        last_login = datetime.now() - timedelta(hours=2)  # 模拟数据

        return {
            "totalLicenses": total_licenses,
            "activeLicenses": active_licenses,
            "totalApiCalls": total_api_calls,
            "monthlyApiCalls": monthly_api_calls,
            "lastLogin": last_login
        }

    except Exception as e:
        logger.error(f"Error getting user stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/activities")
async def get_user_activities(
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """获取用户最近活动"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 这里简化处理，返回模拟数据
        # 实际应用中可以从专门的活动日志表中获取
        activities = [
            {
                "id": 1,
                "type": "login",
                "description": "用户登录",
                "timestamp": datetime.now() - timedelta(hours=1)
            },
            {
                "id": 2,
                "type": "license_activate",
                "description": "激活授权码 ABC123",
                "timestamp": datetime.now() - timedelta(hours=3)
            },
            {
                "id": 3,
                "type": "api_call",
                "description": "API调用 /api/verify",
                "timestamp": datetime.now() - timedelta(hours=5)
            }
        ]

        return activities[:limit]

    except Exception as e:
        logger.error(f"Error getting user activities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# API密钥管理
@router.get("/api-keys")
async def get_user_api_keys(
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """获取用户API密钥列表"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 注意：这里需要先创建 UserApiKey 模型
        # 暂时返回模拟数据
        api_keys = [
            {
                "id": 1,
                "name": "默认密钥",
                "key": "sk_test_" + "".join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32)),
                "description": "用于API调用的默认密钥",
                "is_active": True,
                "last_used": datetime.now() - timedelta(hours=2),
                "created_at": datetime.now() - timedelta(days=30)
            }
        ]

        return api_keys

    except Exception as e:
        logger.error(f"Error getting user API keys: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/api-keys")
async def create_api_key(
    api_key_data: ApiKeyCreate,
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """创建新的API密钥"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 生成API密钥
        api_key = "sk_" + "".join(secrets.choice(string.ascii_letters + string.digits) for _ in range(48))

        # 这里需要保存到数据库
        # 暂时返回成功响应
        logger.info(f"API key created for user {user_id}: {api_key_data.name}")

        return {
            "message": "API密钥创建成功",
            "key": api_key
        }

    except Exception as e:
        logger.error(f"Error creating API key: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/api-keys/{key_id}/toggle")
async def toggle_api_key_status(
    key_id: int,
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """切换API密钥状态"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 这里需要实际的数据库操作
        logger.info(f"API key {key_id} status toggled by user {user_id}")

        return {"message": "API密钥状态更新成功"}

    except Exception as e:
        logger.error(f"Error toggling API key status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.delete("/api-keys/{key_id}")
async def delete_api_key(
    key_id: int,
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """删除API密钥"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码

    try:
        # 这里需要实际的数据库操作
        logger.info(f"API key {key_id} deleted by user {user_id}")

        return {"message": "API密钥删除成功"}

    except Exception as e:
        logger.error(f"Error deleting API key: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
