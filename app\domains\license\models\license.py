from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Boolean, Enum, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum

class LicenseStatus(enum.Enum):
    ACTIVE = "active"        # 激活
    INACTIVE = "inactive"    # 未激活
    EXPIRED = "expired"      # 已过期
    SUSPENDED = "suspended"  # 已暂停
    REVOKED = "revoked"      # 已撤销

class License(Base):
    __tablename__ = "licenses"
    
    id = Column(Integer, primary_key=True, index=True)
    license_code = Column(String(100), unique=True, nullable=False, comment="授权码")
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=True, comment="订单ID（可为空，表示直接生成）")
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True, comment="代理商ID（可为空，表示管理员生成）")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="产品ID")
    user_id = Column(String(100), nullable=True, comment="绑定的用户ID")
    max_api_calls = Column(Integer, default=-1, comment="最大API调用次数（-1表示无限制）")
    used_api_calls = Column(Integer, default=0, comment="已使用API调用次数")
    expire_date = Column(DateTime(timezone=True), comment="过期时间")
    status = Column(Enum(LicenseStatus), default=LicenseStatus.INACTIVE, comment="授权状态")
    device_info = Column(Text, comment="设备信息（JSON格式）")
    notes = Column(Text, comment="备注")
    activated_at = Column(DateTime(timezone=True), comment="激活时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    order = relationship("Order", backref="licenses")
    agent = relationship("Agent", backref="licenses")
    product = relationship("Product", backref="licenses")
    
    def __repr__(self):
        return f"<License(license_code='{self.license_code}', status='{self.status.value}', user_id='{self.user_id}')>"
