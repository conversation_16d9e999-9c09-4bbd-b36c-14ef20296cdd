{% extends "agent/base.html" %}

{% block title %}仪表板{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<div id="agent-dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
            <el-card class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon products">
                        <el-icon><Box /></el-icon>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">{% raw %}{{ stats.authorizedProducts }}{% endraw %}</div>
                        <div class="stat-label">授权产品</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        
        <el-col :span="6">
            <el-card class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon licenses">
                        <el-icon><Key /></el-icon>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">{% raw %}{{ stats.totalLicenses }}{% endraw %}</div>
                        <div class="stat-label">总授权码</div>
                    </div>
                </div>
            </el-card>
        </el-col>

        <el-col :span="6">
            <el-card class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon active">
                        <el-icon><CircleCheckFilled /></el-icon>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">{% raw %}{{ stats.activeLicenses }}{% endraw %}</div>
                        <div class="stat-label">激活授权</div>
                    </div>
                </div>
            </el-card>
        </el-col>

        <el-col :span="6">
            <el-card class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon orders">
                        <el-icon><Document /></el-icon>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">{% raw %}{{ stats.totalOrders }}{% endraw %}</div>
                        <div class="stat-label">总订单</div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
        <el-col :span="12">
            <el-card>
                <template #header>
                    <div class="card-header">
                        <span>快速操作</span>
                    </div>
                </template>
                <div class="action-buttons">
                    <el-button type="primary" @click="navigate('/agent/licenses')" style="margin-bottom: 10px;">
                        <el-icon><Plus /></el-icon>
                        生成授权码
                    </el-button>
                    <el-button type="success" @click="navigate('/agent/orders')" style="margin-bottom: 10px;">
                        <el-icon><Document /></el-icon>
                        创建订单
                    </el-button>
                    <el-button type="info" @click="navigate('/agent/products')">
                        <el-icon><View /></el-icon>
                        查看授权产品
                    </el-button>
                </div>
            </el-card>
        </el-col>
        
        <el-col :span="12">
            <el-card>
                <template #header>
                    <div class="card-header">
                        <span>最近活动</span>
                    </div>
                </template>
                <div class="recent-activities">
                    <div v-if="recentActivities.length === 0" class="no-data">
                        暂无最近活动
                    </div>
                    <div v-else>
                        <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                            <div class="activity-icon">
                                <el-icon><Clock /></el-icon>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{% raw %}{{ activity.title }}{% endraw %}</div>
                                <div class="activity-time">{% raw %}{{ formatDate(activity.time) }}{% endraw %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
    
    <!-- 授权产品概览 -->
    <el-row>
        <el-col :span="24">
            <el-card>
                <template #header>
                    <div class="card-header">
                        <span>授权产品概览</span>
                        <el-button type="text" @click="navigate('/agent/products')">查看全部</el-button>
                    </div>
                </template>
                <el-table :data="authorizedProducts" v-loading="loading">
                    <el-table-column prop="name" label="产品名称" min-width="150"></el-table-column>
                    <el-table-column prop="max_licenses" label="最大授权数" width="120"></el-table-column>
                    <el-table-column prop="used_licenses" label="已使用" width="100"></el-table-column>
                    <el-table-column label="使用率" width="120">
                        <template #default="scope">
                            <el-progress 
                                :percentage="Math.round((scope.row.used_licenses / scope.row.max_licenses) * 100)"
                                :color="getProgressColor(scope.row.used_licenses / scope.row.max_licenses)">
                            </el-progress>
                        </template>
                    </el-table-column>
                    <el-table-column prop="expire_date" label="授权到期" width="180">
                        <template #default="scope">
                            {% raw %}{{ formatDate(scope.row.expire_date) }}{% endraw %}
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" width="100">
                        <template #default="scope">
                            <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                                <span v-if="scope.row.is_active">正常</span>
                                <span v-else>已停用</span>
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </el-col>
    </el-row>
</div>
{% endblock %}

{% block data %}
stats: {
    authorizedProducts: 0,
    totalLicenses: 0,
    activeLicenses: 0,
    totalOrders: 0
},
authorizedProducts: [],
recentActivities: [],
loading: false
{% endblock %}

{% block methods %}
async loadStats() {
    try {
        const token = localStorage.getItem('agent_token');
        if (!token) {
            window.location.href = '/agent/login';
            return;
        }

        const response = await fetch('/api/agent/dashboard/stats', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            this.stats.authorizedProducts = data.authorized_products;
            this.stats.totalLicenses = data.total_licenses;
            this.stats.activeLicenses = data.active_licenses;
            this.stats.totalOrders = data.total_orders;
        } else if (response.status === 401) {
            localStorage.removeItem('agent_token');
            window.location.href = '/agent/login';
        } else {
            throw new Error('Failed to load stats');
        }
    } catch (error) {
        console.error('Failed to load stats:', error);
        ElMessage.error('加载统计数据失败');
    }
},

async loadAuthorizedProducts() {
    this.loading = true;
    try {
        const token = localStorage.getItem('agent_token');
        if (!token) {
            window.location.href = '/agent/login';
            return;
        }

        const response = await fetch('/api/agent/dashboard/authorized-products', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            this.authorizedProducts = data.map(item => ({
                id: item.id,
                name: item.product_name,
                max_licenses: item.max_licenses,
                used_licenses: item.used_licenses,
                expire_date: item.expire_date,
                is_active: item.is_active && !item.is_expired
            }));
        } else if (response.status === 401) {
            localStorage.removeItem('agent_token');
            window.location.href = '/agent/login';
        } else {
            throw new Error('Failed to load authorized products');
        }
    } catch (error) {
        console.error('Failed to load authorized products:', error);
        ElMessage.error('加载授权产品失败');
    } finally {
        this.loading = false;
    }
},

async loadRecentActivities() {
    try {
        const token = localStorage.getItem('agent_token');
        if (!token) return;

        const response = await fetch('/api/agent/dashboard/recent-activities?limit=5', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            this.recentActivities = data.map(item => ({
                id: item.id,
                title: item.title,
                time: item.time
            }));
        } else {
            console.warn('Failed to load recent activities');
        }
    } catch (error) {
        console.error('Failed to load recent activities:', error);
    }
},

formatDate(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('zh-CN');
},

getUsagePercentage(used, max) {
    return max > 0 ? Math.round((used / max) * 100) : 0;
},

getUsageColor(percentage) {
    if (percentage < 50) return '#67c23a';
    if (percentage < 80) return '#e6a23c';
    return '#f56c6c';
}
{% endblock %}

{% block mounted %}
this.loadStats();
this.loadAuthorizedProducts();
this.loadRecentActivities();


<style>
.stats-row {
    margin-bottom: 20px;
}

.stat-card {
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.stat-icon.products {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.licenses {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.active {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.orders {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-number {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.quick-actions {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.action-buttons {
    display: flex;
    flex-direction: column;
}

.action-buttons .el-button {
    width: 100%;
}

.recent-activities {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    margin-right: 10px;
    color: #666;
}

.activity-title {
    font-size: 14px;
    color: #333;
}

.activity-time {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
}

.no-data {
    text-align: center;
    color: #999;
    padding: 20px;
}
</style>
{% endblock %}
