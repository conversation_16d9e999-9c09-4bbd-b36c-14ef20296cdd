from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>teger, Foreign<PERSON>ey, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class AgentProductAuth(Base):
    __tablename__ = "agent_product_auths"
    
    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False, comment="代理商ID")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="产品ID")
    max_licenses = Column(Integer, default=0, comment="最大授权数量")
    used_licenses = Column(Integer, default=0, comment="已使用授权数量")
    expire_date = Column(DateTime(timezone=True), comment="授权过期时间")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    agent = relationship("Agent", backref="product_auths")
    product = relationship("Product", backref="agent_auths")
    
    def __repr__(self):
        return f"<AgentProductAuth(agent_id={self.agent_id}, product_id={self.product_id}, max_licenses={self.max_licenses})>"
