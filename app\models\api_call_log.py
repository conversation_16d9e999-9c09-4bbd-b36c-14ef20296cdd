from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Text, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class ApiCallLog(Base):
    __tablename__ = "api_call_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    license_id = Column(Integer, ForeignKey("licenses.id"), nullable=False, comment="授权码ID")
    user_id = Column(String(100), nullable=False, comment="用户ID")
    api_endpoint = Column(String(255), nullable=False, comment="API端点")
    method = Column(String(10), nullable=False, comment="HTTP方法")
    ip_address = Column(String(45), comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    request_data = Column(Text, comment="请求数据（JSON格式）")
    response_status = Column(Integer, comment="响应状态码")
    response_time = Column(Integer, comment="响应时间（毫秒）")
    success = Column(Boolean, default=True, comment="是否成功")
    error_message = Column(Text, comment="错误信息")
    call_time = Column(DateTime(timezone=True), server_default=func.now(), comment="调用时间")
    
    # 关联关系
    license = relationship("License", backref="api_calls")
    
    def __repr__(self):
        return f"<ApiCallLog(user_id='{self.user_id}', api_endpoint='{self.api_endpoint}', call_time='{self.call_time}')>"
