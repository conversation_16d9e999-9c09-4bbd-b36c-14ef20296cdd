from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
from app.models.user import UserType

class UserBase(BaseModel):
    user_id: str
    license_key: Optional[str] = None
    user_type: UserType = UserType.REGULAR
    email: Optional[str] = None
    phone: Optional[str] = None
    description: Optional[str] = None

class UserCreate(UserBase):
    password: Optional[str] = None
    is_active: bool = True

class UserUpdate(BaseModel):
    license_key: Optional[str] = None
    user_type: Optional[UserType] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    description: Optional[str] = None

class UserResponse(UserBase):
    id: int
    is_active: bool
    last_login_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
