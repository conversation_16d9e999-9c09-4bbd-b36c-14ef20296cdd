@echo off
chcp 65001 >nul
setlocal EnableDelayedExpansion

echo.
echo === FocuSee Update Tool ===

:: 设置云服务器URL（请替换为您的实际服务器地址）
set "SERVER_URL=http://*************:8008/api"
set "DOWNLOAD_URL=http://*************:8008/downloads"

:: 用户验证
echo 🔐 User Authentication
set /p "USER_ID=Please enter your User ID: "
set /p "ORDER_NUMBER=Please enter your Order Number: "

:: 获取系统信息用于统计
for /f "tokens=2 delims==" %%a in ('wmic os get serialnumber /value') do set "SYSTEM_ID=%%a"
for /f "tokens=2 delims==" %%a in ('wmic computersystem get name /value') do set "COMPUTER_NAME=%%a"
for /f "tokens=2 delims=[]" %%a in ('ping -4 -n 1 %COMPUTERNAME% ^| findstr "["') do set "LOCAL_IP=%%a"

:: 验证用户并记录下载
echo 🔄 Verifying user identity...
set "AUTH_DATA={\"user_id\":\"%USER_ID%\",\"order_number\":\"%ORDER_NUMBER%\",\"system_id\":\"%SYSTEM_ID%\",\"computer_name\":\"%COMPUTER_NAME%\",\"ip_address\":\"%LOCAL_IP%\"}"
set "AUTH_RESULT="

:: 使用PowerShell发送验证请求并获取结果
powershell -Command "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8; $auth = '%AUTH_DATA%' | ConvertFrom-Json | ConvertTo-Json; try { $response = Invoke-RestMethod -Uri '%SERVER_URL%/auth/verify-order' -Method Post -Body $auth -ContentType 'application/json'; $result = if ($response.success -eq $true) { 'success' } else { $response.message }; $result | Out-File -FilePath $env:TEMP\auth_result.txt -Encoding UTF8 } catch { $_.Exception.Message | Out-File -FilePath $env:TEMP\auth_result.txt -Encoding UTF8 }"
set /p AUTH_RESULT=<"%TEMP%\auth_result.txt"
del "%TEMP%\auth_result.txt" 2>nul

:: 检查验证结果
if not "%AUTH_RESULT%"=="success" (
    echo ❌ Authentication failed! Error message: %AUTH_RESULT%
    echo Please contact administrator for a valid User ID and License Key.
    pause
    exit /b 1
)

echo ✅ Authentication successful!

:: 创建防火墙规则阻断FocuSee网络连接
call :block_focusee_network

:: 创建临时目录
set "TEMP_DIR=%TEMP%\FocuSee_Update_%RANDOM%"
mkdir "%TEMP_DIR%" 2>nul

:: 记录下载统计
echo 📊 Recording download statistics...
powershell -Command "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8; try { $data = '%AUTH_DATA%' | ConvertFrom-Json; $data | Add-Member -NotePropertyName 'file' -NotePropertyValue 'Gemoo-FocuSee.zip'; $json = $data | ConvertTo-Json; Invoke-RestMethod -Uri '%SERVER_URL%/download_stats' -Method Post -Body $json -ContentType 'application/json' } catch { }" >nul 2>&1

:: 使用PowerShell下载文件
echo 📥 Downloading Gemoo-FocuSee.zip...
powershell -Command "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8; try { Invoke-WebRequest -Uri '%DOWNLOAD_URL%/Gemoo-FocuSee.zip?user=%USER_ID%&key=%LICENSE_KEY%' -OutFile '%TEMP_DIR%\Gemoo-FocuSee.zip' } catch { exit 1 }"
if %ERRORLEVEL% neq 0 (
    echo ❌ Failed to download Gemoo-FocuSee.zip! Please check your network connection or contact administrator.
) else (
    echo ✅ Gemoo-FocuSee.zip downloaded successfully!
)

:: 记录第二个文件的下载统计
powershell -Command "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8; try { $data = '%AUTH_DATA%' | ConvertFrom-Json; $data | Add-Member -NotePropertyName 'file' -NotePropertyValue 'iMobie-FocuSee.zip'; $json = $data | ConvertTo-Json; Invoke-RestMethod -Uri '%SERVER_URL%/download_stats' -Method Post -Body $json -ContentType 'application/json' } catch { }" >nul 2>&1

echo 📥 Downloading iMobie-FocuSee.zip...
powershell -Command "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8; try { Invoke-WebRequest -Uri '%DOWNLOAD_URL%/iMobie-FocuSee.zip?user=%USER_ID%&key=%LICENSE_KEY%' -OutFile '%TEMP_DIR%\iMobie-FocuSee.zip' } catch { exit 1 }"
if %ERRORLEVEL% neq 0 (
    echo ❌ Failed to download iMobie-FocuSee.zip! Please check your network connection or contact administrator.
) else (
    echo ✅ iMobie-FocuSee.zip downloaded successfully!
)

:: 处理下载的文件
call :process "%TEMP_DIR%\Gemoo-FocuSee.zip" "Gemoo"
call :process "%TEMP_DIR%\iMobie-FocuSee.zip" "iMobie"

:: 清理临时文件
echo 🧹 Cleaning up temporary files...
rmdir /s /q "%TEMP_DIR%" 2>nul

echo.
echo ✅ All done! FocuSee files have been updated
pause
exit /b

:process
set "zipPath=%~1"
set "topFolder=%~2"

set "targetDir=%APPDATA%\%topFolder%"
set "focusDir=%targetDir%\FocuSee"

echo.
echo === Processing: %~nx1 -^> %targetDir% ===

:: 检查 ZIP 文件是否存在
if not exist "%zipPath%" (
    echo ⚠️ %~nx1 not found, skipping this file...
    goto :eof
)

:: 删除旧的 FocuSee 文件夹
if exist "%focusDir%" (
    echo 🔄 Removing old FocuSee folder...
    rmdir /s /q "%focusDir%"
)

:: 创建目标目录（如果不存在）
if not exist "%targetDir%" (
    mkdir "%targetDir%"
)

:: 解压缩（使用 PowerShell）
echo 📂 Extracting files...
powershell -Command "Expand-Archive -Path '%zipPath%' -DestinationPath '%targetDir%' -Force"

echo ✅ Completed: %~nx1
goto :eof

:block_focusee_network
echo.
echo === 🔒 Blocking FocuSee Network Access ===

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️ Administrator privileges required for firewall configuration.
    echo Skipping network blocking...
    goto :eof
)

:: 常见的FocuSee安装路径
set "FOCUSEE_PATHS="C:\Program Files\FocuSee\FocuSee.exe" "C:\Program Files (x86)\FocuSee\FocuSee.exe" "D:\Program Files\FocuSee\FocuSee.exe" "D:\Program Files (x86)\FocuSee\FocuSee.exe""

:: 查找FocuSee程序路径
set "FOCUSEE_PATH="
for %%p in (%FOCUSEE_PATHS%) do (
    if exist %%p (
        set "FOCUSEE_PATH=%%~p"
        echo 📍 Found FocuSee program: %%~p
        goto :found_focusee
    )
)

:: 如果没找到，检查AppData中的路径
for %%d in ("Gemoo" "iMobie") do (
    if exist "%APPDATA%\%%~d\FocuSee\FocuSee.exe" (
        set "FOCUSEE_PATH=%APPDATA%\%%~d\FocuSee\FocuSee.exe"
        echo 📍 Found FocuSee program: %APPDATA%\%%~d\FocuSee\FocuSee.exe
        goto :found_focusee
    )
)

:: 如果仍未找到，要求用户手动输入
echo ⚠️ FocuSee program not found in common locations.
echo Please enter the full path to FocuSee.exe (or press Enter to skip):
set /p "FOCUSEE_PATH=Path: "

:: 如果用户没有输入或按了回车，跳过网络阻断
if "%FOCUSEE_PATH%"=="" (
    echo ℹ️ No path provided, skipping network blocking...
    goto :eof
)

:: 检查用户输入的路径是否存在
if not exist "%FOCUSEE_PATH%" (
    echo ❌ Specified path does not exist: %FOCUSEE_PATH%
    echo Skipping network blocking...
    goto :eof
)

:found_focusee
echo 🔒 Will block network access for: %FOCUSEE_PATH%

:: 防火墙规则名称
set "RULE_NAME=Block FocuSee Network"

:: 删除已存在的规则
echo 🔄 Removing existing firewall rules...
netsh advfirewall firewall delete rule name="%RULE_NAME%" >nul 2>&1
netsh advfirewall firewall delete rule name="%RULE_NAME% (Inbound)" >nul 2>&1

:: 创建出站规则（阻止程序连接到互联网）
echo 🚫 Creating outbound firewall rule...
netsh advfirewall firewall add rule name="%RULE_NAME%" dir=out action=block program="%FOCUSEE_PATH%" description="Block FocuSee program network access" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Outbound rule created successfully
) else (
    echo ❌ Failed to create outbound rule
)

:: 创建入站规则（阻止外部连接到程序）
echo 🚫 Creating inbound firewall rule...
netsh advfirewall firewall add rule name="%RULE_NAME% (Inbound)" dir=in action=block program="%FOCUSEE_PATH%" description="Block external network connections to FocuSee program" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Inbound rule created successfully
) else (
    echo ❌ Failed to create inbound rule
)

echo.
echo ✅ FocuSee network access has been blocked!
echo 💡 To restore network access, run these commands as Administrator:
echo    netsh advfirewall firewall delete rule name="%RULE_NAME%"
echo    netsh advfirewall firewall delete rule name="%RULE_NAME% (Inbound)"
echo.
goto :eof
