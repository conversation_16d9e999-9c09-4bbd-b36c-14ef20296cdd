{% extends "admin/base.html" %}

{% block title %}支付调试 - FocuSee管理系统{% endblock %}
{% block page_title %}支付调试{% endblock %}

{% block content %}
<style>
    .debug-card {
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .status-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }
    .status-created { background: #e6f7ff; color: #1890ff; }
    .status-payment_created { background: #fff7e6; color: #fa8c16; }
    .status-paid { background: #f6ffed; color: #52c41a; }
    .status-failed { background: #fff2f0; color: #ff4d4f; }
    .status-timeout { background: #f5f5f5; color: #8c8c8c; }

    .qr-code {
        max-width: 200px;
        max-height: 200px;
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 8px;
    }
</style>

<div id="payment-debug-app">
    <!-- 操作按钮 -->
    <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon> 新建调试
        </el-button>
        <el-button @click="showConfigDialog = true">
            <el-icon><Setting /></el-icon> 配置管理
        </el-button>
        <el-button @click="loadDebugList">
            <el-icon><Refresh /></el-icon> 刷新
        </el-button>
    </div>
    <!-- 调试记录列表 -->
    <el-card>
        <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 16px; font-weight: 500;">调试记录</span>
            </div>
        </template>
        <div v-if="debugList.length === 0" style="text-align: center; padding: 40px; color: #909399;">
            暂无调试记录
        </div>

        <div v-for="debug in debugList" :key="debug.id" class="debug-card">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h3 class="text-lg font-medium">{% raw %}{{ debug.debug_name }}{% endraw %}</h3>
                                    <p class="text-sm text-gray-500">{% raw %}{{ debug.created_at }}{% endraw %}</p>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span :class="'status-badge status-' + debug.debug_status">
                                        {% raw %}{{ getStatusText(debug.debug_status) }}{% endraw %}
                                    </span>
                                    <el-button size="small" @click="queryPayment(debug)">查询状态</el-button>
                                    <el-button size="small" type="danger" @click="deleteDebug(debug.id)">删除</el-button>
                                </div>
                            </div>
                            
                            <el-row :gutter="20">
                                <!-- 基本信息 -->
                                <el-col :span="8">
                                    <h4 style="font-weight: 500; margin-bottom: 12px;">基本信息</h4>
                                    <p><strong>支付模式:</strong> {% raw %}{{ debug.payment_mode }}{% endraw %}</p>
                                    <p><strong>用户ID:</strong> {% raw %}{{ debug.user_id }}{% endraw %}</p>
                                    <p><strong>产品ID:</strong> {% raw %}{{ debug.product_id }}{% endraw %}</p>
                                    <p><strong>金额:</strong> ¥{% raw %}{{ debug.amount }}{% endraw %}</p>
                                    <p><strong>订单号:</strong>
                                        <span v-if="debug.order_no">{% raw %}{{ debug.order_no }}{% endraw %}</span>
                                        <span v-else>未生成</span>
                                    </p>
                                </el-col>

                                <!-- 支付信息 -->
                                <el-col :span="8">
                                    <h4 style="font-weight: 500; margin-bottom: 12px;">支付信息</h4>
                                    <p><strong>交易号:</strong>
                                        <span v-if="debug.trade_no">{% raw %}{{ debug.trade_no }}{% endraw %}</span>
                                        <span v-else>无</span>
                                    </p>
                                    <p><strong>支付状态:</strong>
                                        <span v-if="debug.payment_status">{% raw %}{{ debug.payment_status }}{% endraw %}</span>
                                        <span v-else>无</span>
                                    </p>
                                    <p><strong>回调状态:</strong>
                                        <span v-if="debug.callback_received">已收到</span>
                                        <span v-else>未收到</span>
                                    </p>
                                    <p><strong>最后查询:</strong>
                                        <span v-if="debug.last_query_time">{% raw %}{{ debug.last_query_time }}{% endraw %}</span>
                                        <span v-else>无</span>
                                    </p>
                                </el-col>

                                <!-- 二维码 -->
                                <el-col :span="8" v-if="debug.qr_code || debug.qr_image">
                                    <h4 style="font-weight: 500; margin-bottom: 12px;">支付二维码</h4>
                                    <!-- 优先显示base64图片 -->
                                    <img v-if="debug.qr_image"
                                         :src="debug.qr_image"
                                         class="qr-code"
                                         style="width: 150px; height: 150px;"
                                         alt="支付二维码" />
                                    <!-- 备用：使用QRCode.js生成 -->
                                    <div v-else :id="'qr-' + debug.id" class="qr-code"></div>
                                    <p style="font-size: 12px; color: #909399; margin-top: 8px;">{% raw %}{{ debug.qr_code }}{% endraw %}</p>
                                </el-col>
                            </el-row>
                            
                            <!-- 错误信息 -->
                            <el-alert v-if="debug.error_message"
                                     title="错误信息"
                                     type="error"
                                     :description="debug.error_message"
                                     style="margin-top: 16px;"
                                     show-icon>
                            </el-alert>

                            <!-- 查询结果 -->
                            <el-alert v-if="debug.query_result"
                                     title="查询结果"
                                     type="info"
                                     style="margin-top: 16px;"
                                     show-icon>
                                <pre style="font-size: 12px; margin: 0;">{% raw %}{{ JSON.stringify(debug.query_result, null, 2) }}{% endraw %}</pre>
                            </el-alert>
                        </div>
    </el-card>

        <!-- 创建调试对话框 -->
        <el-dialog title="创建支付调试" v-model="showCreateDialog" width="600px">
            <el-form :model="createForm" label-width="120px">
                <el-form-item label="调试名称">
                    <el-input v-model="createForm.debug_name" placeholder="输入调试名称"></el-input>
                </el-form-item>
                
                <el-form-item label="支付模式">
                    <el-select v-model="createForm.payment_mode" placeholder="选择支付模式">
                        <el-option label="当面付" value="face_to_face"></el-option>
                        <el-option label="条码支付" value="barcode"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="用户ID">
                    <el-input v-model="createForm.user_id" placeholder="输入用户ID"></el-input>
                </el-form-item>
                
                <el-form-item label="产品ID">
                    <el-input-number v-model="createForm.product_id" :min="1" placeholder="选择产品"></el-input-number>
                </el-form-item>
                
                <el-form-item label="支付金额">
                    <el-input-number v-model="createForm.amount" :precision="2" :min="0.01" placeholder="输入金额"></el-input-number>
                </el-form-item>
                
                <el-form-item label="订单标题">
                    <el-input v-model="createForm.subject" placeholder="输入订单标题"></el-input>
                </el-form-item>
                
                <el-form-item label="订单描述">
                    <el-input v-model="createForm.body" type="textarea" placeholder="输入订单描述"></el-input>
                </el-form-item>
                
                <el-form-item label="超时时间">
                    <el-input-number v-model="createForm.timeout_minutes" :min="1" :max="120" placeholder="分钟"></el-input-number>
                </el-form-item>
            </el-form>
            
            <template #footer>
                <el-button @click="showCreateDialog = false">取消</el-button>
                <el-button type="primary" @click="createDebugPayment" :loading="creating">创建</el-button>
            </template>
        </el-dialog>

        <!-- 配置管理对话框 -->
        <el-dialog title="支付配置管理" v-model="showConfigDialog" width="800px">
            <div class="mb-4">
                <el-button type="primary" @click="showCreateConfigDialog = true">
                    <el-icon><Plus /></el-icon> 新建配置
                </el-button>
            </div>

            <el-table :data="configList" style="width: 100%">
                <el-table-column prop="config_name" label="配置名称" width="120"></el-table-column>
                <el-table-column prop="environment" label="环境" width="80"></el-table-column>
                <el-table-column prop="app_id" label="APP_ID" width="150"></el-table-column>
                <el-table-column label="状态" width="80">
                    <template #default="scope">
                        <el-tag :type="scope.row.is_active ? 'success' : 'info'">
                            <span v-if="scope.row.is_active">激活</span>
                            <span v-else>未激活</span>
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template #default="scope">
                        <el-button size="small" @click="editConfig(scope.row)">编辑</el-button>
                        <el-button size="small" type="success" @click="activateConfig(scope.row.id)" v-if="!scope.row.is_active">激活</el-button>
                        <el-button size="small" type="danger" @click="deleteConfig(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>

        <!-- 创建配置对话框 -->
        <el-dialog title="创建支付配置" v-model="showCreateConfigDialog" width="600px">
            <el-form :model="configForm" label-width="120px">
                <el-form-item label="配置名称">
                    <el-input v-model="configForm.config_name" placeholder="输入配置名称"></el-input>
                </el-form-item>

                <el-form-item label="环境">
                    <el-select v-model="configForm.environment" placeholder="选择环境">
                        <el-option label="沙箱环境" value="sandbox"></el-option>
                        <el-option label="生产环境" value="production"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="APP_ID">
                    <el-input v-model="configForm.app_id" placeholder="输入支付宝APP_ID"></el-input>
                </el-form-item>

                <el-form-item label="应用私钥">
                    <el-input v-model="configForm.app_private_key" type="textarea" :rows="4" placeholder="输入应用私钥"></el-input>
                </el-form-item>

                <el-form-item label="支付宝公钥">
                    <el-input v-model="configForm.alipay_public_key" type="textarea" :rows="4" placeholder="输入支付宝公钥"></el-input>
                </el-form-item>

                <el-form-item label="网关地址">
                    <el-input v-model="configForm.gateway_url" placeholder="输入网关地址"></el-input>
                </el-form-item>

                <el-form-item label="回调地址">
                    <el-input v-model="configForm.notify_url" placeholder="输入回调地址"></el-input>
                </el-form-item>

                <el-form-item label="跳转地址">
                    <el-input v-model="configForm.return_url" placeholder="输入跳转地址（可选）"></el-input>
                </el-form-item>

                <el-form-item label="描述">
                    <el-input v-model="configForm.description" type="textarea" placeholder="输入配置描述"></el-input>
                </el-form-item>
            </el-form>

            <template #footer>
                <el-button @click="showCreateConfigDialog = false">取消</el-button>
                <el-button type="primary" @click="createConfig" :loading="creatingConfig">创建</el-button>
            </template>
        </el-dialog>

        <!-- 编辑配置对话框 -->
        <el-dialog title="编辑支付配置" v-model="showEditConfigDialog" width="600px">
            <el-form :model="editConfigForm" label-width="120px">
                <el-form-item label="配置名称">
                    <el-input v-model="editConfigForm.config_name" placeholder="输入配置名称"></el-input>
                </el-form-item>

                <el-form-item label="环境">
                    <el-select v-model="editConfigForm.environment" placeholder="选择环境">
                        <el-option label="沙箱环境" value="sandbox"></el-option>
                        <el-option label="生产环境" value="production"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="APP_ID">
                    <el-input v-model="editConfigForm.app_id" placeholder="输入支付宝APP_ID"></el-input>
                </el-form-item>

                <el-form-item label="应用私钥">
                    <el-input v-model="editConfigForm.app_private_key" type="textarea" :rows="4" placeholder="输入应用私钥"></el-input>
                </el-form-item>

                <el-form-item label="支付宝公钥">
                    <el-input v-model="editConfigForm.alipay_public_key" type="textarea" :rows="4" placeholder="输入支付宝公钥"></el-input>
                </el-form-item>

                <el-form-item label="网关地址">
                    <el-input v-model="editConfigForm.gateway_url" placeholder="输入网关地址"></el-input>
                </el-form-item>

                <el-form-item label="回调地址">
                    <el-input v-model="editConfigForm.notify_url" placeholder="输入回调地址"></el-input>
                </el-form-item>

                <el-form-item label="跳转地址">
                    <el-input v-model="editConfigForm.return_url" placeholder="输入跳转地址（可选）"></el-input>
                </el-form-item>

                <el-form-item label="描述">
                    <el-input v-model="editConfigForm.description" type="textarea" placeholder="输入配置描述"></el-input>
                </el-form-item>
            </el-form>

            <template #footer>
                <el-button @click="showEditConfigDialog = false">取消</el-button>
                <el-button type="primary" @click="updateConfig" :loading="updatingConfig">更新</el-button>
            </template>
        </el-dialog>

        <!-- 支付订单对话框 -->
        <el-dialog title="支付订单" v-model="showPaymentDialog" width="500px" :close-on-click-modal="false">
            <div v-if="currentPaymentOrder" style="text-align: center;">
                <!-- 订单信息 -->
                <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 8px;">
                    <h3 style="margin: 0 0 10px 0; color: #303133;">{% raw %}{{ currentPaymentOrder.debug_name }}{% endraw %}</h3>
                    <p style="margin: 5px 0; color: #606266;"><strong>订单号:</strong> {% raw %}{{ currentPaymentOrder.order_no }}{% endraw %}</p>
                    <p style="margin: 5px 0; color: #606266;"><strong>支付金额:</strong> <span style="color: #e6a23c; font-size: 18px; font-weight: bold;">¥{% raw %}{{ currentPaymentOrder.amount }}{% endraw %}</span></p>
                    <p style="margin: 5px 0; color: #606266;"><strong>订单状态:</strong>
                        <el-tag :type="currentPaymentOrder.debug_status === 'paid' ? 'success' : 'warning'">
                            {% raw %}{{ getStatusText(currentPaymentOrder.debug_status) }}{% endraw %}
                        </el-tag>
                        <span v-if="pollingTimer" style="margin-left: 10px; color: #67c23a; font-size: 12px;">
                            <i class="el-icon-loading"></i> 自动检测中...
                        </span>
                    </p>
                </div>

                <!-- 二维码 -->
                <div v-if="currentPaymentOrder.qr_code || currentPaymentOrder.qr_image" style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 15px; color: #303133;">请使用支付宝扫码支付</h4>
                    <div style="display: flex; justify-content: center; margin-bottom: 15px;">
                        <!-- 优先显示base64图片 -->
                        <img v-if="currentPaymentOrder.qr_image"
                             :src="currentPaymentOrder.qr_image"
                             style="width: 200px; height: 200px; border: 1px solid #dcdfe6; padding: 10px; border-radius: 8px; background-color: white;"
                             alt="支付二维码" />
                        <!-- 备用：使用QRCode.js生成 -->
                        <div v-else-if="currentPaymentOrder.qr_code"
                             id="payment-qr-code"
                             style="border: 1px solid #dcdfe6; padding: 10px; border-radius: 8px; background-color: white;"></div>
                    </div>
                    <p style="font-size: 12px; color: #909399; margin: 0;">请在30分钟内完成支付</p>
                </div>

                <!-- 支付说明 -->
                <el-alert
                    title="支付说明"
                    type="info"
                    :closable="false"
                    style="margin-bottom: 20px; text-align: left;">
                    <p style="margin: 5px 0;">1. 请使用支付宝APP扫描上方二维码</p>
                    <p style="margin: 5px 0;">2. 确认支付金额后完成支付</p>
                    <p style="margin: 5px 0;">3. 系统将自动检测支付状态（每3秒检测一次）</p>
                    <p style="margin: 5px 0;">4. 支付成功后将自动完成流程</p>
                </el-alert>
            </div>

            <template #footer>
                <div style="text-align: center;">
                    <el-button @click="completePayment" type="success" v-if="currentPaymentOrder && currentPaymentOrder.debug_status === 'paid'">
                        <el-icon><Check /></el-icon> 完成
                    </el-button>
                    <el-button @click="checkPaymentStatus" type="primary" v-if="currentPaymentOrder && currentPaymentOrder.debug_status !== 'paid'">
                        <el-icon><Refresh /></el-icon> 手动查询
                    </el-button>
                    <el-button @click="cancelPayment">
                        <el-icon><Close /></el-icon> 取消
                    </el-button>
                </div>
            </template>
        </el-dialog>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
{% endblock %}

{% block data %}
    debugList: [],
    configList: [],
    showCreateDialog: false,
    showConfigDialog: false,
    showCreateConfigDialog: false,
    showEditConfigDialog: false,
    showPaymentDialog: false,
    creating: false,
    pollingTimer: null,
    pollingInterval: 3000, // 3秒轮询一次
    creatingConfig: false,
    updatingConfig: false,
    currentEditConfigId: null,
    currentPaymentOrder: null,
    createForm: {
        debug_name: '',
        payment_mode: 'face_to_face',
        user_id: '',
        product_id: 1,
        amount: 0.01,
        subject: '',
        body: '',
        timeout_minutes: 30
    },
    configForm: {
        config_name: '',
        config_type: 'alipay',
        app_id: '',
        app_private_key: '',
        alipay_public_key: '',
        gateway_url: 'https://openapi-sandbox.dl.alipaydev.com/gateway.do',
        notify_url: '',
        return_url: '',
        environment: 'sandbox',
        description: ''
    },
    editConfigForm: {
        config_name: '',
        config_type: 'alipay',
        app_id: '',
        app_private_key: '',
        alipay_public_key: '',
        gateway_url: '',
        notify_url: '',
        return_url: '',
        environment: 'sandbox',
        description: ''
    }
{% endblock %}

{% block mounted %}
    this.loadDebugList();
    this.loadConfigList();
{% endblock %}

{% block methods %}
    async loadDebugList() {
        try {
            const response = await fetch('/api/payment-debug/debug/list');
            const data = await response.json();
            if (data.success) {
                this.debugList = data.data.records;
                this.$nextTick(() => {
                    this.generateQRCodes();
                });
            }
        } catch (error) {
            ElMessage.error('加载调试记录失败');
        }
    },

    async loadConfigList() {
        try {
            const response = await fetch('/api/payment-debug/config/list');
            const data = await response.json();
            if (data.success) {
                this.configList = data.data;
            }
        } catch (error) {
            ElMessage.error('加载配置列表失败');
        }
    },

    async createDebugPayment() {
        this.creating = true;
        try {
            const response = await fetch('/api/payment-debug/debug/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.createForm)
            });
            const data = await response.json();

            if (data.success) {
                ElMessage.success('调试支付创建成功');
                this.showCreateDialog = false;

                // 设置当前支付订单并显示支付对话框
                this.currentPaymentOrder = data.data;
                this.showPaymentDialog = true;

                // 生成二维码
                this.$nextTick(() => {
                    this.generatePaymentQRCode();
                });

                // 启动自动轮询
                this.startPolling();
            } else {
                ElMessage.error(data.error || '创建失败');
            }
        } catch (error) {
            ElMessage.error('创建调试支付失败');
        } finally {
            this.creating = false;
        }
    },

    async queryPayment(debug) {
        try {
            const response = await fetch(`/api/payment-debug/debug/${debug.id}/query`);
            const data = await response.json();

            if (data.success) {
                ElMessage.success('查询成功');
                this.loadDebugList();
            } else {
                ElMessage.error(data.error || '查询失败');
            }
        } catch (error) {
            ElMessage.error('查询支付状态失败');
        }
    },

    async deleteDebug(id) {
        try {
            await ElMessageBox.confirm('确定要删除这条调试记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });

            const response = await fetch(`/api/payment-debug/debug/${id}`, {
                method: 'DELETE'
            });
            const data = await response.json();

            if (data.success) {
                ElMessage.success('删除成功');
                this.loadDebugList();
            } else {
                ElMessage.error('删除失败');
            }
        } catch (error) {
            // 用户取消删除
        }
    },

    generateQRCodes() {
        if (typeof QRCode === 'undefined') {
            console.warn('QRCode库未加载，稍后重试...');
            setTimeout(() => this.generateQRCodes(), 100);
            return;
        }

        this.debugList.forEach(debug => {
            // 只有在没有base64图片时才使用QRCode.js生成
            if (debug.qr_code && !debug.qr_image) {
                const element = document.getElementById(`qr-${debug.id}`);
                if (element) {
                    element.innerHTML = '';
                    QRCode.toCanvas(element, debug.qr_code, { width: 150 }, function (error) {
                        if (error) console.error(error);
                    });
                }
            }
        });
    },

    generatePaymentQRCode() {
        // 如果有base64图片，直接使用，不需要生成
        if (this.currentPaymentOrder && this.currentPaymentOrder.qr_image) {
            console.log('使用后端返回的base64二维码图片');
            return;
        }

        // 只有在没有base64图片时才使用QRCode.js生成
        if (typeof QRCode === 'undefined') {
            console.warn('QRCode库未加载，稍后重试...');
            setTimeout(() => this.generatePaymentQRCode(), 100);
            return;
        }

        if (this.currentPaymentOrder && this.currentPaymentOrder.qr_code) {
            const element = document.getElementById('payment-qr-code');
            if (element) {
                element.innerHTML = '';
                QRCode.toCanvas(element, this.currentPaymentOrder.qr_code, {
                    width: 200,
                    height: 200,
                    margin: 2
                }, function (error) {
                    if (error) console.error('二维码生成失败:', error);
                });
            }
        }
    },

    getStatusText(status) {
        const statusMap = {
            'created': '已创建',
            'payment_created': '支付已创建',
            'paid': '已支付',
            'failed': '失败',
            'timeout': '超时'
        };
        return statusMap[status] || status;
    },

    async createConfig() {
        this.creatingConfig = true;
        try {
            const response = await fetch('/api/payment-debug/config/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.configForm)
            });
            const data = await response.json();

            if (data.success) {
                ElMessage.success('配置创建成功');
                this.showCreateConfigDialog = false;
                this.loadConfigList();
                this.resetConfigForm();
            } else {
                ElMessage.error(data.error || '创建失败');
            }
        } catch (error) {
            ElMessage.error('创建配置失败');
        } finally {
            this.creatingConfig = false;
        }
    },

    editConfig(config) {
        this.currentEditConfigId = config.id;
        this.editConfigForm = { ...config };
        this.showEditConfigDialog = true;
    },

    async updateConfig() {
        this.updatingConfig = true;
        try {
            const response = await fetch(`/api/payment-debug/config/${this.currentEditConfigId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.editConfigForm)
            });
            const data = await response.json();

            if (data.success) {
                ElMessage.success('配置更新成功');
                this.showEditConfigDialog = false;
                this.loadConfigList();
            } else {
                ElMessage.error(data.error || '更新失败');
            }
        } catch (error) {
            ElMessage.error('更新配置失败');
        } finally {
            this.updatingConfig = false;
        }
    },

    async activateConfig(id) {
        try {
            const response = await fetch(`/api/payment-debug/config/${id}/activate`, {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                ElMessage.success('配置已激活');
                this.loadConfigList();
            } else {
                ElMessage.error('激活失败');
            }
        } catch (error) {
            ElMessage.error('激活配置失败');
        }
    },

    async deleteConfig(id) {
        try {
            await ElMessageBox.confirm('确定要删除这个配置吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });

            const response = await fetch(`/api/payment-debug/config/${id}`, {
                method: 'DELETE'
            });
            const data = await response.json();

            if (data.success) {
                ElMessage.success('删除成功');
                this.loadConfigList();
            } else {
                ElMessage.error('删除失败');
            }
        } catch (error) {
            // 用户取消删除
        }
    },

    resetConfigForm() {
        this.configForm = {
            config_name: '',
            config_type: 'alipay',
            app_id: '',
            app_private_key: '',
            alipay_public_key: '',
            gateway_url: 'https://openapi-sandbox.dl.alipaydev.com/gateway.do',
            notify_url: '',
            return_url: '',
            environment: 'sandbox',
            description: ''
        };
    },

    async checkPaymentStatus() {
        if (!this.currentPaymentOrder) return;

        try {
            const response = await fetch(`/api/payment-debug/debug/${this.currentPaymentOrder.id}/query`);
            const data = await response.json();

            if (data.success) {
                // 保留二维码图片，只更新其他字段
                const qr_image = this.currentPaymentOrder.qr_image;
                const qr_code = this.currentPaymentOrder.qr_code;

                // 更新当前订单状态
                this.currentPaymentOrder = data.data;

                // 恢复二维码数据（查询接口不返回二维码）
                if (qr_image && !this.currentPaymentOrder.qr_image) {
                    this.currentPaymentOrder.qr_image = qr_image;
                }
                if (qr_code && !this.currentPaymentOrder.qr_code) {
                    this.currentPaymentOrder.qr_code = qr_code;
                }

                // 检查是否支付成功
                if (this.currentPaymentOrder.debug_status === 'paid') {
                    ElMessage.success('支付成功！');
                    this.completePayment();
                } else {
                    ElMessage.info('支付状态已更新');
                }
            } else {
                ElMessage.error(data.error || '查询失败');
            }
        } catch (error) {
            ElMessage.error('查询支付状态失败');
        }
    },

    completePayment() {
        // 停止轮询
        this.stopPolling();

        // 关闭支付对话框
        this.showPaymentDialog = false;
        this.currentPaymentOrder = null;

        // 刷新调试记录列表
        this.loadDebugList();

        ElMessage.success('支付流程完成，调试记录已更新');
    },

    cancelPayment() {
        // 停止轮询
        this.stopPolling();

        this.showPaymentDialog = false;
        this.currentPaymentOrder = null;

        // 刷新调试记录列表以显示新创建的记录
        this.loadDebugList();
    },

    startPolling() {
        // 清除之前的轮询
        this.stopPolling();

        console.log('开始自动轮询支付状态...');
        this.pollingTimer = setInterval(() => {
            this.autoCheckPaymentStatus();
        }, this.pollingInterval);
    },

    stopPolling() {
        if (this.pollingTimer) {
            console.log('停止自动轮询');
            clearInterval(this.pollingTimer);
            this.pollingTimer = null;
        }
    },

    async autoCheckPaymentStatus() {
        if (!this.currentPaymentOrder) {
            this.stopPolling();
            return;
        }

        try {
            const response = await fetch(`/api/payment-debug/debug/${this.currentPaymentOrder.id}/query`);
            const data = await response.json();

            if (data.success) {
                const oldStatus = this.currentPaymentOrder.debug_status;

                // 保留二维码图片，只更新其他字段
                const qr_image = this.currentPaymentOrder.qr_image;
                const qr_code = this.currentPaymentOrder.qr_code;

                this.currentPaymentOrder = data.data;

                // 恢复二维码数据（查询接口不返回二维码）
                if (qr_image && !this.currentPaymentOrder.qr_image) {
                    this.currentPaymentOrder.qr_image = qr_image;
                }
                if (qr_code && !this.currentPaymentOrder.qr_code) {
                    this.currentPaymentOrder.qr_code = qr_code;
                }

                // 检查状态是否发生变化
                if (oldStatus !== this.currentPaymentOrder.debug_status) {
                    console.log(`支付状态变化: ${oldStatus} → ${this.currentPaymentOrder.debug_status}`);

                    // 如果支付成功，显示成功消息并停止轮询
                    if (this.currentPaymentOrder.debug_status === 'paid') {
                        ElMessage.success('🎉 支付成功！');
                        this.stopPolling();

                        // 3秒后自动完成
                        setTimeout(() => {
                            if (this.showPaymentDialog) {
                                this.completePayment();
                            }
                        }, 3000);
                    }
                    // 如果支付失败，显示失败消息并停止轮询
                    else if (this.currentPaymentOrder.debug_status === 'failed') {
                        ElMessage.error('❌ 支付失败');
                        this.stopPolling();
                    }
                }
            } else {
                console.warn('轮询查询失败:', data.error);
            }
        } catch (error) {
            console.warn('轮询查询异常:', error);
        }
    }
{% endblock %}
