{% extends "agent/base.html" %}

{% block title %}订单管理{% endblock %}
{% block page_title %}订单管理{% endblock %}

{% block content %}
<div>
    <div class="content-card">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">订单管理</h1>
            <div class="header-actions">
                <el-button type="primary" @click="openCreateOrderDialog">
                    <el-icon><Plus /></el-icon>
                    创建订单
                </el-button>
                <el-button @click="loadOrders" :loading="loading">
                    <el-icon><Refresh /></el-icon>
                    刷新数据
                </el-button>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-section">
            <el-row :gutter="16" class="search-bar">
                <el-col :span="6">
                    <el-input
                        v-model="orderSearchKeyword"
                        placeholder="搜索订单号或客户信息"
                        @keyup.enter="searchOrders"
                        clearable>
                        <template #append>
                            <el-button @click="searchOrders">
                                <el-icon><Search /></el-icon>
                            </el-button>
                        </template>
                    </el-input>
                </el-col>
                <el-col :span="4">
                    <el-select v-model="orderStatusFilter" placeholder="订单状态" clearable @change="searchOrders" style="width: 100%">
                        <el-option label="待处理" value="pending" />
                        <el-option label="处理中" value="processing" />
                        <el-option label="已完成" value="completed" />
                        <el-option label="已取消" value="cancelled" />
                        <el-option label="已退款" value="refunded" />
                    </el-select>
                </el-col>
                <el-col :span="6">
                    <el-date-picker
                        v-model="orderDateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="searchOrders"
                        value-format="YYYY-MM-DD"
                        style="width: 100%"
                    />
                </el-col>
                <el-col :span="4">
                    <el-select v-model="orderProductFilter" placeholder="选择产品" clearable @change="searchOrders" style="width: 100%">
                        <el-option
                            v-for="product in availableProducts"
                            :key="product.id"
                            :label="product.name"
                            :value="product.id">
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <el-button @click="searchOrders" type="primary" plain>
                        <el-icon><Search /></el-icon>
                        搜索
                    </el-button>
                </el-col>
            </el-row>
        </div>
        
        <!-- 订单列表 -->
        <el-table :data="orders" v-loading="loading" stripe>
            <el-table-column prop="order_number" label="订单号" width="180" sortable>
                <template #default="scope">
                    <el-text class="order-number">
                        {% raw %}{{ scope.row.order_number }}{% endraw %}
                    </el-text>
                </template>
            </el-table-column>
            
            <el-table-column prop="product_name" label="产品" width="150">
                <template #default="scope">
                    {% raw %}{{ scope.row.product_name || '-' }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column prop="product_code" label="产品代码" width="120">
                <template #default="scope">
                    {% raw %}{{ scope.row.product_code || '-' }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column prop="customer_info" label="客户信息" width="200" show-overflow-tooltip>
                <template #default="scope">
                    {% raw %}{{ scope.row.customer_info || '-' }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column prop="quantity" label="数量" width="80" sortable>
                <template #default="scope">
                    {% raw %}{{ scope.row.quantity }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column prop="unit_price" label="单价" width="100" sortable>
                <template #default="scope">
                    ¥{% raw %}{{ (scope.row.unit_price || 0).toFixed(2) }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column prop="total_price" label="总金额" width="100" sortable>
                <template #default="scope">
                    ¥{% raw %}{{ (scope.row.total_price || 0).toFixed(2) }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                    <el-tag :type="getOrderStatusType(scope.row.status)">
                        {% raw %}{{ getOrderStatusText(scope.row.status) }}{% endraw %}
                    </el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="expire_date" label="过期时间" width="160" sortable>
                <template #default="scope">
                    {% raw %}{{ formatDate(scope.row.expire_date) || '-' }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column prop="notes" label="备注" width="150" show-overflow-tooltip>
                <template #default="scope">
                    {% raw %}{{ scope.row.notes || '-' }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column prop="created_at" label="创建时间" width="160" sortable>
                <template #default="scope">
                    {% raw %}{{ formatDate(scope.row.created_at) }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column prop="updated_at" label="更新时间" width="160" sortable>
                <template #default="scope">
                    {% raw %}{{ formatDate(scope.row.updated_at) }}{% endraw %}
                </template>
            </el-table-column>

            <el-table-column label="操作" width="280" fixed="right">
                <template #default="scope">
                    <div class="table-actions">
                        <el-button type="primary" size="small" @click="editOrder(scope.row)">
                            <el-icon><Edit /></el-icon>
                            编辑
                        </el-button>
                        <el-dropdown trigger="click" @command="handleStatusChange($event, scope.row)">
                            <el-button type="success" size="small">
                                <el-icon><More /></el-icon>
                                状态操作
                                <el-icon class="el-icon--right"><arrow-down /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="pending" v-if="scope.row.status !== 'pending'">标记为待处理</el-dropdown-item>
                                    <el-dropdown-item command="processing" v-if="scope.row.status !== 'processing'">标记为处理中</el-dropdown-item>
                                    <el-dropdown-item command="completed" v-if="scope.row.status !== 'completed'">标记为已完成</el-dropdown-item>
                                    <el-dropdown-item command="cancelled" v-if="scope.row.status !== 'cancelled'">标记为已取消</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                        <el-button
                            size="small"
                            type="danger"
                            @click="deleteOrder(scope.row)"
                            v-if="scope.row.status === 'pending'">
                            <el-icon><Delete /></el-icon>
                            删除
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
            <el-pagination
                v-model:current-page="orderCurrentPage"
                v-model:page-size="orderPageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="orderTotal"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleOrderSizeChange"
                @current-change="handleOrderCurrentChange"
                background>
            </el-pagination>
        </div>
    </div>

    <!-- 创建/编辑订单对话框 -->
    <el-dialog
        :title="orderEditMode ? '编辑订单' : '创建订单'"
        v-model="orderDialogVisible"
        width="550px">
        <el-form ref="orderFormRef" :model="orderForm" :rules="orderRules" label-width="100px">
            <el-form-item label="订单号" prop="order_number">
                <el-input v-model="orderForm.order_number" placeholder="留空自动生成" :disabled="orderEditMode"></el-input>
            </el-form-item>

            <el-form-item label="产品" prop="product_id">
                <el-select v-model="orderForm.product_id" placeholder="请选择产品" style="width: 100%">
                    <el-option
                        v-for="product in availableProducts"
                        :key="product.id"
                        :label="product.name"
                        :value="product.id">
                        <span>{% raw %}{{ product.name }}{% endraw %}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">
                            {% raw %}{{ product.code }}{% endraw %}
                        </span>
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="数量" prop="quantity">
                <el-input-number v-model="orderForm.quantity" :min="1" :max="1000" style="width: 100%"></el-input-number>
            </el-form-item>

            <el-form-item label="单价" prop="unit_price">
                <el-input-number v-model="orderForm.unit_price" :precision="2" :min="0" style="width: 100%"></el-input-number>
            </el-form-item>

            <el-form-item label="总价" prop="total_price">
                <el-input-number v-model="orderForm.total_price" :precision="2" :min="0" style="width: 100%"></el-input-number>
            </el-form-item>

            <el-form-item label="过期时间">
                <el-date-picker
                    v-model="orderForm.expire_date"
                    type="datetime"
                    placeholder="选择过期时间"
                    style="width: 100%">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="客户信息">
                <el-input
                    v-model="orderForm.customer_info"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入客户信息">
                </el-input>
            </el-form-item>

            <el-form-item label="备注">
                <el-input
                    v-model="orderForm.notes"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入备注信息">
                </el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="orderDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveOrder">确定</el-button>
            </span>
        </template>
    </el-dialog>
</div>
{% endblock %}

{% block styles %}
<style>
/* 页面布局样式 */
.page-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.search-bar {
    margin-bottom: 0;
}

/* 表格操作按钮样式 */
.table-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.table-actions .el-button {
    height: 32px;
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 4px;
    font-weight: 500;
}

/* 分页样式 */
.pagination-wrapper {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e6e6e6;
    display: flex;
    justify-content: center;
}

/* 订单号样式 */
.order-number {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    color: #409EFF;
    font-weight: 600;
    padding: 2px 6px;
    background: #f0f9ff;
    border-radius: 4px;
    border: 1px solid #e1f5fe;
}

/* 确保按钮样式正确 */
.el-button {
    height: 32px;
    padding: 8px 15px;
    font-size: 14px;
    border-radius: 4px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.el-button--primary {
    background-color: #409eff;
    border-color: #409eff;
    color: #ffffff;
}

.el-button--success {
    background-color: #67c23a;
    border-color: #67c23a;
    color: #ffffff;
}

.el-button--danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #ffffff;
}

.el-button--small {
    height: 28px;
    padding: 6px 12px;
    font-size: 13px;
}

.el-button .el-icon {
    font-size: 16px;
}

.el-button--small .el-icon {
    font-size: 14px;
}

/* 表格样式优化 */
.el-table {
    border-radius: 8px;
    overflow: hidden;
}

.el-table th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 600;
}

.el-table td {
    padding: 12px 0;
}

/* 对话框样式 */
.el-dialog {
    border-radius: 8px;
}

.el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #e6e6e6;
}

.el-dialog__body {
    padding: 20px;
}

.el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #e6e6e6;
}
</style>
{% endblock %}