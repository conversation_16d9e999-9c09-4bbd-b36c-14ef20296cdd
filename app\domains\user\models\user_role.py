from sqlalchemy import Column, Integer, String, Foreign<PERSON>ey, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class UserRole(Base):
    __tablename__ = "user_roles"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(100), nullable=False, comment="用户ID")
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=False, comment="角色ID")
    assigned_by = Column(String(100), comment="分配者")
    is_active = Column(Boolean, default=True, comment="是否激活")
    assigned_at = Column(DateTime(timezone=True), server_default=func.now(), comment="分配时间")
    expires_at = Column(DateTime(timezone=True), comment="过期时间")
    
    # 关联关系
    role = relationship("Role", backref="user_roles")
    user = relationship("User", foreign_keys=[user_id], primaryjoin="UserRole.user_id == User.user_id")
    
    def __repr__(self):
        return f"<UserRole(user_id='{self.user_id}', role_id={self.role_id}, is_active={self.is_active})>"
