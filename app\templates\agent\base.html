<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}代理商管理系统{% endblock %} - FocuSee</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
        }

        .layout-container {
            height: 100vh;
        }

        .header {
            background: #fff;
            border-bottom: 1px solid #e6e6e6;
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .logo {
            font-size: 20px;
            font-weight: 600;
            color: #409EFF;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .sidebar {
            background: #304156;
            overflow: hidden;
            box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
        }

        .main-content {
            background: #f0f2f5;
            padding: 24px;
            overflow-y: auto;
        }

        .content-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e6e6e6;
        }

        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e6e6e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        /* 确保按钮样式正确 */
        .el-button {
            height: 32px;
            padding: 8px 15px;
            font-size: 14px;
            border-radius: 4px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: 1px solid;
            cursor: pointer;
            transition: all 0.3s;
        }

        .el-button--primary {
            background-color: #409eff;
            border-color: #409eff;
            color: #ffffff;
        }

        .el-button--primary:hover {
            background-color: #66b1ff;
            border-color: #66b1ff;
        }

        .el-button--success {
            background-color: #67c23a;
            border-color: #67c23a;
            color: #ffffff;
        }

        .el-button--success:hover {
            background-color: #85ce61;
            border-color: #85ce61;
        }

        .el-button--info {
            background-color: #909399;
            border-color: #909399;
            color: #ffffff;
        }

        .el-button--info:hover {
            background-color: #a6a9ad;
            border-color: #a6a9ad;
        }

        .el-button--danger {
            background-color: #f56c6c;
            border-color: #f56c6c;
            color: #ffffff;
        }

        .el-button--danger:hover {
            background-color: #f78989;
            border-color: #f78989;
        }

        .el-button--small {
            height: 28px;
            padding: 6px 12px;
            font-size: 13px;
        }

        .el-button .el-icon {
            font-size: 16px;
        }

        .el-button--small .el-icon {
            font-size: 14px;
        }

        /* 菜单样式优化 */
        .el-menu-item {
            height: 48px;
            line-height: 48px;
            padding: 0 20px !important;
        }

        .el-menu-item .el-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        /* 下拉菜单样式 */
        .el-dropdown-link {
            cursor: pointer;
            color: #606266;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
        }

        .el-dropdown-link:hover {
            color: #409eff;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container class="layout-container">
            <!-- 顶部导航 -->
            <el-header class="header" height="60px">
                <div class="logo">
                    <el-icon><Box /></el-icon>
                    FocuSee 代理商管理系统
                </div>
                <div>
                    <el-dropdown @command="handleCommand">
                        <span class="el-dropdown-link">
                            <el-icon><User /></el-icon>
                            {% raw %}{{ agentInfo.company_name }}{% endraw %}
                            <el-icon class="el-icon--right"><arrow-down /></el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </el-header>
            
            <el-container>
                <!-- 侧边栏 -->
                <el-aside class="sidebar" width="200px">
                    <el-menu
                        :default-active="currentRoute"
                        class="el-menu-vertical"
                        background-color="#304156"
                        text-color="#bfcbd9"
                        active-text-color="#409EFF">
                        
                        <el-menu-item index="/agent/dashboard" @click="navigate('/agent/dashboard')">
                            <el-icon><Odometer /></el-icon>
                            <span>仪表板</span>
                        </el-menu-item>
                        <el-menu-item index="/agent/products" @click="navigate('/agent/products')">
                            <el-icon><Box /></el-icon>
                            <span>授权产品</span>
                        </el-menu-item>
                        <el-menu-item index="/agent/licenses" @click="navigate('/agent/licenses')">
                            <el-icon><Key /></el-icon>
                            <span>授权码管理</span>
                        </el-menu-item>
                        <el-menu-item index="/agent/orders" @click="navigate('/agent/orders')">
                            <el-icon><Document /></el-icon>
                            <span>订单管理</span>
                        </el-menu-item>
                        <el-menu-item index="/agent/profile" @click="navigate('/agent/profile')">
                            <el-icon><Setting /></el-icon>
                            <span>个人设置</span>
                        </el-menu-item>
                    </el-menu>
                </el-aside>
                
                <!-- 主内容区 -->
                <el-main class="main-content">
                    <div class="content-card">
                        <div class="page-header">
                            <h1 class="page-title">{% block page_title %}仪表板{% endblock %}</h1>
                        </div>
                        {% block content %}{% endblock %}
                    </div>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                const currentRoute = ref(window.location.pathname);
                const agentInfo = ref({
                    company_name: '加载中...'
                });

                // 页面数据
                const stats = reactive({
                    authorizedProducts: 0,
                    totalLicenses: 0,
                    activeLicenses: 0,
                    totalOrders: 0
                });
                const authorizedProducts = ref([]);
                const recentActivities = ref([]);
                const loading = ref(false);

                // 个人设置页面数据
                const profileForm = reactive({
                    username: '',
                    company_name: '',
                    contact_name: '',
                    contact_email: '',
                    contact_phone: '',
                    address: '',
                    created_at: ''
                });
                const passwordForm = reactive({
                    old_password: '',
                    new_password: '',
                    confirm_password: ''
                });
                const changingPassword = ref(false);

                // 授权码管理页面数据
                const licenses = ref([]);
                const searchKeyword = ref('');
                const statusFilter = ref('');
                const productFilter = ref('');
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const dialogVisible = ref(false);
                const dialogTitle = ref('');
                const showCreateDialog = ref(false);
                const licenseForm = reactive({
                    product_id: '',
                    quantity: 1,
                    max_api_calls: -1,
                    expire_date: '',
                    notes: ''
                });
                const expireOption = ref('never');
                const availableProducts = ref([]);
                const detailDialogVisible = ref(false);
                const currentLicense = ref({});
                const extendDialogVisible = ref(false);
                const extendForm = reactive({
                    expire_date: ''
                });
                const editDialogVisible = ref(false);
                const editForm = reactive({
                    license_code: '',
                    product_id: '',
                    max_api_calls: -1,
                    expire_date: '',
                    notes: ''
                });
                const editExpireOption = ref('never');

                // 订单管理页面数据
                const orders = ref([]);
                const orderSearchKeyword = ref('');
                const orderStatusFilter = ref('');
                const orderDateRange = ref([]);
                const orderProductFilter = ref('');
                const orderCurrentPage = ref(1);
                const orderPageSize = ref(10);
                const orderTotal = ref(0);
                const orderDialogVisible = ref(false);
                const orderEditMode = ref(false);
                const currentOrder = ref({});
                const orderForm = reactive({
                    order_number: '',
                    product_id: '',
                    quantity: 1,
                    unit_price: 0,
                    total_price: 0,
                    expire_date: '',
                    customer_info: '',
                    notes: ''
                });
                const orderRules = {
                    quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
                    unit_price: [{ required: true, message: '请输入单价', trigger: 'blur' }],
                    total_price: [{ required: true, message: '请输入总价', trigger: 'blur' }]
                };

                // 检查登录状态
                const checkAuth = () => {
                    const token = localStorage.getItem('agent_token');
                    if (!token) {
                        window.location.href = '/agent/login';
                        return false;
                    }

                    const savedAgentInfo = localStorage.getItem('agent_info');
                    if (savedAgentInfo) {
                        agentInfo.value = JSON.parse(savedAgentInfo);
                    }

                    return true;
                };

                // 数据加载方法
                const loadStats = async () => {
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) return;

                        const response = await fetch('/api/agent/dashboard/stats', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            stats.authorizedProducts = data.authorized_products;
                            stats.totalLicenses = data.total_licenses;
                            stats.activeLicenses = data.active_licenses;
                            stats.totalOrders = data.total_orders;
                        }
                    } catch (error) {
                        console.error('Failed to load stats:', error);
                    }
                };

                const loadAuthorizedProducts = async () => {
                    loading.value = true;
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) return;

                        const response = await fetch('/api/agent/dashboard/authorized-products', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            authorizedProducts.value = data.map(item => ({
                                id: item.id,
                                name: item.product_name,
                                max_licenses: item.max_licenses,
                                used_licenses: item.used_licenses,
                                is_active: item.is_active && !item.is_expired
                            }));
                        }
                    } catch (error) {
                        console.error('Failed to load authorized products:', error);
                    } finally {
                        loading.value = false;
                    }
                };

                const loadRecentActivities = async () => {
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) return;

                        const response = await fetch('/api/agent/dashboard/recent-activities?limit=5', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        if (response.ok) {
                            recentActivities.value = await response.json();
                        }
                    } catch (error) {
                        console.error('Failed to load recent activities:', error);
                    }
                };

                // 工具方法
                const formatDate = (dateString) => {
                    if (!dateString) return '-';
                    return new Date(dateString).toLocaleString('zh-CN');
                };

                const getUsagePercentage = (used, max) => {
                    return max > 0 ? Math.round((used / max) * 100) : 0;
                };

                const getUsageColor = (percentage) => {
                    if (percentage < 0.5) return '#67c23a';
                    if (percentage < 0.8) return '#e6a23c';
                    return '#f56c6c';
                };

                // 个人设置方法
                const loadProfile = async () => {
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) return;

                        const response = await fetch('/api/agent-auth/me', {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            profileForm.username = data.username;
                            profileForm.company_name = data.company_name;
                            profileForm.contact_name = data.contact_name || '';
                            profileForm.contact_email = data.contact_email || '';
                            profileForm.contact_phone = data.contact_phone || '';
                            profileForm.address = data.address || '';
                            profileForm.created_at = data.created_at;
                        }
                    } catch (error) {
                        console.error('Failed to load profile:', error);
                    }
                };

                const changePassword = async () => {
                    // 简单验证
                    if (!passwordForm.old_password || !passwordForm.new_password) {
                        ElMessage.error('请填写完整的密码信息');
                        return;
                    }

                    if (passwordForm.new_password !== passwordForm.confirm_password) {
                        ElMessage.error('两次输入的密码不一致');
                        return;
                    }

                    if (passwordForm.new_password.length < 6) {
                        ElMessage.error('密码长度不能小于6个字符');
                        return;
                    }

                    changingPassword.value = true;
                    try {
                        const token = localStorage.getItem('agent_token');
                        const agentInfo = JSON.parse(localStorage.getItem('agent_info') || '{}');

                        const response = await fetch(`/api/agents/${agentInfo.id}/password`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify({
                                old_password: passwordForm.old_password,
                                new_password: passwordForm.new_password
                            })
                        });

                        if (response.ok) {
                            ElMessage.success('密码修改成功');
                            passwordForm.old_password = '';
                            passwordForm.new_password = '';
                            passwordForm.confirm_password = '';
                        } else {
                            const error = await response.json();
                            ElMessage.error(error.detail || '密码修改失败');
                        }
                    } catch (error) {
                        console.error('Failed to change password:', error);
                        ElMessage.error('网络错误');
                    } finally {
                        changingPassword.value = false;
                    }
                };

                const resetPasswordForm = () => {
                    passwordForm.old_password = '';
                    passwordForm.new_password = '';
                    passwordForm.confirm_password = '';
                };

                // 授权码管理方法
                const loadLicenses = async () => {
                    loading.value = true;
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) {
                            ElMessage.error('请先登录');
                            return;
                        }

                        const skip = (currentPage.value - 1) * pageSize.value;
                        const params = new URLSearchParams({
                            skip: skip,
                            limit: pageSize.value
                        });

                        if (searchKeyword.value) {
                            params.append('search', searchKeyword.value);
                        }
                        
                        if (statusFilter.value) {
                            params.append('status_filter', statusFilter.value);
                        }
                        
                        if (productFilter.value) {
                            params.append('product_id', productFilter.value);
                        }

                        const response = await fetch(`/api/agent/licenses?${params}`, {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            licenses.value = data.items || [];
                            total.value = data.total || 0;
                        } else if (response.status === 401) {
                            ElMessage.error('登录已过期，请重新登录');
                            localStorage.removeItem('agent_token');
                            localStorage.removeItem('agent_info');
                            window.location.href = '/agent/login';
                        } else {
                            const errorData = await response.json().catch(() => ({ detail: '未知错误' }));
                            ElMessage.error(`加载授权码失败: ${errorData.detail || '服务器错误'}`);
                        }
                    } catch (error) {
                        console.error('Failed to load licenses:', error);
                        ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };

                const searchLicenses = () => {
                    currentPage.value = 1;
                    loadLicenses();
                };

                const handleSizeChange = (size) => {
                    pageSize.value = size;
                    currentPage.value = 1;
                    loadLicenses();
                };

                const handleCurrentChange = (page) => {
                    currentPage.value = page;
                    loadLicenses();
                };

                const openCreateDialog = () => {
                    dialogTitle.value = '生成授权码';
                    dialogVisible.value = true;
                    showCreateDialog.value = true;
                    loadAvailableProducts();
                };

                const loadAvailableProducts = async () => {
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) {
                            ElMessage.error('请先登录');
                            return;
                        }

                        const response = await fetch('/api/agent/dashboard/authorized-products', {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            availableProducts.value = data.map(item => ({
                                id: item.product_id,
                                name: item.product_name,
                                remaining_licenses: item.max_licenses - item.used_licenses
                            }));
                        } else if (response.status === 401) {
                            ElMessage.error('登录已过期，请重新登录');
                            localStorage.removeItem('agent_token');
                            localStorage.removeItem('agent_info');
                            window.location.href = '/agent/login';
                        } else {
                            const errorData = await response.json().catch(() => ({ detail: '未知错误' }));
                            ElMessage.error(`加载产品失败: ${errorData.detail || '服务器错误'}`);
                        }
                    } catch (error) {
                        console.error('Failed to load available products:', error);
                        ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
                    }
                };

                const createLicense = async () => {
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) {
                            ElMessage.error('请先登录');
                            return;
                        }

                        // 验证表单数据
                        if (!licenseForm.product_id) {
                            ElMessage.error('请选择产品');
                            return;
                        }

                        const response = await fetch('/api/agent/licenses/generate', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(licenseForm)
                        });

                        if (response.ok) {
                            const data = await response.json();
                            ElMessage.success(`成功生成 ${data.length} 个授权码`);
                            dialogVisible.value = false;
                            loadLicenses();
                            resetLicenseForm();
                        } else if (response.status === 401) {
                            ElMessage.error('登录已过期，请重新登录');
                            localStorage.removeItem('agent_token');
                            localStorage.removeItem('agent_info');
                            window.location.href = '/agent/login';
                        } else {
                            const error = await response.json().catch(() => ({ detail: '未知错误' }));
                            console.error('License generation error:', error);

                            // 显示详细的错误信息
                            let errorMessage = '生成授权码失败：';
                            if (error.detail) {
                                if (typeof error.detail === 'string') {
                                    errorMessage += error.detail;
                                } else if (Array.isArray(error.detail)) {
                                    errorMessage += error.detail.map(e => e.msg || e.message || e).join(', ');
                                } else {
                                    errorMessage += JSON.stringify(error.detail);
                                }
                            } else if (error.message) {
                                errorMessage += error.message;
                            } else {
                                errorMessage += '未知错误';
                            }

                            ElMessage.error(errorMessage);
                        }
                    } catch (error) {
                        console.error('Failed to create license:', error);
                        ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
                    }
                };

                const resetLicenseForm = () => {
                    licenseForm.product_id = '';
                    licenseForm.quantity = 1;
                    licenseForm.max_api_calls = -1;
                    licenseForm.expire_date = '';
                    licenseForm.notes = '';
                    expireOption.value = 'never';
                };

                const handleExpireOptionChange = (value) => {
                    if (value === 'never') {
                        licenseForm.expire_date = '';
                    }
                };

                const getStatusText = (status) => {
                    const statusMap = {
                        'inactive': '未激活',
                        'active': '已激活',
                        'expired': '已过期',
                        'suspended': '已暂停'
                    };
                    return statusMap[status] || status;
                };

                const getStatusType = (status) => {
                    const typeMap = {
                        'inactive': 'info',
                        'active': 'success',
                        'expired': 'danger',
                        'suspended': 'warning'
                    };
                    return typeMap[status] || 'info';
                };

                const copyToClipboard = async (text) => {
                    try {
                        await navigator.clipboard.writeText(text);
                        ElMessage.success('已复制到剪贴板');
                    } catch (error) {
                        ElMessage.error('复制失败');
                    }
                };

                // 编辑授权码
                const editLicense = (license) => {
                    currentLicense.value = license;
                    editForm.license_code = license.license_code;
                    editForm.product_id = license.product_id;
                    editForm.max_api_calls = license.max_api_calls;
                    editForm.expire_date = license.expire_date || '';
                    editForm.notes = license.notes || '';

                    // 设置过期时间选项
                    editExpireOption.value = license.expire_date ? 'custom' : 'never';

                    editDialogVisible.value = true;
                };

                const handleEditExpireOptionChange = (value) => {
                    if (value === 'never') {
                        editForm.expire_date = '';
                    }
                };

                const updateLicense = async () => {
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) {
                            ElMessage.error('请先登录');
                            return;
                        }

                        loading.value = true;

                        const response = await fetch(`/api/agent/licenses/${currentLicense.value.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify({
                                max_api_calls: editForm.max_api_calls,
                                expire_date: editForm.expire_date || null,
                                notes: editForm.notes
                            })
                        });

                        if (response.ok) {
                            ElMessage.success('授权码更新成功');
                            editDialogVisible.value = false;
                            loadLicenses();
                        } else if (response.status === 401) {
                            ElMessage.error('登录已过期，请重新登录');
                            localStorage.removeItem('agent_token');
                            localStorage.removeItem('agent_info');
                            window.location.href = '/agent/login';
                        } else {
                            const error = await response.json().catch(() => ({ detail: '未知错误' }));
                            console.error('License update error:', error);

                            let errorMessage = '更新授权码失败：';
                            if (error.detail) {
                                if (typeof error.detail === 'string') {
                                    errorMessage += error.detail;
                                } else if (Array.isArray(error.detail)) {
                                    errorMessage += error.detail.map(e => e.msg || e.message || e).join(', ');
                                } else {
                                    errorMessage += JSON.stringify(error.detail);
                                }
                            } else if (error.message) {
                                errorMessage += error.message;
                            } else {
                                errorMessage += '未知错误';
                            }

                            ElMessage.error(errorMessage);
                        }
                    } catch (error) {
                        console.error('Failed to update license:', error);
                        ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };

                // 处理授权码操作
                const handleLicenseAction = async (command, license) => {
                    currentLicense.value = license;

                    switch (command) {
                        case 'copy':
                            await copyToClipboard(license.license_code);
                            break;
                        case 'suspend':
                            await suspendLicense(license);
                            break;
                        case 'activate':
                            await activateLicense(license);
                            break;
                        case 'extend':
                            openExtendDialog(license);
                            break;
                        case 'revoke':
                            await revokeLicense(license);
                            break;
                    }
                };

                const suspendLicense = async (license) => {
                    try {
                        await ElMessageBox.confirm(`确定要暂停授权码 ${license.license_code} 吗？`, '确认暂停', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        const token = localStorage.getItem('agent_token');
                        const response = await fetch(`/api/agent/licenses/${license.id}/suspend`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            ElMessage.success('授权码已暂停');
                            loadLicenses();
                        } else {
                            const error = await response.json().catch(() => ({ detail: '未知错误' }));
                            ElMessage.error(`暂停失败：${error.detail || '未知错误'}`);
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('Failed to suspend license:', error);
                            ElMessage.error('操作失败');
                        }
                    }
                };

                const activateLicense = async (license) => {
                    try {
                        await ElMessageBox.confirm(`确定要恢复授权码 ${license.license_code} 吗？`, '确认恢复', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        const token = localStorage.getItem('agent_token');
                        const response = await fetch(`/api/agent/licenses/${license.id}/activate`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            ElMessage.success('授权码已恢复');
                            loadLicenses();
                        } else {
                            const error = await response.json().catch(() => ({ detail: '未知错误' }));
                            ElMessage.error(`恢复失败：${error.detail || '未知错误'}`);
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('Failed to activate license:', error);
                            ElMessage.error('操作失败');
                        }
                    }
                };

                const openExtendDialog = (license) => {
                    currentLicense.value = license;
                    extendForm.expire_date = '';
                    extendDialogVisible.value = true;
                };

                const confirmExtendLicense = async () => {
                    try {
                        if (!extendForm.expire_date) {
                            ElMessage.error('请选择新的过期时间');
                            return;
                        }

                        const token = localStorage.getItem('agent_token');
                        const response = await fetch(`/api/agent/licenses/${currentLicense.value.id}/extend`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                expire_date: extendForm.expire_date
                            })
                        });

                        if (response.ok) {
                            ElMessage.success('授权码有效期已延长');
                            extendDialogVisible.value = false;
                            loadLicenses();
                        } else {
                            const error = await response.json().catch(() => ({ detail: '未知错误' }));
                            ElMessage.error(`延长失败：${error.detail || '未知错误'}`);
                        }
                    } catch (error) {
                        console.error('Failed to extend license:', error);
                        ElMessage.error('操作失败');
                    }
                };

                const revokeLicense = async (license) => {
                    try {
                        await ElMessageBox.confirm(`确定要撤销授权码 ${license.license_code} 吗？此操作不可恢复！`, '确认撤销', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        const token = localStorage.getItem('agent_token');
                        const response = await fetch(`/api/agent/licenses/${license.id}`, {
                            method: 'DELETE',
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });

                        if (response.ok) {
                            ElMessage.success('授权码已撤销');
                            loadLicenses();
                        } else {
                            const error = await response.json().catch(() => ({ detail: '未知错误' }));
                            ElMessage.error(`撤销失败：${error.detail || '未知错误'}`);
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('Failed to revoke license:', error);
                            ElMessage.error('操作失败');
                        }
                    }
                };

                // 订单管理方法
                const loadOrders = async () => {
                    try {
                        loading.value = true;
                        const token = localStorage.getItem('agent_token');
                        if (!token) return;

                        const params = new URLSearchParams({
                            page: orderCurrentPage.value,
                            size: orderPageSize.value
                        });

                        if (orderSearchKeyword.value) {
                            params.append('search', orderSearchKeyword.value);
                        }
                        
                        if (orderStatusFilter.value) {
                            params.append('status', orderStatusFilter.value);
                        }
                        
                        if (orderProductFilter.value) {
                            params.append('product_id', orderProductFilter.value);
                        }
                        
                        if (orderDateRange.value && orderDateRange.value.length === 2) {
                            params.append('start_date', orderDateRange.value[0]);
                            params.append('end_date', orderDateRange.value[1]);
                        }

                        const response = await fetch(`/api/agent/orders?${params}`, {
                            headers: { 'Authorization': `Bearer ${token}` }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            orders.value = data.items || [];
                            orderTotal.value = data.total || 0;
                        }
                    } catch (error) {
                        console.error('Failed to load orders:', error);
                        ElMessage.error('加载订单列表失败');
                    } finally {
                        loading.value = false;
                    }
                };

                const searchOrders = () => {
                    orderCurrentPage.value = 1;
                    loadOrders();
                };

                const handleOrderSizeChange = (size) => {
                    orderPageSize.value = size;
                    orderCurrentPage.value = 1;
                    loadOrders();
                };

                const handleOrderCurrentChange = (page) => {
                    orderCurrentPage.value = page;
                    loadOrders();
                };

                const getOrderStatusText = (status) => {
                    const statusMap = {
                        'pending': '待处理',
                        'processing': '处理中',
                        'completed': '已完成',
                        'cancelled': '已取消',
                        'refunded': '已退款'
                    };
                    return statusMap[status] || status;
                };

                const getOrderStatusType = (status) => {
                    const typeMap = {
                        'pending': 'warning',
                        'processing': 'primary',
                        'completed': 'success',
                        'cancelled': 'danger',
                        'refunded': 'info'
                    };
                    return typeMap[status] || 'info';
                };

                // 处理订单状态变更
                const handleStatusChange = async (status, order) => {
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) return;

                        ElMessageBox.confirm(
                            `确定要将订单 ${order.order_number} 的状态修改为"${getOrderStatusText(status)}"吗？`,
                            '确认修改状态',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        ).then(async () => {
                            const response = await fetch(`/api/agent/orders/${order.id}/status`, {
                                method: 'PUT',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Authorization': `Bearer ${token}`
                                },
                                body: JSON.stringify({ status: status })
                            });

                            if (response.ok) {
                                ElMessage.success(`订单状态已更新为"${getOrderStatusText(status)}"`);
                                loadOrders();
                            } else {
                                const error = await response.json();
                                let errorMessage = '状态更新失败';
                                if (error.detail) {
                                    if (typeof error.detail === 'string') {
                                        errorMessage += '：' + error.detail;
                                    } else if (Array.isArray(error.detail)) {
                                        errorMessage += '：' + error.detail.map(err => err.msg).join('; ');
                                    } else if (typeof error.detail === 'object') {
                                        const details = [];
                                        for (const key in error.detail) {
                                            details.push(`${key}: ${error.detail[key]}`);
                                        }
                                        errorMessage += '：' + details.join('; ');
                                    }
                                }
                                ElMessage.error(errorMessage);
                            }
                        }).catch(() => {
                            // 用户取消操作
                        });
                    } catch (error) {
                        console.error('Error updating order status:', error);
                        ElMessage.error('状态更新失败：' + error.message);
                    }
                };

                // 订单CRUD操作方法
                const openCreateOrderDialog = () => {
                    orderEditMode.value = false;
                    resetOrderForm();
                    orderDialogVisible.value = true;
                    loadAvailableProducts();
                };

                const editOrder = (order) => {
                    orderEditMode.value = true;
                    currentOrder.value = order;
                    Object.assign(orderForm, {
                        order_number: order.order_number,
                        product_id: order.product_id,
                        quantity: order.quantity,
                        unit_price: order.unit_price,
                        total_price: order.total_price,
                        expire_date: order.expire_date ? new Date(order.expire_date) : '',
                        customer_info: order.customer_info,
                        notes: order.notes
                    });
                    orderDialogVisible.value = true;
                    loadAvailableProducts();
                };

                const saveOrder = async () => {
                    try {
                        const token = localStorage.getItem('agent_token');
                        if (!token) return;

                        const url = orderEditMode.value
                            ? `/api/agent/orders/${currentOrder.value.id}`
                            : '/api/agent/orders';
                        const method = orderEditMode.value ? 'PUT' : 'POST';

                        const orderData = { ...orderForm };
                        if (orderData.expire_date) {
                            if (orderData.expire_date instanceof Date) {
                                orderData.expire_date = orderData.expire_date.toISOString();
                            }
                        }

                        const response = await fetch(url, {
                            method: method,
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(orderData)
                        });

                        if (response.ok) {
                            ElMessage.success(orderEditMode.value ? '订单更新成功' : '订单创建成功');
                            orderDialogVisible.value = false;
                            loadOrders();
                        } else {
                            const error = await response.json();
                            let errorMessage = '操作失败';
                            if (error.detail) {
                                if (typeof error.detail === 'string') {
                                    errorMessage += '：' + error.detail;
                                } else if (Array.isArray(error.detail)) {
                                    errorMessage += '：' + error.detail.map(err => err.msg).join('; ');
                                } else if (typeof error.detail === 'object') {
                                    const details = [];
                                    for (const key in error.detail) {
                                        details.push(`${key}: ${error.detail[key]}`);
                                    }
                                    errorMessage += '：' + details.join('; ');
                                }
                            }
                            ElMessage.error(errorMessage);
                        }
                    } catch (error) {
                        console.error('Error saving order:', error);
                        ElMessage.error('操作失败：' + error.message);
                    }
                };

                const deleteOrder = (order) => {
                    ElMessageBox.confirm(`确定要删除订单 ${order.order_number} 吗？`, '确认删除', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(async () => {
                        try {
                            const token = localStorage.getItem('agent_token');
                            if (!token) return;

                            const response = await fetch(`/api/agent/orders/${order.id}`, {
                                method: 'DELETE',
                                headers: {
                                    'Authorization': `Bearer ${token}`
                                }
                            });

                            if (response.ok) {
                                ElMessage.success('订单删除成功');
                                loadOrders();
                            } else {
                                const error = await response.json();
                                ElMessage.error('删除失败：' + (error.detail || '未知错误'));
                            }
                        } catch (error) {
                            console.error('Error deleting order:', error);
                            ElMessage.error('删除失败：' + error.message);
                        }
                    });
                };

                const resetOrderForm = () => {
                    Object.assign(orderForm, {
                        order_number: '',
                        product_id: '',
                        quantity: 1,
                        unit_price: 0,
                        total_price: 0,
                        expire_date: '',
                        customer_info: '',
                        notes: ''
                    });
                };
                
                const navigate = (path) => {
                    window.location.href = path;
                };
                
                const handleCommand = (command) => {
                    if (command === 'profile') {
                        navigate('/agent/profile');
                    } else if (command === 'logout') {
                        ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            localStorage.removeItem('agent_token');
                            localStorage.removeItem('agent_info');
                            ElMessage.success('已退出登录');
                            window.location.href = '/agent/login';
                        });
                    }
                };
                
                onMounted(() => {
                    checkAuth();
                    // 根据页面加载不同数据
                    const path = window.location.pathname;
                    if (path === '/agent/dashboard') {
                        loadStats();
                        loadAuthorizedProducts();
                        loadRecentActivities();
                    } else if (path === '/agent/products') {
                        // 产品页面也需要加载授权产品数据
                        loadAuthorizedProducts();
                    } else if (path === '/agent/profile') {
                        loadProfile();
                    } else if (path === '/agent/licenses') {
                        loadLicenses();
                    } else if (path === '/agent/orders') {
                        loadOrders();
                    }

                    // 将方法暴露到全局，供子页面使用
                    window.loadAuthorizedProducts = loadAuthorizedProducts;
                    window.authorizedProducts = authorizedProducts;
                });

                return {
                    currentRoute,
                    agentInfo,
                    stats,
                    authorizedProducts,
                    recentActivities,
                    loading,
                    profileForm,
                    passwordForm,
                    changingPassword,
                    // 授权码管理
                    licenses,
                    searchKeyword,
                    currentPage,
                    pageSize,
                    total,
                    dialogVisible,
                    dialogTitle,
                    showCreateDialog,
                    licenseForm,
                    availableProducts,
                    expireOption,
                    // 订单管理
                    orders,
                    orderSearchKeyword,
                    orderStatusFilter,
                    orderDateRange,
                    orderProductFilter,
                    orderCurrentPage,
                    orderPageSize,
                    orderTotal,
                    orderDialogVisible,
                    orderEditMode,
                    currentOrder,
                    orderForm,
                    orderRules,
                    // 方法
                    navigate,
                    handleCommand,
                    loadStats,
                    loadAuthorizedProducts,
                    loadRecentActivities,
                    loadProfile,
                    changePassword,
                    resetPasswordForm,
                    loadLicenses,
                    searchLicenses,
                    handleSizeChange,
                    handleCurrentChange,
                    openCreateDialog,
                    loadAvailableProducts,
                    createLicense,
                    resetLicenseForm,
                    handleExpireOptionChange,
                    getStatusText,
                    getStatusType,
                    copyToClipboard,
                    loadOrders,
                    searchOrders,
                    handleOrderSizeChange,
                    handleOrderCurrentChange,
                    getOrderStatusText,
                    getOrderStatusType,
                    handleStatusChange,
                    openCreateOrderDialog,
                    editOrder,
                    saveOrder,
                    deleteOrder,
                    resetOrderForm,
                    formatDate,
                    getUsagePercentage,
                    getUsageColor
                };
            }
        });

        // 注册 Element Plus 图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus);
        app.mount('#app');
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
