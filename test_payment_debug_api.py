#!/usr/bin/env python3
"""
测试支付调试API
"""

import requests
import json

def test_payment_debug_api():
    """测试支付调试API"""
    base_url = "http://localhost:8008"
    
    print("🔍 测试支付调试API...")
    
    # 1. 测试获取配置列表
    print("\n1. 测试获取配置列表...")
    try:
        response = requests.get(f"{base_url}/api/payment-debug/config/list")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   配置数量: {len(data.get('data', []))}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 2. 测试获取调试记录列表
    print("\n2. 测试获取调试记录列表...")
    try:
        response = requests.get(f"{base_url}/api/payment-debug/debug/list")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   调试记录数量: {len(data.get('data', {}).get('records', []))}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 3. 测试创建调试支付
    print("\n3. 测试创建调试支付...")
    try:
        create_data = {
            "debug_name": "API测试支付",
            "payment_mode": "face_to_face",
            "user_id": "test_user_001",
            "product_id": 1,
            "amount": 0.01,
            "subject": "测试订单",
            "body": "这是一个测试订单",
            "timeout_minutes": 30
        }
        
        response = requests.post(
            f"{base_url}/api/payment-debug/debug/create",
            json=create_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("   ✅ 创建成功")
                debug_id = data.get("data", {}).get("id")
                print(f"   调试ID: {debug_id}")
                
                # 4. 测试查询支付状态
                if debug_id:
                    print(f"\n4. 测试查询支付状态 (ID: {debug_id})...")
                    try:
                        query_response = requests.get(f"{base_url}/api/payment-debug/debug/{debug_id}/query")
                        print(f"   查询状态码: {query_response.status_code}")
                        if query_response.status_code == 200:
                            query_data = query_response.json()
                            print(f"   查询结果: {query_data.get('success', False)}")
                        else:
                            print(f"   查询错误: {query_response.text}")
                    except Exception as e:
                        print(f"   查询异常: {str(e)}")
            else:
                print(f"   ❌ 创建失败: {data.get('error', '未知错误')}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
    except Exception as e:
        print(f"   异常: {str(e)}")

if __name__ == "__main__":
    test_payment_debug_api()
