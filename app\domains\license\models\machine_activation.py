from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.sql import func
from app.database import Base

class MachineActivation(Base):
    """机器码激活码记录表"""
    __tablename__ = "machine_activations"
    
    id = Column(Integer, primary_key=True, index=True)
    machine_id = Column(String(100), nullable=False, comment="机器码")
    activation_code = Column(String(200), nullable=False, comment="生成的激活码")
    days = Column(Integer, nullable=False, comment="有效期天数")
    expire_timestamp = Column(Integer, nullable=False, comment="过期时间戳")
    generation_timestamp = Column(Integer, nullable=False, comment="生成时间戳")
    is_active = Column(Boolean, default=True, comment="是否有效")
    notes = Column(Text, comment="备注")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<MachineActivation(machine_id='{self.machine_id}', activation_code='{self.activation_code}', days={self.days})>"
