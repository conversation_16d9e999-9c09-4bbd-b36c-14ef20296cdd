"""
支付调试模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, DECIMAL
from sqlalchemy.sql import func
from app.database import Base

class PaymentDebug(Base):
    """支付调试记录"""
    __tablename__ = "payment_debug"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 调试信息
    debug_name = Column(String(100), nullable=False, comment="调试名称")
    payment_mode = Column(String(50), nullable=False, comment="支付模式：face_to_face/barcode")
    
    # 支付参数
    user_id = Column(String(100), nullable=False, comment="用户ID")
    product_id = Column(Integer, nullable=False, comment="产品ID")
    amount = Column(DECIMAL(10, 2), nullable=False, comment="支付金额")
    subject = Column(String(200), nullable=False, comment="订单标题")
    body = Column(String(500), comment="订单描述")
    timeout_minutes = Column(Integer, default=30, comment="超时时间（分钟）")
    
    # 支付结果
    order_no = Column(String(100), comment="订单号")
    qr_code = Column(Text, comment="二维码内容")
    trade_no = Column(String(100), comment="支付宝交易号")
    payment_status = Column(String(50), comment="支付状态")
    
    # 调试状态
    debug_status = Column(String(50), default="created", comment="调试状态：created/paid/failed/timeout")
    error_message = Column(Text, comment="错误信息")
    
    # 支付环境配置
    alipay_config = Column(JSON, comment="支付宝配置信息")
    
    # 回调信息
    callback_received = Column(Boolean, default=False, comment="是否收到回调")
    callback_data = Column(JSON, comment="回调数据")
    callback_time = Column(DateTime, comment="回调时间")
    
    # 查询结果
    query_result = Column(JSON, comment="查询结果")
    last_query_time = Column(DateTime, comment="最后查询时间")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "debug_name": self.debug_name,
            "payment_mode": self.payment_mode,
            "user_id": self.user_id,
            "product_id": self.product_id,
            "amount": float(self.amount) if self.amount else None,
            "subject": self.subject,
            "body": self.body,
            "timeout_minutes": self.timeout_minutes,
            "order_no": self.order_no,
            "qr_code": self.qr_code,
            "trade_no": self.trade_no,
            "payment_status": self.payment_status,
            "debug_status": self.debug_status,
            "error_message": self.error_message,
            "alipay_config": self.alipay_config,
            "callback_received": self.callback_received,
            "callback_data": self.callback_data,
            "callback_time": self.callback_time.isoformat() if self.callback_time else None,
            "query_result": self.query_result,
            "last_query_time": self.last_query_time.isoformat() if self.last_query_time else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class PaymentConfig(Base):
    """支付配置"""
    __tablename__ = "payment_config"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 配置信息
    config_name = Column(String(100), nullable=False, unique=True, comment="配置名称")
    config_type = Column(String(50), default="alipay", comment="配置类型")
    is_active = Column(Boolean, default=False, comment="是否激活")
    
    # 支付宝配置
    app_id = Column(String(100), comment="支付宝APP_ID")
    app_private_key = Column(Text, comment="应用私钥")
    alipay_public_key = Column(Text, comment="支付宝公钥")
    gateway_url = Column(String(200), comment="网关地址")
    notify_url = Column(String(200), comment="回调地址")
    return_url = Column(String(200), comment="跳转地址")
    
    # 环境信息
    environment = Column(String(50), default="sandbox", comment="环境：sandbox/production")
    description = Column(Text, comment="配置描述")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "config_name": self.config_name,
            "config_type": self.config_type,
            "is_active": self.is_active,
            "app_id": self.app_id,
            "app_private_key": self.app_private_key[:50] + "..." if self.app_private_key else None,
            "alipay_public_key": self.alipay_public_key[:50] + "..." if self.alipay_public_key else None,
            "gateway_url": self.gateway_url,
            "notify_url": self.notify_url,
            "return_url": self.return_url,
            "environment": self.environment,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
