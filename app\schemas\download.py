from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class VerifyRequest(BaseModel):
    user_id: str
    license_key: str
    system_id: Optional[str] = None
    computer_name: Optional[str] = None
    ip_address: Optional[str] = None

class DownloadStatsRequest(BaseModel):
    user_id: str
    license_key: str
    system_id: Optional[str] = None
    computer_name: Optional[str] = None
    ip_address: Optional[str] = None
    file: str

class DownloadResponse(BaseModel):
    id: int
    user_id: str
    file_name: str
    download_time: datetime
    ip_address: Optional[str] = None
    computer_name: Optional[str] = None
    
    class Config:
        from_attributes = True

class VerifyResponse(BaseModel):
    status: str
