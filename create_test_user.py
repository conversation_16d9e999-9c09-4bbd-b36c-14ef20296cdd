#!/usr/bin/env python3
"""
创建测试用户
"""

from sqlalchemy.orm import Session
from app.database import SessionLocal, create_tables
from app.models.user import User, UserType
from app.utils.password import hash_password
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_user():
    """创建测试用户"""
    db = SessionLocal()
    try:
        # 检查用户是否已存在
        existing_user = db.query(User).filter(User.username == "user").first()
        if existing_user:
            logger.info("用户 'user' 已存在")
            # 更新密码
            existing_user.password_hash = hash_password("123456")
            db.commit()
            logger.info("已更新用户 'user' 的密码为 '123456'")
            return existing_user
        
        # 创建新用户
        user = User(
            user_id="user",
            username="user",
            password_hash=hash_password("123456"),
            email="<EMAIL>",
            phone="13800138000",
            full_name="测试用户",
            user_type=UserType.REGULAR,
            is_active=True,
            description="测试用户账户"
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        logger.info(f"创建测试用户成功:")
        logger.info(f"  用户名: {user.username}")
        logger.info(f"  密码: 123456")
        logger.info(f"  邮箱: {user.email}")
        logger.info(f"  用户类型: {user.user_type.value}")
        
        return user
        
    except Exception as e:
        logger.error(f"创建测试用户失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def create_admin_user():
    """创建管理员用户"""
    db = SessionLocal()
    try:
        # 检查管理员用户是否已存在
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if existing_admin:
            logger.info("管理员用户 'admin' 已存在")
            # 更新密码
            existing_admin.password_hash = hash_password("admin123")
            db.commit()
            logger.info("已更新管理员用户 'admin' 的密码为 'admin123'")
            return existing_admin
        
        # 创建管理员用户
        admin_user = User(
            user_id="admin",
            username="admin",
            password_hash=hash_password("admin123"),
            email="<EMAIL>",
            phone="13800138001",
            full_name="系统管理员",
            user_type=UserType.ADMIN,
            is_active=True,
            description="系统管理员账户"
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        logger.info(f"创建管理员用户成功:")
        logger.info(f"  用户名: {admin_user.username}")
        logger.info(f"  密码: admin123")
        logger.info(f"  邮箱: {admin_user.email}")
        logger.info(f"  用户类型: {admin_user.user_type.value}")
        
        return admin_user
        
    except Exception as e:
        logger.error(f"创建管理员用户失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def list_users():
    """列出所有用户"""
    db = SessionLocal()
    try:
        users = db.query(User).all()
        logger.info(f"当前系统中有 {len(users)} 个用户:")
        for user in users:
            logger.info(f"  ID: {user.id}, 用户名: {user.username}, 用户ID: {user.user_id}, 类型: {user.user_type.value}, 激活: {user.is_active}")
    except Exception as e:
        logger.error(f"查询用户失败: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    print("创建测试用户...")
    
    # 确保数据库表存在
    create_tables()
    
    # 创建测试用户
    create_test_user()
    
    # 创建管理员用户
    create_admin_user()
    
    # 列出所有用户
    list_users()
    
    print("\n测试用户创建完成！")
    print("可以使用以下账户登录:")
    print("  普通用户: user / 123456")
    print("  管理员: admin / admin123")
