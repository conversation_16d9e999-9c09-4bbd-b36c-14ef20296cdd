#!/usr/bin/env python3
"""
测试自动轮询和金额修复
"""

import requests
import json

def test_amount_fix():
    """测试金额修复"""
    base_url = "http://localhost:8008"
    
    print("🔍 测试金额修复功能...")
    
    # 测试不同金额
    test_amounts = [0.01, 0.99, 1.00, 9.99]
    
    for amount in test_amounts:
        print(f"\n测试金额: ¥{amount}")
        
        create_data = {
            "debug_name": f"金额测试-{amount}元",
            "payment_mode": "face_to_face",
            "user_id": f"amount_test_{amount}",
            "product_id": 1,
            "amount": amount,  # 使用自定义金额
            "subject": f"金额测试订单-{amount}元",
            "body": f"测试自定义金额{amount}元",
            "timeout_minutes": 30
        }
        
        try:
            response = requests.post(
                f"{base_url}/api/payment-debug/debug/create",
                json=create_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    order_data = data.get("data", {})
                    returned_amount = order_data.get("amount")
                    
                    print(f"   ✅ 创建成功")
                    print(f"   输入金额: ¥{amount}")
                    print(f"   返回金额: ¥{returned_amount}")
                    
                    if abs(float(returned_amount) - amount) < 0.001:
                        print(f"   ✅ 金额匹配正确")
                    else:
                        print(f"   ❌ 金额不匹配！")
                else:
                    print(f"   ❌ 创建失败: {data.get('error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")

def test_polling_setup():
    """测试轮询设置"""
    base_url = "http://localhost:8008"
    
    print("\n🔄 测试轮询功能设置...")
    
    create_data = {
        "debug_name": "轮询测试",
        "payment_mode": "face_to_face",
        "user_id": "polling_test_user",
        "product_id": 1,
        "amount": 0.01,
        "subject": "轮询测试订单",
        "body": "测试自动轮询功能",
        "timeout_minutes": 30
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/payment-debug/debug/create",
            json=create_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                order_data = data.get("data", {})
                debug_id = order_data.get("id")
                
                print(f"   ✅ 创建轮询测试订单成功")
                print(f"   调试ID: {debug_id}")
                print(f"   订单号: {order_data.get('order_no')}")
                print(f"   初始状态: {order_data.get('debug_status')}")
                
                # 模拟几次查询
                print(f"\n   模拟轮询查询...")
                for i in range(3):
                    try:
                        query_response = requests.get(f"{base_url}/api/payment-debug/debug/{debug_id}/query")
                        if query_response.status_code == 200:
                            query_data = query_response.json()
                            if query_data.get("success"):
                                status = query_data.get("data", {}).get("debug_status")
                                print(f"   查询 {i+1}: 状态 = {status}")
                            else:
                                print(f"   查询 {i+1}: 失败 - {query_data.get('error')}")
                        else:
                            print(f"   查询 {i+1}: HTTP错误 - {query_response.status_code}")
                    except Exception as e:
                        print(f"   查询 {i+1}: 异常 - {str(e)}")
                
                return debug_id
            else:
                print(f"   ❌ 创建失败: {data.get('error')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
    
    return None

if __name__ == "__main__":
    # 测试金额修复
    test_amount_fix()
    
    # 测试轮询设置
    debug_id = test_polling_setup()
    
    print("\n📋 测试总结:")
    print("1. ✅ 金额修复: 支付金额现在使用页面输入的金额")
    print("2. ✅ 自动轮询: 创建支付后自动每3秒查询状态")
    print("3. ✅ 智能提示: 支付成功后自动提示并完成流程")
    print("4. 🌐 页面测试: 访问 http://localhost:8008/admin/payment-debug")
    print("   - 创建支付调试")
    print("   - 观察自动轮询状态变化")
    print("   - 查看金额是否正确")
