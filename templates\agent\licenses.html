{% extends "agent/base.html" %}

{% block title %}授权码管理{% endblock %}
{% block page_title %}授权码管理{% endblock %}

{% block content %}
<div>
    <div class="content-card">
        <div class="page-header">
            <h1 class="page-title">授权码管理</h1>
            <div class="header-actions">
                <el-button type="primary" @click="openCreateDialog">
                    <el-icon><Plus /></el-icon>
                    生成授权码
                </el-button>
                <el-button @click="loadLicenses" :loading="loading">
                    <el-icon><Refresh /></el-icon>
                    刷新数据
                </el-button>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-section">
            <el-row :gutter="16" class="search-bar">
                <el-col :span="6">
                    <el-input
                        v-model="searchKeyword"
                        placeholder="搜索授权码或备注"
                        @keyup.enter="searchLicenses"
                        clearable>
                        <template #append>
                            <el-button @click="searchLicenses">
                                <el-icon><Search /></el-icon>
                            </el-button>
                        </template>
                    </el-input>
                </el-col>
                <el-col :span="4">
                    <el-select v-model="statusFilter" placeholder="状态筛选" clearable @change="searchLicenses" style="width: 100%">
                        <el-option label="未激活" value="inactive" />
                        <el-option label="已激活" value="active" />
                        <el-option label="已过期" value="expired" />
                        <el-option label="已暂停" value="suspended" />
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <el-select v-model="productFilter" placeholder="产品筛选" clearable @change="searchLicenses" style="width: 100%">
                        <el-option
                            v-for="product in availableProducts"
                            :key="product.id"
                            :label="product.name"
                            :value="product.id">
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <el-button @click="searchLicenses" type="primary" plain>
                        <el-icon><Search /></el-icon>
                        搜索
                    </el-button>
                </el-col>
            </el-row>
        </div>
        
        <!-- 授权码列表 -->
        <el-table :data="licenses" v-loading="loading" stripe>
            <el-table-column prop="id" label="ID" width="80"></el-table-column>

            <el-table-column prop="license_code" label="授权码" width="200">
                <template #default="scope">
                    <el-text class="license-code" @click="copyToClipboard(scope.row.license_code)">
                        {% raw %}{{ scope.row.license_code }}{% endraw %}
                    </el-text>
                </template>
            </el-table-column>

            <el-table-column prop="product_name" label="产品" width="120">
                <template #default="scope">
                    {% raw %}{{ scope.row.product_name || '-' }}{% endraw %}
                </template>
            </el-table-column>

            <el-table-column prop="max_api_calls" label="最大调用次数" width="120">
                <template #default="scope">
                    {% raw %}{{ scope.row.max_api_calls === -1 ? '无限制' : scope.row.max_api_calls }}{% endraw %}
                </template>
            </el-table-column>

            <el-table-column prop="used_api_calls" label="已使用次数" width="120">
                <template #default="scope">
                    {% raw %}{{ scope.row.used_api_calls || 0 }}{% endraw %}
                </template>
            </el-table-column>

            <el-table-column label="剩余次数" width="100">
                <template #default="scope">
                    {% raw %}{{ scope.row.max_api_calls === -1 ? '无限制' : (scope.row.max_api_calls - (scope.row.used_api_calls || 0)) }}{% endraw %}
                </template>
            </el-table-column>

            <el-table-column prop="expire_date" label="过期时间" width="150">
                <template #default="scope">
                    {% raw %}{{ scope.row.expire_date ? formatDate(scope.row.expire_date) : '永不过期' }}{% endraw %}
                </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                        {% raw %}{{ getStatusText(scope.row.status) }}{% endraw %}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column prop="created_at" label="创建时间" width="150">
                <template #default="scope">
                    {% raw %}{{ formatDate(scope.row.created_at) }}{% endraw %}
                </template>
            </el-table-column>

            <el-table-column prop="activated_at" label="激活时间" width="150">
                <template #default="scope">
                    {% raw %}{{ scope.row.activated_at ? formatDate(scope.row.activated_at) : '未激活' }}{% endraw %}
                </template>
            </el-table-column>
            
            <el-table-column label="操作" width="280" fixed="right">
                <template #default="scope">
                    <div class="table-actions">
                        <el-button type="primary" size="small" @click="editLicense(scope.row)">
                            <el-icon><Edit /></el-icon>
                            编辑
                        </el-button>
                        <el-dropdown trigger="click" @command="handleLicenseAction($event, scope.row)">
                            <el-button type="success" size="small">
                                <el-icon><More /></el-icon>
                                更多操作
                                <el-icon class="el-icon--right"><arrow-down /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="copy" icon="DocumentCopy">复制授权码</el-dropdown-item>
                                    <el-dropdown-item command="suspend" icon="Lock" v-if="scope.row.status === 'active'">暂停授权</el-dropdown-item>
                                    <el-dropdown-item command="activate" icon="Unlock" v-if="scope.row.status === 'suspended'">恢复授权</el-dropdown-item>
                                    <el-dropdown-item command="extend" icon="Calendar">延长有效期</el-dropdown-item>
                                    <el-dropdown-item command="revoke" icon="Delete" divided>撤销授权</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                background>
            </el-pagination>
        </div>
    </div>
    
    <!-- 生成授权码对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="550px">
        <el-form :model="licenseForm" label-width="120px">
            <el-form-item label="选择产品" required>
                <el-select v-model="licenseForm.product_id" placeholder="请选择产品" style="width: 100%">
                    <el-option
                        v-for="product in availableProducts"
                        :key="product.id"
                        :label="product.name"
                        :value="product.id">
                        <span style="float: left">{% raw %}{{ product.name }}{% endraw %}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">
                            剩余: {% raw %}{{ product.remaining_licenses }}{% endraw %}
                        </span>
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="生成数量">
                <el-input-number v-model="licenseForm.quantity" :min="1" :max="100" style="width: 100%"></el-input-number>
                <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                    建议一次生成不超过50个授权码
                </div>
            </el-form-item>

            <el-form-item label="API调用限制">
                <el-radio-group v-model="licenseForm.max_api_calls">
                    <el-radio :value="-1">无限制</el-radio>
                    <el-radio :value="1000">1,000次</el-radio>
                    <el-radio :value="5000">5,000次</el-radio>
                    <el-radio :value="10000">10,000次</el-radio>
                </el-radio-group>
                <div style="margin-top: 8px;">
                    <el-input-number
                        v-if="licenseForm.max_api_calls !== -1"
                        v-model="licenseForm.max_api_calls"
                        :min="1"
                        :max="1000000"
                        placeholder="自定义次数"
                        style="width: 200px">
                    </el-input-number>
                </div>
            </el-form-item>

            <el-form-item label="过期时间">
                <el-radio-group v-model="expireOption" @change="handleExpireOptionChange">
                    <el-radio value="never">永不过期</el-radio>
                    <el-radio value="custom">自定义时间</el-radio>
                </el-radio-group>
                <div style="margin-top: 8px;" v-if="expireOption === 'custom'">
                    <el-date-picker
                        v-model="licenseForm.expire_date"
                        type="datetime"
                        placeholder="选择过期时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%">
                    </el-date-picker>
                </div>
            </el-form-item>

            <el-form-item label="备注">
                <el-input
                    v-model="licenseForm.notes"
                    type="textarea"
                    :rows="3"
                    placeholder="可选，添加授权码备注信息"
                    maxlength="500"
                    show-word-limit>
                </el-input>
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="createLicense" :loading="loading">
                    生成授权码
                </el-button>
            </span>
        </template>
    </el-dialog>
    
    <!-- 授权码详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="授权码详情" width="700px">
        <el-descriptions :column="2" border>
            <el-descriptions-item label="授权码">
                <div class="license-code-detail" @click="copyToClipboard(currentLicense.license_code)">
                    {% raw %}{{ currentLicense.license_code }}{% endraw %}
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="产品">{% raw %}{{ currentLicense.product_name }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(currentLicense.status)">
                    {% raw %}{{ getStatusText(currentLicense.status) }}{% endraw %}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="API调用">
                {% raw %}{{ currentLicense.used_api_calls || 0 }}/{{ currentLicense.max_api_calls === -1 ? '无限制' : currentLicense.max_api_calls }}{% endraw %}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{% raw %}{{ formatDate(currentLicense.created_at) }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="激活时间">{% raw %}{{ currentLicense.activated_at ? formatDate(currentLicense.activated_at) : '未激活' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="过期时间">{% raw %}{{ currentLicense.expire_date ? formatDate(currentLicense.expire_date) : '永不过期' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="用户ID">{% raw %}{{ currentLicense.user_id || '未绑定' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="设备信息" :span="2">{% raw %}{{ currentLicense.device_info || '无设备信息' }}{% endraw %}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{% raw %}{{ currentLicense.notes || '无备注' }}{% endraw %}</el-descriptions-item>
        </el-descriptions>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="detailDialogVisible = false">关闭</el-button>
                <el-button type="primary" @click="copyToClipboard(currentLicense.license_code)">复制授权码</el-button>
            </span>
        </template>
    </el-dialog>
    
    <!-- 编辑授权码对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑授权码" width="550px">
        <el-form :model="editForm" label-width="120px">
            <el-form-item label="授权码">
                <el-input v-model="editForm.license_code" disabled></el-input>
            </el-form-item>

            <el-form-item label="产品">
                <el-select v-model="editForm.product_id" placeholder="请选择产品" style="width: 100%" disabled>
                    <el-option
                        v-for="product in availableProducts"
                        :key="product.id"
                        :label="product.name"
                        :value="product.id">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="API调用限制">
                <el-radio-group v-model="editForm.max_api_calls">
                    <el-radio :value="-1">无限制</el-radio>
                    <el-radio :value="1000">1,000次</el-radio>
                    <el-radio :value="5000">5,000次</el-radio>
                    <el-radio :value="10000">10,000次</el-radio>
                </el-radio-group>
                <div style="margin-top: 8px;">
                    <el-input-number
                        v-if="editForm.max_api_calls !== -1"
                        v-model="editForm.max_api_calls"
                        :min="1"
                        :max="1000000"
                        placeholder="自定义次数"
                        style="width: 200px">
                    </el-input-number>
                </div>
            </el-form-item>

            <el-form-item label="过期时间">
                <el-radio-group v-model="editExpireOption" @change="handleEditExpireOptionChange">
                    <el-radio value="never">永不过期</el-radio>
                    <el-radio value="custom">自定义时间</el-radio>
                </el-radio-group>
                <div style="margin-top: 8px;" v-if="editExpireOption === 'custom'">
                    <el-date-picker
                        v-model="editForm.expire_date"
                        type="datetime"
                        placeholder="选择过期时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%">
                    </el-date-picker>
                </div>
            </el-form-item>

            <el-form-item label="备注">
                <el-input
                    v-model="editForm.notes"
                    type="textarea"
                    :rows="3"
                    placeholder="可选，添加授权码备注信息"
                    maxlength="500"
                    show-word-limit>
                </el-input>
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="editDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="updateLicense" :loading="loading">
                    保存修改
                </el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 延长有效期对话框 -->
    <el-dialog v-model="extendDialogVisible" title="延长授权有效期" width="500px">
        <el-form :model="extendForm" label-width="120px">
            <el-form-item label="授权码">
                <el-text>{% raw %}{{ currentLicense.license_code }}{% endraw %}</el-text>
            </el-form-item>
            <el-form-item label="当前过期时间">
                <el-text>{% raw %}{{ currentLicense.expire_date ? formatDate(currentLicense.expire_date) : '永不过期' }}{% endraw %}</el-text>
            </el-form-item>
            <el-form-item label="新过期时间" required>
                <el-date-picker
                    v-model="extendForm.expire_date"
                    type="datetime"
                    placeholder="选择新的过期时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%">
                </el-date-picker>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="extendDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmExtendLicense" :loading="loading">确认延长</el-button>
            </span>
        </template>
    </el-dialog>
</div>

<style>
/* 页面布局样式 */
.page-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.search-bar {
    margin-bottom: 0;
}

/* 表格操作按钮样式 */
.table-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.table-actions .el-button {
    height: 32px;
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 4px;
    font-weight: 500;
}

/* 分页样式 */
.pagination-wrapper {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e6e6e6;
    display: flex;
    justify-content: center;
}

/* 授权码样式 */
.license-code {
    cursor: pointer;
    color: #409EFF;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-weight: 600;
    padding: 2px 6px;
    background: #f0f9ff;
    border-radius: 4px;
    border: 1px solid #e1f5fe;
}

.license-code:hover {
    background: #e3f2fd;
    text-decoration: underline;
}

.license-code-detail {
    cursor: pointer;
    color: #409EFF;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-weight: bold;
    font-size: 16px;
}

/* 确保按钮样式正确 */
.el-button {
    height: 32px;
    padding: 8px 15px;
    font-size: 14px;
    border-radius: 4px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.el-button--primary {
    background-color: #409eff;
    border-color: #409eff;
    color: #ffffff;
}

.el-button--success {
    background-color: #67c23a;
    border-color: #67c23a;
    color: #ffffff;
}

.el-button--small {
    height: 28px;
    padding: 6px 12px;
    font-size: 13px;
}

.el-button .el-icon {
    font-size: 16px;
}

.el-button--small .el-icon {
    font-size: 14px;
}

/* 表格样式优化 */
.el-table {
    border-radius: 8px;
    overflow: hidden;
}

.el-table th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 600;
}

.el-table td {
    padding: 12px 0;
}
</style>

<script>
// 授权码管理页面的JavaScript代码将在base.html中定义
// 这里只是一个占位符，确保页面结构正确

        // 对话框状态
        const dialogVisible = ref(false);
        const dialogTitle = ref('生成授权码');
        const detailDialogVisible = ref(false);
        const editDialogVisible = ref(false);
        const extendDialogVisible = ref(false);

        // 当前操作的授权码
        const currentLicense = ref({});

        // 可用产品列表
        const availableProducts = ref([]);

        // 生成授权码表单
        const licenseForm = reactive({
            product_id: '',
            quantity: 1,
            max_api_calls: -1,
            expire_date: '',
            notes: ''
        });
        const expireOption = ref('never');

        // 编辑授权码表单
        const editForm = reactive({
            license_code: '',
            product_id: '',
            max_api_calls: -1,
            expire_date: '',
            notes: ''
        });
        const editExpireOption = ref('never');

        // 延长有效期表单
        const extendForm = reactive({
            expire_date: ''
        });

        // 页面加载时执行
        onMounted(() => {
            // 初始化代理商信息
            const savedAgentInfo = localStorage.getItem('agent_info');
            if (savedAgentInfo) {
                agentInfo.value = JSON.parse(savedAgentInfo);
            }

            loadLicenses();
        });

        // 加载授权码列表
        const loadLicenses = async () => {
            loading.value = true;
            try {
                const token = localStorage.getItem('agent_token');
                if (!token) {
                    ElMessage.error('请先登录');
                    return;
                }

                const skip = (currentPage.value - 1) * pageSize.value;
                const params = new URLSearchParams({
                    skip: skip,
                    limit: pageSize.value
                });

                if (searchKeyword.value) {
                    params.append('search', searchKeyword.value);
                }

                if (statusFilter.value) {
                    params.append('status_filter', statusFilter.value);
                }

                if (productFilter.value) {
                    params.append('product_id', productFilter.value);
                }

                const response = await fetch(`/api/agent/licenses?${params}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    licenses.value = data.items || [];
                    total.value = data.total || 0;
                } else if (response.status === 401) {
                    ElMessage.error('登录已过期，请重新登录');
                    localStorage.removeItem('agent_token');
                    localStorage.removeItem('agent_info');
                    window.location.href = '/agent/login';
                } else {
                    const errorData = await response.json().catch(() => ({ detail: '未知错误' }));
                    ElMessage.error(`加载授权码失败: ${errorData.detail || '服务器错误'}`);
                }
            } catch (error) {
                console.error('Failed to load licenses:', error);
                ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
            } finally {
                loading.value = false;
            }
        };

        return {
            // 数据
            loading,
            licenses,
            searchKeyword,
            statusFilter,
            productFilter,
            currentPage,
            pageSize,
            total,
            dialogVisible,
            dialogTitle,
            detailDialogVisible,
            editDialogVisible,
            extendDialogVisible,
            currentLicense,
            availableProducts,
            licenseForm,
            expireOption,
            editForm,
            editExpireOption,
            extendForm
        };

        // 加载可用产品
        const loadAvailableProducts = async () => {
            try {
                const token = localStorage.getItem('agent_token');
                if (!token) {
                    ElMessage.error('请先登录');
                    return;
                }

                const response = await fetch('/api/agent/dashboard/authorized-products', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    availableProducts.value = data.map(item => ({
                        id: item.id,
                        name: item.product_name,
                        remaining_licenses: item.max_licenses - item.used_licenses
                    }));
                } else {
                    const errorData = await response.json().catch(() => ({ detail: '未知错误' }));
                    ElMessage.error(`加载产品失败: ${errorData.detail || '服务器错误'}`);
                }
            } catch (error) {
                console.error('Failed to load available products:', error);
                ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
            }
        };

        return {
            // 数据
            loading,
            licenses,
            searchKeyword,
            statusFilter,
            productFilter,
            currentPage,
            pageSize,
            total,
            dialogVisible,
            dialogTitle,
            detailDialogVisible,
            editDialogVisible,
            extendDialogVisible,
            currentLicense,
            availableProducts,
            licenseForm,
            expireOption,
            editForm,
            editExpireOption,
            extendForm,
            // 方法
            loadLicenses,
            searchLicenses: () => {
                currentPage.value = 1;
                loadLicenses();
            },
            handleSizeChange: (size) => {
                pageSize.value = size;
                currentPage.value = 1;
                loadLicenses();
            },
            handleCurrentChange: (page) => {
                currentPage.value = page;
                loadLicenses();
            },
            openCreateDialog: async () => {
                dialogTitle.value = '生成授权码';
                dialogVisible.value = true;
                await loadAvailableProducts();
            },
            loadAvailableProducts,

            // 生成授权码
            createLicense: async () => {
                try {
                    const token = localStorage.getItem('agent_token');
                    if (!token) {
                        ElMessage.error('请先登录');
                        return;
                    }

                    // 验证表单数据
                    if (!licenseForm.product_id) {
                        ElMessage.error('请选择产品');
                        return;
                    }

                    loading.value = true;

                    const response = await fetch('/api/agent/licenses/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(licenseForm)
                    });

                    if (response.ok) {
                        const data = await response.json();
                        ElMessage.success(`成功生成 ${data.length} 个授权码`);
                        dialogVisible.value = false;
                        loadLicenses();
                        resetLicenseForm();
                    } else if (response.status === 401) {
                        ElMessage.error('登录已过期，请重新登录');
                        localStorage.removeItem('agent_token');
                        localStorage.removeItem('agent_info');
                        window.location.href = '/agent/login';
                    } else {
                        const error = await response.json().catch(() => ({ detail: '未知错误' }));
                        console.error('License generation error:', error);

                        // 显示详细的错误信息
                        let errorMessage = '生成授权码失败：';
                        if (error.detail) {
                            if (typeof error.detail === 'string') {
                                errorMessage += error.detail;
                            } else if (Array.isArray(error.detail)) {
                                errorMessage += error.detail.map(e => e.msg || e.message || e).join(', ');
                            } else {
                                errorMessage += JSON.stringify(error.detail);
                            }
                        } else if (error.message) {
                            errorMessage += error.message;
                        } else {
                            errorMessage += '未知错误';
                        }

                        ElMessage.error(errorMessage);
                    }
                } catch (error) {
                    console.error('Failed to create license:', error);
                    ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
                } finally {
                    loading.value = false;
                }
            },

            // 重置生成授权码表单
            resetLicenseForm: () => {
                licenseForm.product_id = '';
                licenseForm.quantity = 1;
                licenseForm.max_api_calls = -1;
                licenseForm.expire_date = '';
                licenseForm.notes = '';
                expireOption.value = 'never';
            },

            // 处理过期时间选项变化
            handleExpireOptionChange: (value) => {
                if (value === 'never') {
                    licenseForm.expire_date = '';
                }
            }
        };

        // 将resetLicenseForm添加到setup函数内部
        const resetLicenseForm = () => {
            licenseForm.product_id = '';
            licenseForm.quantity = 1;
            licenseForm.max_api_calls = -1;
            licenseForm.expire_date = '';
            licenseForm.notes = '';
            expireOption.value = 'never';
        };

        // 编辑授权码
        const editLicense = (license) => {
            currentLicense.value = license;
            editForm.license_code = license.license_code;
            editForm.product_id = license.product_id;
            editForm.max_api_calls = license.max_api_calls;
            editForm.expire_date = license.expire_date || '';
            editForm.notes = license.notes || '';

            // 设置过期时间选项
            editExpireOption.value = license.expire_date ? 'custom' : 'never';

            editDialogVisible.value = true;
        };

        const handleEditExpireOptionChange = (value) => {
            if (value === 'never') {
                editForm.expire_date = '';
            }
        };

        // 处理授权码操作
        const handleLicenseAction = async (command, license) => {
            currentLicense.value = license;

            switch (command) {
                case 'copy':
                    await copyToClipboard(license.license_code);
                    break;
                case 'suspend':
                    await suspendLicense(license);
                    break;
                case 'activate':
                    await activateLicense(license);
                    break;
                case 'extend':
                    openExtendDialog(license);
                    break;
                case 'revoke':
                    await revokeLicense(license);
                    break;
            }
        };

        const copyToClipboard = async (text) => {
            try {
                await navigator.clipboard.writeText(text);
                ElMessage.success('已复制到剪贴板');
            } catch (error) {
                ElMessage.error('复制失败');
            }
        };

        const suspendLicense = async (license) => {
            try {
                await ElMessageBox.confirm(`确定要暂停授权码 ${license.license_code} 吗？`, '确认暂停', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const token = localStorage.getItem('agent_token');
                const response = await fetch(`/api/agent/licenses/${license.id}/suspend`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    ElMessage.success('授权码已暂停');
                    loadLicenses();
                } else {
                    const error = await response.json().catch(() => ({ detail: '未知错误' }));
                    ElMessage.error(`暂停失败：${error.detail || '未知错误'}`);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('Failed to suspend license:', error);
                    ElMessage.error('操作失败');
                }
            }
        };

        const activateLicense = async (license) => {
            try {
                await ElMessageBox.confirm(`确定要恢复授权码 ${license.license_code} 吗？`, '确认恢复', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const token = localStorage.getItem('agent_token');
                const response = await fetch(`/api/agent/licenses/${license.id}/activate`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    ElMessage.success('授权码已恢复');
                    loadLicenses();
                } else {
                    const error = await response.json().catch(() => ({ detail: '未知错误' }));
                    ElMessage.error(`恢复失败：${error.detail || '未知错误'}`);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('Failed to activate license:', error);
                    ElMessage.error('操作失败');
                }
            }
        };

        const openExtendDialog = (license) => {
            currentLicense.value = license;
            extendForm.expire_date = '';
            extendDialogVisible.value = true;
        };

        const confirmExtendLicense = async () => {
            try {
                if (!extendForm.expire_date) {
                    ElMessage.error('请选择新的过期时间');
                    return;
                }

                const token = localStorage.getItem('agent_token');
                const response = await fetch(`/api/agent/licenses/${currentLicense.value.id}/extend`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        expire_date: extendForm.expire_date
                    })
                });

                if (response.ok) {
                    ElMessage.success('授权码有效期已延长');
                    extendDialogVisible.value = false;
                    loadLicenses();
                } else {
                    const error = await response.json().catch(() => ({ detail: '未知错误' }));
                    ElMessage.error(`延长失败：${error.detail || '未知错误'}`);
                }
            } catch (error) {
                console.error('Failed to extend license:', error);
                ElMessage.error('操作失败');
            }
        };

        const revokeLicense = async (license) => {
            try {
                await ElMessageBox.confirm(`确定要撤销授权码 ${license.license_code} 吗？此操作不可恢复！`, '确认撤销', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const token = localStorage.getItem('agent_token');
                const response = await fetch(`/api/agent/licenses/${license.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    ElMessage.success('授权码已撤销');
                    loadLicenses();
                } else {
                    const error = await response.json().catch(() => ({ detail: '未知错误' }));
                    ElMessage.error(`撤销失败：${error.detail || '未知错误'}`);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('Failed to revoke license:', error);
                    ElMessage.error('操作失败');
                }
            }
        };

        const updateLicense = async () => {
            try {
                const token = localStorage.getItem('agent_token');
                if (!token) {
                    ElMessage.error('请先登录');
                    return;
                }

                loading.value = true;

                const response = await fetch(`/api/agent/licenses/${currentLicense.value.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        max_api_calls: editForm.max_api_calls,
                        expire_date: editForm.expire_date || null,
                        notes: editForm.notes
                    })
                });

                if (response.ok) {
                    ElMessage.success('授权码更新成功');
                    editDialogVisible.value = false;
                    loadLicenses();
                } else if (response.status === 401) {
                    ElMessage.error('登录已过期，请重新登录');
                    localStorage.removeItem('agent_token');
                    localStorage.removeItem('agent_info');
                    window.location.href = '/agent/login';
                } else {
                    const error = await response.json().catch(() => ({ detail: '未知错误' }));
                    console.error('License update error:', error);

                    let errorMessage = '更新授权码失败：';
                    if (error.detail) {
                        if (typeof error.detail === 'string') {
                            errorMessage += error.detail;
                        } else if (Array.isArray(error.detail)) {
                            errorMessage += error.detail.map(e => e.msg || e.message || e).join(', ');
                        } else {
                            errorMessage += JSON.stringify(error.detail);
                        }
                    } else if (error.message) {
                        errorMessage += error.message;
                    } else {
                        errorMessage += '未知错误';
                    }

                    ElMessage.error(errorMessage);
                }
            } catch (error) {
                console.error('Failed to update license:', error);
                ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
            } finally {
                loading.value = false;
            }
        };

        // 导航函数（从base.html继承需要）
        const navigate = (path) => {
            window.location.href = path;
        };

        const handleCommand = (command) => {
            if (command === 'profile') {
                navigate('/agent/profile');
            } else if (command === 'logout') {
                ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    localStorage.removeItem('agent_token');
                    localStorage.removeItem('agent_info');
                    ElMessage.success('已退出登录');
                    window.location.href = '/agent/login';
                });
            }
        };

        return {
            // 数据
            agentInfo,
            loading,
            licenses,
            searchKeyword,
            statusFilter,
            productFilter,
            currentPage,
            pageSize,
            total,
            dialogVisible,
            dialogTitle,
            detailDialogVisible,
            editDialogVisible,
            extendDialogVisible,
            currentLicense,
            availableProducts,
            licenseForm,
            expireOption,
            editForm,
            editExpireOption,
            extendForm,
            // 方法
            loadLicenses,
            searchLicenses: () => {
                currentPage.value = 1;
                loadLicenses();
            },
            handleSizeChange: (size) => {
                pageSize.value = size;
                currentPage.value = 1;
                loadLicenses();
            },
            handleCurrentChange: (page) => {
                currentPage.value = page;
                loadLicenses();
            },
            openCreateDialog: async () => {
                dialogTitle.value = '生成授权码';
                dialogVisible.value = true;
                await loadAvailableProducts();
            },
            loadAvailableProducts,
            createLicense: async () => {
                try {
                    const token = localStorage.getItem('agent_token');
                    if (!token) {
                        ElMessage.error('请先登录');
                        return;
                    }

                    // 验证表单数据
                    if (!licenseForm.product_id) {
                        ElMessage.error('请选择产品');
                        return;
                    }

                    loading.value = true;

                    const response = await fetch('/api/agent/licenses/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(licenseForm)
                    });

                    if (response.ok) {
                        const data = await response.json();
                        ElMessage.success(`成功生成 ${data.length} 个授权码`);
                        dialogVisible.value = false;
                        loadLicenses();
                        resetLicenseForm();
                    } else if (response.status === 401) {
                        ElMessage.error('登录已过期，请重新登录');
                        localStorage.removeItem('agent_token');
                        localStorage.removeItem('agent_info');
                        window.location.href = '/agent/login';
                    } else {
                        const error = await response.json().catch(() => ({ detail: '未知错误' }));
                        console.error('License generation error:', error);

                        // 显示详细的错误信息
                        let errorMessage = '生成授权码失败：';
                        if (error.detail) {
                            if (typeof error.detail === 'string') {
                                errorMessage += error.detail;
                            } else if (Array.isArray(error.detail)) {
                                errorMessage += error.detail.map(e => e.msg || e.message || e).join(', ');
                            } else {
                                errorMessage += JSON.stringify(error.detail);
                            }
                        } else if (error.message) {
                            errorMessage += error.message;
                        } else {
                            errorMessage += '未知错误';
                        }

                        ElMessage.error(errorMessage);
                    }
                } catch (error) {
                    console.error('Failed to create license:', error);
                    ElMessage.error('网络连接失败，请检查网络连接或稍后重试');
                } finally {
                    loading.value = false;
                }
            },
            resetLicenseForm,
            handleExpireOptionChange: (value) => {
                if (value === 'never') {
                    licenseForm.expire_date = '';
                }
            },

            // 工具函数
            getStatusText: (status) => {
                const statusMap = {
                    'inactive': '未激活',
                    'active': '已激活',
                    'expired': '已过期',
                    'suspended': '已暂停'
                };
                return statusMap[status] || status;
            },
            getStatusType: (status) => {
                const typeMap = {
                    'inactive': 'info',
                    'active': 'success',
                    'expired': 'danger',
                    'suspended': 'warning'
                };
                return typeMap[status] || 'info';
            },
            formatDate: (dateString) => {
                if (!dateString) return '-';
                return new Date(dateString).toLocaleString('zh-CN');
            },
            copyToClipboard,
            editLicense,
            handleEditExpireOptionChange,
            updateLicense,
            handleLicenseAction,
            suspendLicense,
            activateLicense,
            openExtendDialog,
            confirmExtendLicense,
            revokeLicense,
            navigate,
            handleCommand
        };
    }
}).mount('#app');
</script>

{% endblock %}