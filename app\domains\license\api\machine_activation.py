from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional
import logging

from app.database import get_db
from app.schemas.machine_activation import (
    MachineActivationCreate, 
    MachineActivationUpdate, 
    MachineActivationResponse,
    MachineActivationListResponse,
    MachineActivationStatsResponse
)
from app.services.machine_activation_service import MachineActivationService

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=MachineActivationResponse)
async def create_machine_activation(
    activation_data: MachineActivationCreate,
    db: Session = Depends(get_db)
):
    """创建机器码激活码"""
    try:
        service = MachineActivationService()
        activation = service.create_machine_activation(
            db=db,
            machine_id=activation_data.machine_id,
            days=activation_data.days,
            notes=activation_data.notes,
            algorithm_id=activation_data.algorithm_id
        )
        
        if not activation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="激活码生成失败"
            )
        
        return activation
        
    except Exception as e:
        logger.error(f"创建机器码激活码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/", response_model=MachineActivationListResponse)
async def get_machine_activations(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    machine_id: Optional[str] = Query(None, description="机器码筛选"),
    db: Session = Depends(get_db)
):
    """获取机器码激活码列表"""
    try:
        service = MachineActivationService()
        result = service.get_machine_activations(
            db=db,
            skip=skip,
            limit=limit,
            machine_id=machine_id
        )
        
        return result
        
    except Exception as e:
        logger.error(f"获取机器码激活码列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/stats", response_model=MachineActivationStatsResponse)
async def get_machine_activation_stats(
    db: Session = Depends(get_db)
):
    """获取机器码激活码统计信息"""
    try:
        service = MachineActivationService()
        stats = service.get_statistics(db)
        
        return stats
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/{activation_id}", response_model=MachineActivationResponse)
async def get_machine_activation(
    activation_id: int,
    db: Session = Depends(get_db)
):
    """获取单个机器码激活码记录"""
    try:
        service = MachineActivationService()
        activation = service.get_machine_activation_by_id(db, activation_id)
        
        if not activation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记录不存在"
            )
        
        return activation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取机器码激活码记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.put("/{activation_id}", response_model=MachineActivationResponse)
async def update_machine_activation(
    activation_id: int,
    update_data: MachineActivationUpdate,
    db: Session = Depends(get_db)
):
    """更新机器码激活码记录"""
    try:
        service = MachineActivationService()
        activation = service.update_machine_activation(
            db=db,
            activation_id=activation_id,
            notes=update_data.notes,
            is_active=update_data.is_active
        )
        
        if not activation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记录不存在"
            )
        
        return activation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新机器码激活码记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.delete("/{activation_id}")
async def delete_machine_activation(
    activation_id: int,
    db: Session = Depends(get_db)
):
    """删除机器码激活码记录"""
    try:
        service = MachineActivationService()
        success = service.delete_machine_activation(db, activation_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记录不存在"
            )
        
        return {"message": "删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除机器码激活码记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )
