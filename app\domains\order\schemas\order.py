from typing import Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime
from decimal import Decimal
from app.models.order import OrderStatus

class OrderBase(BaseModel):
    order_number: Optional[str] = None
    product_id: Optional[int] = None
    quantity: int = Field(ge=1, description="订单数量，必须大于0")
    unit_price: Optional[Decimal] = Field(None, ge=0, description="单价")
    total_price: Optional[Decimal] = Field(None, ge=0, description="总价")
    status: OrderStatus = OrderStatus.PENDING
    expire_date: Optional[datetime] = None
    customer_info: Optional[str] = None
    notes: Optional[str] = None

    @validator('status', pre=True)
    def validate_status(cls, v):
        if isinstance(v, str):
            try:
                return OrderStatus[v.upper()]
            except KeyError:
                raise ValueError(f'Invalid status value: {v}')
        return v

class OrderCreate(OrderBase):
    agent_id: Optional[int] = None
    created_by_admin: bool = False

class OrderUpdate(OrderBase):
    status: Optional[OrderStatus] = None
    quantity: Optional[int] = Field(None, ge=1, description="订单数量，必须大于0")
    unit_price: Optional[Decimal] = Field(None, ge=0, description="单价")
    total_price: Optional[Decimal] = Field(None, ge=0, description="总价")

class OrderResponse(OrderBase):
    id: int
    agent_id: Optional[int]
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class OrderVerifyRequest(BaseModel):
    order_number: str
    user_id: Optional[str] = None
