<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理商登录 - FocuSee授权管理系统</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .agent-gradient {
            background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .input-focus:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .login-button {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 15%;
            left: 15%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 25%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            bottom: 15%;
            left: 25%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body class="min-h-screen agent-gradient relative overflow-hidden">
    <!-- 浮动装饰元素 -->
    <div class="floating-shapes">
        <div class="shape w-32 h-32 bg-white rounded-full"></div>
        <div class="shape w-24 h-24 bg-white rounded-lg"></div>
        <div class="shape w-16 h-16 bg-white rounded-full"></div>
    </div>

    <div class="min-h-screen flex items-center justify-center p-4 relative z-10">
        <div class="w-full max-w-md">
            <!-- 登录卡片 -->
            <div class="glass-effect rounded-2xl p-8 shadow-2xl">
                <!-- Logo 和标题 -->
                <div class="text-center mb-8">
                    <div class="mb-6">
                        <img src="https://www.gemoo-resource.com/focusee/img/<EMAIL>"
                             alt="FocuSee"
                             class="h-12 mx-auto mb-4">
                    </div>
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">代理商登录</h1>
                    <p class="text-gray-600">FocuSee 授权管理系统</p>
                    <div class="mt-4 inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                        <i class="fas fa-users mr-2"></i>代理商专属入口
                    </div>
                </div>

                <!-- 登录表单 -->
                <div id="app">
                    <form @submit.prevent="handleLogin" class="space-y-6">
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user-tie mr-2 text-green-600"></i>代理商账号
                            </label>
                            <input type="text"
                                   id="username"
                                   v-model="loginForm.username"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition duration-300"
                                   placeholder="请输入代理商用户名">
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-key mr-2 text-green-600"></i>登录密码
                            </label>
                            <input type="password"
                                   id="password"
                                   v-model="loginForm.password"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition duration-300"
                                   placeholder="请输入密码">
                        </div>

                        <div v-if="errorMessage" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                            <i class="fas fa-exclamation-circle mr-2"></i>{{ errorMessage }}
                        </div>

                        <button type="submit"
                                :disabled="loading"
                                class="w-full login-button text-white py-3 px-4 rounded-lg font-semibold text-lg"
                                :class="{ 'opacity-50 cursor-not-allowed': loading }">
                            <i class="fas fa-sign-in-alt mr-2" :class="{ 'fa-spinner fa-spin': loading }"></i>
                            <span v-if="loading">登录中...</span>
                            <span v-else>登录代理商系统</span>
                        </button>
                    </form>
                </div>

                <!-- 底部链接 -->
                <div class="mt-8 text-center">
                    <div class="flex justify-center space-x-6 text-sm text-gray-500">
                        <a href="/" class="hover:text-green-600 transition duration-300">
                            <i class="fas fa-home mr-1"></i>返回首页
                        </a>
                        <a href="/admin" class="hover:text-green-600 transition duration-300">
                            <i class="fas fa-cog mr-1"></i>管理员登录
                        </a>
                        <a href="/contact" class="hover:text-green-600 transition duration-300">
                            <i class="fas fa-envelope mr-1"></i>联系我们
                        </a>
                    </div>
                </div>
            </div>

            <!-- 版权信息 -->
            <div class="text-center mt-8 text-white text-opacity-80">
                <p class="text-sm">
                    <i class="fas fa-copyright mr-1"></i>2024 FocuSee. 保留所有权利.
                </p>
            </div>
        </div>
    </div>
    <!-- Vue.js 应用 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
        const { createApp, ref } = Vue;

        const app = createApp({
            setup() {
                const loading = ref(false);
                const errorMessage = ref('');

                const loginForm = ref({
                    username: '',
                    password: ''
                });

                const handleLogin = async () => {
                    if (!loginForm.value.username || !loginForm.value.password) {
                        errorMessage.value = '请输入用户名和密码';
                        return;
                    }

                    loading.value = true;
                    errorMessage.value = '';

                    try {
                        const response = await fetch('/api/agent-auth/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(loginForm.value)
                        });

                        if (response.ok) {
                            const data = await response.json();

                            // 保存token到localStorage
                            localStorage.setItem('agent_token', data.access_token);
                            localStorage.setItem('agent_info', JSON.stringify(data.agent_info));

                            // 跳转到代理商仪表板
                            window.location.href = '/agent/dashboard';

                        } else {
                            const error = await response.json();
                            errorMessage.value = error.detail || '登录失败，请检查用户名和密码';
                        }

                    } catch (error) {
                        errorMessage.value = '网络错误，请稍后重试';
                    } finally {
                        loading.value = false;
                    }
                };

                return {
                    loginForm,
                    loading,
                    errorMessage,
                    handleLogin
                };
            }
        });

        app.mount('#app');
    </script>
</body>
</html>
