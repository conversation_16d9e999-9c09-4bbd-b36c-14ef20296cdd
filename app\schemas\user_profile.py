from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class UserProfileBase(BaseModel):
    display_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    address: Optional[str] = None
    avatar_url: Optional[str] = None

class UserProfileCreate(UserProfileBase):
    user_id: str
    preferences: Optional[str] = None

class UserProfileUpdate(UserProfileBase):
    preferences: Optional[str] = None

class UserProfileResponse(UserProfileBase):
    id: int
    user_id: str
    preferences: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class UserLoginRequest(BaseModel):
    user_id: str
    password: str

class UserRegisterRequest(BaseModel):
    user_id: str
    password: str
    email: Optional[EmailStr] = None
    display_name: Optional[str] = None
