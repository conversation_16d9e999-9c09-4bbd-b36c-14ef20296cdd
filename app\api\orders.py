from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.schemas.order import OrderCreate, OrderUpdate, OrderResponse, OrderVerifyRequest
from app.models.order import OrderStatus
from app.services.order_service import OrderService
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=OrderResponse, status_code=status.HTTP_201_CREATED)
async def create_order(
    order_data: OrderCreate,
    db: Session = Depends(get_db)
):
    """创建订单"""
    order = OrderService.create_order(db, order_data)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to create order. Please check the provided data."
        )
    
    return order

@router.get("/", response_model=List[OrderResponse])
async def get_orders(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    agent_id: Optional[int] = Query(None),
    status: Optional[OrderStatus] = Query(None),
    created_by_admin: Optional[bool] = Query(None),
    keyword: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取订单列表"""
    if keyword:
        orders = OrderService.search_orders(db, keyword, skip, limit)
    else:
        orders = OrderService.get_orders(db, skip, limit, agent_id, status, created_by_admin)

    return orders

@router.get("/{order_id}", response_model=OrderResponse)
async def get_order(
    order_id: int,
    db: Session = Depends(get_db)
):
    """获取单个订单"""
    order = OrderService.get_order_by_id(db, order_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )
    
    return order

@router.put("/{order_id}", response_model=OrderResponse)
async def update_order(
    order_id: int,
    order_data: OrderUpdate,
    db: Session = Depends(get_db)
):
    """更新订单"""
    order = OrderService.update_order(db, order_id, order_data)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update order. Order may not exist or data is invalid."
        )
    
    return order

@router.delete("/{order_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_order(
    order_id: int,
    db: Session = Depends(get_db)
):
    """删除订单"""
    success = OrderService.delete_order(db, order_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )

@router.get("/stats/count")
async def get_order_count(
    agent_id: Optional[int] = Query(None),
    status: Optional[OrderStatus] = Query(None),
    created_by_admin: Optional[bool] = Query(None),
    keyword: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取订单统计"""
    if keyword:
        count = OrderService.get_search_order_count(db, keyword, agent_id)
    else:
        count = OrderService.get_order_count(db, agent_id, status, created_by_admin)
    return {"count": count}

@router.post("/verify")
async def verify_order(
    verify_data: OrderVerifyRequest,
    db: Session = Depends(get_db)
):
    """验证订单（公开接口，用于客户端验证）"""
    order = OrderService.verify_order(db, verify_data.order_number, verify_data.user_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found or invalid"
        )
    
    return {
        "valid": True,
        "order_number": order.order_number,
        "product_id": order.product_id,
        "quantity": order.quantity,
        "expire_date": order.expire_date,
        "status": order.status.value
    }

@router.get("/by-number/{order_number}", response_model=OrderResponse)
async def get_order_by_number(
    order_number: str,
    db: Session = Depends(get_db)
):
    """根据订单号获取订单"""
    order = OrderService.get_order_by_number(db, order_number)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )
    
    return order
