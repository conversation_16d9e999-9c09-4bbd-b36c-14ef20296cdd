from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from datetime import datetime, timedelta
from app.database import get_db
from app.utils.auth import get_current_agent, AuthUser
from app.services.agent_product_auth_service import AgentProductAuthService
from app.services.license_service import LicenseService
from app.services.order_service import OrderService
from app.models.license import License, LicenseStatus
from app.models.order import Order
from app.models.agent_product_auth import AgentProductAuth
from app.models.product import Product
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/stats")
async def get_agent_stats(
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取代理商统计数据"""
    try:
        agent_id = current_agent.user_id
        
        # 获取授权产品数量
        authorized_products = db.query(AgentProductAuth).filter(
            AgentProductAuth.agent_id == agent_id,
            AgentProductAuth.is_active == True
        ).count()
        
        # 获取总授权码数量
        total_licenses = db.query(License).filter(
            License.agent_id == agent_id
        ).count()
        
        # 获取激活的授权码数量
        active_licenses = db.query(License).filter(
            License.agent_id == agent_id,
            License.status == LicenseStatus.ACTIVE
        ).count()
        
        # 获取总订单数量
        total_orders = db.query(Order).filter(
            Order.agent_id == agent_id
        ).count()
        
        # 获取今日统计
        today = datetime.now().date()
        today_licenses = db.query(License).filter(
            License.agent_id == agent_id,
            License.created_at >= today
        ).count()
        
        today_orders = db.query(Order).filter(
            Order.agent_id == agent_id,
            Order.created_at >= today
        ).count()
        
        return {
            "authorized_products": authorized_products,
            "total_licenses": total_licenses,
            "active_licenses": active_licenses,
            "total_orders": total_orders,
            "today_licenses": today_licenses,
            "today_orders": today_orders
        }
        
    except Exception as e:
        logger.error(f"Error getting agent stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent statistics"
        )

@router.get("/authorized-products")
async def get_authorized_products(
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """获取代理商被授权的产品列表"""
    try:
        agent_id = current_agent.user_id
        
        # 获取代理商的产品授权
        auths = db.query(AgentProductAuth).join(Product).filter(
            AgentProductAuth.agent_id == agent_id,
            AgentProductAuth.is_active == True
        ).all()
        
        result = []
        for auth in auths:
            # 计算剩余授权数量
            remaining_licenses = auth.max_licenses - auth.used_licenses
            
            # 检查是否过期
            is_expired = False
            if auth.expire_date:
                is_expired = datetime.now() > auth.expire_date
            
            result.append({
                "id": auth.id,
                "product_id": auth.product_id,
                "product_name": auth.product.name if auth.product else "",
                "product_code": auth.product.code if auth.product else "",
                "max_licenses": auth.max_licenses,
                "used_licenses": auth.used_licenses,
                "remaining_licenses": remaining_licenses,
                "expire_date": auth.expire_date.isoformat() if auth.expire_date else None,
                "is_expired": is_expired,
                "is_active": auth.is_active,
                "created_at": auth.created_at.isoformat() if auth.created_at else None
            })
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting authorized products: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get authorized products"
        )

@router.get("/recent-activities")
async def get_recent_activities(
    limit: int = 10,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """获取代理商最近活动"""
    try:
        agent_id = current_agent.user_id
        activities = []
        
        # 获取最近的授权码生成记录
        recent_licenses = db.query(License).filter(
            License.agent_id == agent_id
        ).order_by(License.created_at.desc()).limit(limit // 2).all()
        
        for license in recent_licenses:
            activities.append({
                "id": f"license_{license.id}",
                "type": "license_created",
                "title": f"生成了授权码 {license.license_code}",
                "description": f"为产品 {license.product.name if license.product else '未知'} 生成授权码",
                "time": license.created_at.isoformat() if license.created_at else None
            })
        
        # 获取最近的订单记录
        recent_orders = db.query(Order).filter(
            Order.agent_id == agent_id
        ).order_by(Order.created_at.desc()).limit(limit // 2).all()
        
        for order in recent_orders:
            activities.append({
                "id": f"order_{order.id}",
                "type": "order_created",
                "title": f"创建了订单 {order.order_number}",
                "description": f"订单金额: ¥{order.total_amount}",
                "time": order.created_at.isoformat() if order.created_at else None
            })
        
        # 按时间排序
        activities.sort(key=lambda x: x["time"] or "", reverse=True)
        
        return activities[:limit]
        
    except Exception as e:
        logger.error(f"Error getting recent activities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get recent activities"
        )

@router.get("/licenses")
async def get_agent_licenses(
    skip: int = 0,
    limit: int = 100,
    status_filter: str = None,
    product_id: int = None,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取代理商的授权码列表"""
    try:
        agent_id = current_agent.user_id
        
        query = db.query(License).filter(License.agent_id == agent_id)
        
        # 状态过滤
        if status_filter:
            try:
                status_enum = LicenseStatus(status_filter)
                query = query.filter(License.status == status_enum)
            except ValueError:
                pass
        
        # 产品过滤
        if product_id:
            query = query.filter(License.product_id == product_id)
        
        total = query.count()
        licenses = query.order_by(License.created_at.desc()).offset(skip).limit(limit).all()
        
        result = []
        for license in licenses:
            result.append({
                "id": license.id,
                "license_code": license.license_code,
                "product_id": license.product_id,
                "product_name": license.product.name if license.product else "",
                "user_id": license.user_id,
                "max_api_calls": license.max_api_calls,
                "used_api_calls": license.used_api_calls,
                "expire_date": license.expire_date.isoformat() if license.expire_date else None,
                "status": license.status.value if license.status else None,
                "created_at": license.created_at.isoformat() if license.created_at else None,
                "activated_at": license.activated_at.isoformat() if license.activated_at else None
            })
        
        return {
            "total": total,
            "licenses": result
        }
        
    except Exception as e:
        logger.error(f"Error getting agent licenses: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent licenses"
        )
