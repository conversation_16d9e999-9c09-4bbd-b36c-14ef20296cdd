{% extends "admin/base.html" %}

{% block title %}订单管理 - FocuSee 管理系统{% endblock %}
{% block page_title %}订单管理{% endblock %}

{% block data %}
    orders: [
        {% for order in orders %}
        {
            id: {{ order.id }},
            order_number: "{{ order.order_number }}",
            product_id: {{ order.product_id or 'null' }},
            agent_id: {{ order.agent_id or 'null' }},
            product_name: "{{ order.product.name if order.product else '' }}",
            agent_name: "{{ order.agent.company_name if order.agent else '' }}",
            quantity: {{ order.quantity }},
            unit_price: {{ order.unit_price or 'null' }},
            total_price: {{ order.total_price or 'null' }},
            status: "{{ order.status.name if order.status else '' }}",
            customer_info: "{{ order.customer_info or '' }}",
            notes: "{{ order.notes or '' }}",
            expire_date: "{{ order.expire_date.isoformat() if order.expire_date else '' }}",
            created_at: "{{ order.created_at.isoformat() if order.created_at else '' }}",
            created_by_admin: {{ 'true' if order.created_by_admin else 'false' }}
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    products: [
        {% for product in products %}
        {
            id: {{ product.id }},
            name: "{{ product.name }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    agents: [
        {% for agent in agents %}
        {
            id: {{ agent.id }},
            company_name: "{{ agent.company_name }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    searchForm: {
        keyword: '',
        status: '',
        agent_id: null
    },
    currentPage: 1,
    pageSize: 20,
    total: 0,
    allOrders: [], // 保存所有订单数据
    sortField: 'created_at', // 默认按创建时间排序
    sortOrder: 'desc', // 默认降序

    dialogVisible: false,
    editMode: false,
    currentOrder: {
        order_number: '',
        product_id: null,
        agent_id: null,
        quantity: 1,
        unit_price: null,
        total_price: null,
        status: 'PENDING',
        customer_info: '',
        notes: '',
        expire_date: null
    },
    orderForm: {
        order_number: '',
        product_id: null,
        agent_id: null,
        quantity: 1,
        unit_price: null,
        total_price: null,
        status: 'PENDING',
        customer_info: '',
        notes: '',
        expire_date: null
    },
    rules: {
        product_id: [
            { required: true, message: '请选择产品', trigger: 'change' }
        ],
        quantity: [
            { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        expire_date: [
            { required: true, message: '请选择过期时间', trigger: 'change' }
        ]
    },
    loading: false
{% endblock %}

{% block content %}
<div class="content-card">
    <!-- 查询栏 -->
    <div style="margin-bottom: 20px;">
        <el-row :gutter="20">
            <el-col :span="6">
                <el-input
                    v-model="searchForm.keyword"
                    placeholder="搜索订单号、客户信息"
                    clearable
                    @keyup.enter="searchOrders">
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
            </el-col>
            <el-col :span="4">
                <el-select v-model="searchForm.status" placeholder="订单状态" clearable>
                    <el-option label="待处理" value="PENDING"></el-option>
                    <el-option label="已确认" value="CONFIRMED"></el-option>
                    <el-option label="已完成" value="COMPLETED"></el-option>
                    <el-option label="已取消" value="CANCELLED"></el-option>
                    <el-option label="已过期" value="EXPIRED"></el-option>
                </el-select>
            </el-col>
            <el-col :span="4">
                <el-select v-model="searchForm.agent_id" placeholder="代理商" clearable>
                    <el-option label="管理员创建" :value="null"></el-option>
                    <el-option
                        v-for="agent in agents"
                        :key="agent.id"
                        :label="agent.company_name"
                        :value="agent.id">
                    </el-option>
                </el-select>
            </el-col>
            <el-col :span="6">
                <el-button type="primary" @click="searchOrders">
                    <el-icon><Search /></el-icon>
                    查询
                </el-button>
                <el-button @click="resetSearch">
                    <el-icon><Refresh /></el-icon>
                    重置
                </el-button>
            </el-col>
            <el-col :span="4" style="text-align: right;">
                <el-button type="primary" @click="openAddDialog">
                    <el-icon><Plus /></el-icon>
                    创建订单
                </el-button>
            </el-col>
        </el-row>
    </div>

    <!-- 订单表格 -->
    <el-table
        :data="orders"
        style="width: 100%"
        v-loading="loading"
        @sort-change="handleSortChange"
        :default-sort="{prop: 'created_at', order: 'descending'}">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="order_number" label="订单号" width="180"></el-table-column>
        <el-table-column prop="product_name" label="产品" min-width="120"></el-table-column>
        <el-table-column prop="agent_name" label="代理商" min-width="120">
            <template #default="scope">
                {% raw %}{{ scope.row.agent_name || '管理员直接创建' }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="80"></el-table-column>
        <el-table-column prop="total_price" label="总价" width="100">
            <template #default="scope">
                <span v-if="scope.row.total_price">¥{% raw %}{{ scope.row.total_price }}{% endraw %}</span>
                <span v-else>-</span>
            </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                    {% raw %}{{ getStatusText(scope.row.status) }}{% endraw %}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="expire_date" label="过期时间" width="180" sortable="custom" sort-by="expire_date">
            <template #default="scope">
                {% raw %}{{ formatTime(scope.row.expire_date) }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom" sort-by="created_at">
            <template #default="scope">
                {% raw %}{{ formatTime(scope.row.created_at) }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
            <template #default="scope">
                <el-button size="small" @click="editOrder(scope.row)">编辑</el-button>
                <el-button
                    size="small"
                    type="warning"
                    v-if="scope.row.status === 'PENDING'"
                    @click="confirmOrder(scope.row)">
                    确认
                </el-button>
                <el-button
                    size="small"
                    type="danger"
                    v-if="['PENDING', 'CONFIRMED'].includes(scope.row.status)"
                    @click="cancelOrder(scope.row)">
                    取消
                </el-button>
            </template>
        </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div style="margin-top: 20px; text-align: right;">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
        </el-pagination>
    </div>
</div>
<!-- 添加/编辑订单对话框 -->
<el-dialog
    :title="editMode ? '编辑订单' : '创建订单'"
    v-model="dialogVisible"
    width="700px">
    <el-form ref="orderFormRef" :model="orderForm" :rules="rules" label-width="120px">
        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="订单号">
                    <el-input v-model="orderForm.order_number" placeholder="留空自动生成" :disabled="editMode"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="产品" prop="product_id">
                    <el-select v-model="orderForm.product_id" placeholder="请选择产品" style="width: 100%">
                        <el-option
                            v-for="product in products"
                            :key="product.id"
                            :label="product.name"
                            :value="product.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="代理商">
                    <el-select v-model="orderForm.agent_id" placeholder="选择代理商（可选）" style="width: 100%" clearable>
                        <el-option
                            v-for="agent in agents"
                            :key="agent.id"
                            :label="agent.company_name"
                            :value="agent.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="数量" prop="quantity">
                    <el-input-number v-model="orderForm.quantity" :min="1" style="width: 100%"></el-input-number>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="单价">
                    <el-input-number v-model="orderForm.unit_price" :precision="2" :min="0" style="width: 100%"></el-input-number>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="总价">
                    <el-input-number v-model="orderForm.total_price" :precision="2" :min="0" style="width: 100%"></el-input-number>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="状态">
                    <el-select v-model="orderForm.status" style="width: 100%">
                        <el-option label="待处理" value="PENDING"></el-option>
                        <el-option label="已确认" value="CONFIRMED"></el-option>
                        <el-option label="已完成" value="COMPLETED"></el-option>
                        <el-option label="已取消" value="CANCELLED"></el-option>
                        <el-option label="已过期" value="EXPIRED"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="过期时间" prop="expire_date">
                    <el-date-picker
                        v-model="orderForm.expire_date"
                        type="datetime"
                        placeholder="选择过期时间"
                        style="width: 100%"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        :default-time="new Date()"
                        :disabled-date="disabledDate">
                    </el-date-picker>
                </el-form-item>
            </el-col>
        </el-row>

        <el-form-item label="客户信息">
            <el-input type="textarea" v-model="orderForm.customer_info" placeholder="请输入客户信息" :rows="3"></el-input>
        </el-form-item>

        <el-form-item label="备注">
            <el-input type="textarea" v-model="orderForm.notes" placeholder="请输入备注" :rows="2"></el-input>
        </el-form-item>
    </el-form>

    <template #footer>
        <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveOrder">确定</el-button>
        </span>
    </template>
</el-dialog>
{% endblock %}

{% block methods %}
formatTime(timeStr) {
    if (!timeStr) return '-';
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
},
getStatusType(status) {
    const statusMap = {
        'PENDING': 'warning',
        'CONFIRMED': 'primary',
        'COMPLETED': 'success',
        'CANCELLED': 'danger',
        'EXPIRED': 'info'
    };
    return statusMap[status] || 'info';
},
getStatusText(status) {
    const statusMap = {
        'PENDING': '待处理',
        'CONFIRMED': '已确认',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消',
        'EXPIRED': '已过期'
    };
    return statusMap[status] || status;
},
openAddDialog() {
    this.editMode = false;
    this.orderForm = {
        order_number: '',
        product_id: null,
        agent_id: null,
        quantity: 1,
        unit_price: null,
        total_price: null,
        status: 'PENDING',
        customer_info: '',
        notes: '',
        expire_date: null
    };
    this.dialogVisible = true;
},
editOrder(order) {
    console.log('Editing order:', order);
    console.log('Order product_id:', order.product_id, 'Type:', typeof order.product_id);
    console.log('Order agent_id:', order.agent_id, 'Type:', typeof order.agent_id);
    console.log('Order expire_date:', order.expire_date, 'Type:', typeof order.expire_date);

    this.editMode = true;
    this.currentOrder = order;

    // 处理过期时间格式
    let expireDate = null;
    if (order.expire_date) {
        // 如果有过期时间，转换为日期选择器需要的格式
        const date = new Date(order.expire_date);
        // 转换为 YYYY-MM-DD HH:mm:ss 格式
        expireDate = date.getFullYear() + '-' +
                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0') + ' ' +
                   String(date.getHours()).padStart(2, '0') + ':' +
                   String(date.getMinutes()).padStart(2, '0') + ':' +
                   String(date.getSeconds()).padStart(2, '0');
    }

    this.orderForm = {
        order_number: order.order_number,
        product_id: order.product_id || null,
        agent_id: order.agent_id || null,
        quantity: order.quantity,
        unit_price: order.unit_price,
        total_price: order.total_price,
        status: order.status,
        customer_info: order.customer_info || '',
        notes: order.notes || '',
        expire_date: expireDate
    };

    console.log('OrderForm after setting:', this.orderForm);
    console.log('Formatted expire_date:', expireDate);
    console.log('Products available:', this.products);
    console.log('Agents available:', this.agents);

    this.dialogVisible = true;

    // 确保表单重新渲染
    this.$nextTick(() => {
        if (this.$refs.orderFormRef) {
            this.$refs.orderFormRef.clearValidate();
        }
    });
},
saveOrder() {
    this.$refs.orderFormRef.validate((valid) => {
        if (valid) {
            const url = this.editMode ? `/api/orders/${this.currentOrder.id}` : '/api/orders';
            const method = this.editMode ? 'PUT' : 'POST';

            // 准备订单数据
            const orderData = {
                ...this.orderForm,
                created_by_admin: true
            };

            // 处理过期时间 - 确保使用中国时区
            if (orderData.expire_date) {
                if (typeof orderData.expire_date === 'string') {
                    // 如果是字符串，直接使用
                    orderData.expire_date = orderData.expire_date;
                } else {
                    // 如果是Date对象，转换为ISO字符串
                    orderData.expire_date = orderData.expire_date.toISOString();
                }
            }

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => Promise.reject(err));
                }
                return response.json();
            })
            .then(data => {
                ElMessage.success(this.editMode ? '订单更新成功' : '订单创建成功');
                this.dialogVisible = false;
                this.refreshOrders();
            })
            .catch(error => {
                console.error('Error saving order:', error);
                ElMessage.error('操作失败：' + (error.detail || error.message || '未知错误'));
            });
        }
    });
},
confirmOrder(order) {
    ElMessageBox.confirm(`确定要确认订单 ${order.order_number} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        fetch(`/api/orders/${order.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: 'CONFIRMED'
            })
        })
        .then(response => response.json())
        .then(data => {
            ElMessage.success('订单确认成功');
            this.refreshOrders();
        })
        .catch(error => {
            ElMessage.error('操作失败：' + error.message);
        });
    });
},
cancelOrder(order) {
    ElMessageBox.confirm(`确定要取消订单 ${order.order_number} 吗？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        fetch(`/api/orders/${order.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: 'CANCELLED'
            })
        })
        .then(response => response.json())
        .then(data => {
            ElMessage.success('订单取消成功');
            this.refreshOrders();
        })
        .catch(error => {
            ElMessage.error('操作失败：' + error.message);
        });
    });
},
// 搜索和分页相关方法
initializeData() {
    // 将模板中的订单数据保存到allOrders
    this.allOrders = [...this.orders];
    this.total = this.allOrders.length;
    this.applyFiltersAndPagination();
},
applyFiltersAndPagination() {
    let filteredOrders = [...this.allOrders];

    // 应用搜索过滤
    if (this.searchForm.keyword) {
        const keyword = this.searchForm.keyword.toLowerCase();
        filteredOrders = filteredOrders.filter(order =>
            order.order_number.toLowerCase().includes(keyword) ||
            (order.customer_info && order.customer_info.toLowerCase().includes(keyword)) ||
            (order.notes && order.notes.toLowerCase().includes(keyword))
        );
    }

    // 应用状态过滤
    if (this.searchForm.status) {
        filteredOrders = filteredOrders.filter(order => order.status === this.searchForm.status);
    }

    // 应用代理商过滤
    if (this.searchForm.agent_id !== null && this.searchForm.agent_id !== '') {
        filteredOrders = filteredOrders.filter(order => order.agent_id === this.searchForm.agent_id);
    }

    // 应用排序
    filteredOrders.sort((a, b) => {
        let aValue = a[this.sortField];
        let bValue = b[this.sortField];

        // 处理时间字段
        if (this.sortField === 'created_at' || this.sortField === 'expire_date') {
            aValue = aValue ? new Date(aValue).getTime() : 0;
            bValue = bValue ? new Date(bValue).getTime() : 0;
        }

        // 处理空值
        if (!aValue && !bValue) return 0;
        if (!aValue) return this.sortOrder === 'asc' ? -1 : 1;
        if (!bValue) return this.sortOrder === 'asc' ? 1 : -1;

        // 比较值
        if (aValue < bValue) return this.sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return this.sortOrder === 'asc' ? 1 : -1;
        return 0;
    });

    // 更新总数
    this.total = filteredOrders.length;

    // 应用分页
    const start = (this.currentPage - 1) * this.pageSize;
    const end = start + this.pageSize;
    this.orders = filteredOrders.slice(start, end);
},
searchOrders() {
    this.currentPage = 1;
    this.applyFiltersAndPagination();
},
resetSearch() {
    this.searchForm = {
        keyword: '',
        status: '',
        agent_id: null
    };
    this.currentPage = 1;
    this.sortField = 'created_at';
    this.sortOrder = 'desc';
    this.applyFiltersAndPagination();
},
handleSizeChange(newSize) {
    this.pageSize = newSize;
    this.currentPage = 1;
    this.applyFiltersAndPagination();
},
handleCurrentChange(newPage) {
    this.currentPage = newPage;
    this.applyFiltersAndPagination();
},
handleSortChange({ column, prop, order }) {
    if (prop && (prop === 'created_at' || prop === 'expire_date')) {
        this.sortField = prop;
        this.sortOrder = order === 'ascending' ? 'asc' : 'desc';
        this.applyFiltersAndPagination();
    }
},
refreshOrders() {
    this.loading = true;
    window.location.reload();
},
// 时间相关方法
disabledDate(time) {
    // 禁用过去的日期
    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
},
formatTime(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    // 转换为中国时区显示
    return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}
{% endblock %}

{% block mounted %}
// 初始化数据
this.initializeData();
{% endblock %}
