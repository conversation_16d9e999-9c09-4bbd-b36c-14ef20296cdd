"""
密码加密和验证工具模块
"""
from passlib.context import CryptContext
import logging

logger = logging.getLogger(__name__)

# 创建密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """
    对密码进行哈希加密

    Args:
        password: 明文密码

    Returns:
        str: 加密后的密码哈希
    """
    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error(f"Error hashing password: {str(e)}")
        raise

def verify_password(password: str, hashed_password: str) -> bool:
    """
    验证密码是否正确

    Args:
        password: 明文密码
        hashed_password: 加密后的密码哈希

    Returns:
        bool: 密码是否正确
    """
    try:
        return pwd_context.verify(password, hashed_password)
    except Exception as e:
        logger.error(f"Error verifying password: {str(e)}")
        return False

def generate_random_password(length: int = 12) -> str:
    """
    生成随机密码
    
    Args:
        length: 密码长度，默认12位
        
    Returns:
        str: 随机生成的密码
    """
    import secrets
    import string
    
    # 定义密码字符集
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    
    # 确保密码包含至少一个大写字母、小写字母、数字和特殊字符
    password = [
        secrets.choice(string.ascii_lowercase),
        secrets.choice(string.ascii_uppercase),
        secrets.choice(string.digits),
        secrets.choice("!@#$%^&*")
    ]
    
    # 填充剩余长度
    for _ in range(length - 4):
        password.append(secrets.choice(alphabet))
    
    # 打乱顺序
    secrets.SystemRandom().shuffle(password)
    
    return ''.join(password)

def is_strong_password(password: str) -> tuple[bool, list[str]]:
    """
    检查密码强度
    
    Args:
        password: 要检查的密码
        
    Returns:
        tuple: (是否强密码, 错误信息列表)
    """
    errors = []
    
    # 检查长度
    if len(password) < 8:
        errors.append("密码长度至少8位")
    
    # 检查是否包含大写字母
    if not any(c.isupper() for c in password):
        errors.append("密码必须包含至少一个大写字母")
    
    # 检查是否包含小写字母
    if not any(c.islower() for c in password):
        errors.append("密码必须包含至少一个小写字母")
    
    # 检查是否包含数字
    if not any(c.isdigit() for c in password):
        errors.append("密码必须包含至少一个数字")
    
    # 检查是否包含特殊字符
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    if not any(c in special_chars for c in password):
        errors.append("密码必须包含至少一个特殊字符")
    
    return len(errors) == 0, errors
