from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from app.database import get_db
from app.schemas.activation_algorithm import (
    ActivationAlgorithmCreate, 
    ActivationAlgorithmUpdate, 
    ActivationAlgorithmResponse,
    ActivationAlgorithmListResponse,
    ActivationAlgorithmSimple
)
from app.services.activation_algorithm_service import ActivationAlgorithmService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/", response_model=ActivationAlgorithmResponse)
async def create_algorithm(
    algorithm_data: ActivationAlgorithmCreate,
    db: Session = Depends(get_db)
):
    """创建新算法"""
    try:
        service = ActivationAlgorithmService()
        
        # 验证算法代码
        validation = service.validate_algorithm_code(algorithm_data.algorithm_code)
        if not validation["valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"算法代码验证失败: {validation['error']}"
            )
        
        algorithm = service.create_algorithm(db=db, algorithm_data=algorithm_data)
        
        if not algorithm:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="算法创建失败"
            )
        
        return algorithm
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建算法失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/", response_model=ActivationAlgorithmListResponse)
async def get_algorithms(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    name: Optional[str] = Query(None, description="算法名称筛选"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    db: Session = Depends(get_db)
):
    """获取算法列表"""
    try:
        service = ActivationAlgorithmService()
        result = service.get_algorithms(
            db=db,
            skip=skip,
            limit=limit,
            name=name,
            is_active=is_active
        )
        
        return result
        
    except Exception as e:
        logger.error(f"获取算法列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/active", response_model=List[ActivationAlgorithmSimple])
async def get_active_algorithms(db: Session = Depends(get_db)):
    """获取所有启用的算法（用于选择器）"""
    try:
        service = ActivationAlgorithmService()
        algorithms = service.get_active_algorithms(db=db)
        
        return algorithms
        
    except Exception as e:
        logger.error(f"获取启用算法失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/{algorithm_id}", response_model=ActivationAlgorithmResponse)
async def get_algorithm(
    algorithm_id: int,
    db: Session = Depends(get_db)
):
    """获取单个算法详情"""
    try:
        service = ActivationAlgorithmService()
        algorithm = service.get_algorithm_by_id(db=db, algorithm_id=algorithm_id)
        
        if not algorithm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="算法不存在"
            )
        
        return algorithm
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取算法详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.put("/{algorithm_id}", response_model=ActivationAlgorithmResponse)
async def update_algorithm(
    algorithm_id: int,
    algorithm_data: ActivationAlgorithmUpdate,
    db: Session = Depends(get_db)
):
    """更新算法"""
    try:
        service = ActivationAlgorithmService()
        
        # 如果更新了算法代码，需要验证
        if algorithm_data.algorithm_code:
            validation = service.validate_algorithm_code(algorithm_data.algorithm_code)
            if not validation["valid"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"算法代码验证失败: {validation['error']}"
                )
        
        algorithm = service.update_algorithm(
            db=db, 
            algorithm_id=algorithm_id, 
            algorithm_data=algorithm_data
        )
        
        if not algorithm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="算法不存在"
            )
        
        return algorithm
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新算法失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.delete("/{algorithm_id}")
async def delete_algorithm(
    algorithm_id: int,
    db: Session = Depends(get_db)
):
    """删除算法"""
    try:
        service = ActivationAlgorithmService()
        success = service.delete_algorithm(db=db, algorithm_id=algorithm_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="删除失败，可能是默认算法或算法不存在"
            )
        
        return {"message": "算法删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除算法失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.post("/validate")
async def validate_algorithm_code(
    algorithm_code: str,
    db: Session = Depends(get_db)
):
    """验证算法代码"""
    try:
        service = ActivationAlgorithmService()
        result = service.validate_algorithm_code(algorithm_code)
        
        return result
        
    except Exception as e:
        logger.error(f"验证算法代码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.post("/test")
async def test_algorithm(
    algorithm_id: int,
    machine_id: str = "TEST123456",
    days: int = 30,
    db: Session = Depends(get_db)
):
    """测试算法"""
    try:
        service = ActivationAlgorithmService()
        algorithm = service.get_algorithm_by_id(db=db, algorithm_id=algorithm_id)
        
        if not algorithm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="算法不存在"
            )
        
        if not algorithm.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="算法未启用"
            )
        
        activation_code = service.execute_algorithm(
            algorithm=algorithm,
            machine_id=machine_id,
            days=days
        )
        
        if not activation_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="算法执行失败"
            )
        
        return {
            "algorithm_name": algorithm.name,
            "machine_id": machine_id,
            "days": days,
            "activation_code": activation_code
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试算法失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )
