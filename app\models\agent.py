from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.sql import func
from app.database import Base

class Agent(Base):
    __tablename__ = "agents"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False, comment="代理商用户名")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    company_name = Column(String(100), nullable=False, comment="公司名称")
    contact_name = Column(String(50), comment="联系人姓名")
    contact_email = Column(String(100), comment="联系邮箱")
    contact_phone = Column(String(20), comment="联系电话")
    address = Column(Text, comment="公司地址")
    description = Column(Text, comment="代理商描述")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<Agent(username='{self.username}', company='{self.company_name}', is_active={self.is_active})>"
