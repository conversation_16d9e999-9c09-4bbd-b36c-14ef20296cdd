from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class AgentBase(BaseModel):
    username: str
    company_name: str
    contact_name: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None
    address: Optional[str] = None
    description: Optional[str] = None

class AgentCreate(AgentBase):
    password: str
    is_active: bool = True

class AgentUpdate(BaseModel):
    username: Optional[str] = None
    company_name: Optional[str] = None
    contact_name: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None
    address: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

class AgentPasswordUpdate(BaseModel):
    old_password: str
    new_password: str

class AgentResponse(AgentBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class AgentLogin(BaseModel):
    username: str
    password: str
