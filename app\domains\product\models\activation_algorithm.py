from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.sql import func
from app.database import Base

class ActivationAlgorithm(Base):
    """激活码算法管理表"""
    __tablename__ = "activation_algorithms"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, comment="算法名称")
    description = Column(Text, comment="算法描述")
    algorithm_code = Column(Text, nullable=False, comment="算法代码")
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_default = Column(Boolean, default=False, comment="是否为默认算法")
    version = Column(String(20), default="1.0", comment="算法版本")
    author = Column(String(100), comment="算法作者")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<ActivationAlgorithm(name='{self.name}', version='{self.version}', is_active={self.is_active})>"
