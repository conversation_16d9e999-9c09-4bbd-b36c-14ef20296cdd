#!/usr/bin/env python3
"""
创建支付调试相关表
"""

from sqlalchemy import create_engine
from app.config import settings
from app.models.payment_debug import PaymentDebug, PaymentConfig
from app.database import Base

def create_payment_debug_tables():
    """创建支付调试相关表"""
    print("🔧 创建支付调试相关表...")
    
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        # 创建表
        PaymentDebug.__table__.create(engine, checkfirst=True)
        PaymentConfig.__table__.create(engine, checkfirst=True)
        
        print("✅ 支付调试表创建成功")
        print("   - payment_debug: 支付调试记录表")
        print("   - payment_config: 支付配置表")
        
        # 插入默认配置
        from sqlalchemy.orm import sessionmaker
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # 检查是否已有配置
            existing_config = session.query(PaymentConfig).first()
            if not existing_config:
                # 创建默认沙箱配置
                default_config = PaymentConfig(
                    config_name="默认沙箱配置",
                    config_type="alipay",
                    app_id="9021000150602505",
                    app_private_key="请填入您的应用私钥",
                    alipay_public_key="请填入支付宝公钥",
                    gateway_url="https://openapi-sandbox.dl.alipaydev.com/gateway.do",
                    notify_url="http://localhost:8008/api/payment/alipay/notify",
                    return_url="http://localhost:8008/payment/return",
                    environment="sandbox",
                    description="默认的支付宝沙箱环境配置，请根据实际情况修改",
                    is_active=True
                )
                
                session.add(default_config)
                session.commit()
                print("✅ 默认配置已创建")
            else:
                print("ℹ️  配置已存在，跳过创建默认配置")
                
        except Exception as e:
            print(f"⚠️  创建默认配置失败: {str(e)}")
            session.rollback()
        finally:
            session.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 创建支付调试表失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 支付调试表创建工具")
    print("=" * 50)
    
    success = create_payment_debug_tables()
    
    if success:
        print("\n🎉 支付调试功能已准备就绪！")
        print("\n📋 使用说明:")
        print("1. 访问 http://localhost:8008/admin/payment-debug 进入调试页面")
        print("2. 在配置管理中设置正确的支付宝参数")
        print("3. 创建调试支付进行测试")
        print("4. 实时查询支付状态")
        
        print("\n⚠️  注意事项:")
        print("- 请在配置管理中填入正确的支付宝密钥")
        print("- 沙箱环境用于测试，生产环境需要真实商户信息")
        print("- 确保回调地址可以被支付宝访问到")
        
    else:
        print("\n❌ 创建失败，请检查数据库连接和权限")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
