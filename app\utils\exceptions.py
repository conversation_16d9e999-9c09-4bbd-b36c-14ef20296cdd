from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)

class FocuSeeException(Exception):
    """自定义异常基类"""
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class UserNotFoundError(FocuSeeException):
    """用户未找到异常"""
    def __init__(self, user_id: str):
        super().__init__(f"User {user_id} not found", 404)

class InvalidCredentialsError(FocuSeeException):
    """无效凭证异常"""
    def __init__(self):
        super().__init__("Invalid user ID or license key", 401)

class FileNotFoundError(FocuSeeException):
    """文件未找到异常"""
    def __init__(self, filename: str):
        super().__init__(f"File {filename} not found", 404)

class PermissionDeniedError(FocuSeeException):
    """权限拒绝异常"""
    def __init__(self, message: str = "Permission denied"):
        super().__init__(message, 403)

async def focusee_exception_handler(request: Request, exc: FocuSeeException):
    """自定义异常处理器"""
    logger.error(f"FocuSee Exception: {exc.message}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"status": "error", "message": exc.message}
    )

async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"status": "error", "message": "Internal server error"}
    )
