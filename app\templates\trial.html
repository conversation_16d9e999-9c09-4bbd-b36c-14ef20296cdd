<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FocuSee 一个月免费试用 | 获取专业版授权码</title>
    <meta name="description" content="下载 FocuSee 并输入激活码，即可获得价值 ¥299 的一个月免费专业版许可证。体验完整的专业功能，无水印录制。">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .hero-bg {
            background: linear-gradient(135deg, #252b3c 0%, #6a5b4b 100%);
        }
        .step-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .activation-code {
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            font-weight: bold;
            letter-spacing: 2px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            user-select: text;
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
        }
        .select-text {
            user-select: text !important;
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
        }
    </style>
</head>
<body class="bg-white">
    <!-- 导航栏 -->
    <nav class="bg-black shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <a href="/">
                            <img class="h-8 w-auto" src="https://focusee.imobie-resource.com/img/focusee_logo_index.svg" alt="FocuSee">
                        </a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-white hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                        返回首页
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-bg pt-20 pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="mb-8">
                <span class="inline-flex items-center px-6 py-3 rounded-full text-lg font-medium bg-green-500 text-white backdrop-blur-sm pulse-animation">
                    <i class="fas fa-gift mr-3"></i>
                    🎁 免费获取一个月专业版许可证 - 价值 ¥299
                </span>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                下载并激活，获得<br>
                <span class="text-yellow-300">一个月免费试用</span>
            </h1>
            <p class="text-xl text-white text-opacity-90 mb-8 max-w-3xl mx-auto">
                FocuSee Desktop，一款具有 AI 自动编辑功能的屏幕录制工具。
                下载并输入激活码，即可获得一个月免费许可证。
            </p>
        </div>
    </section>

    <!-- 步骤说明 -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    三步获取免费试用
                </h2>
                <p class="text-xl text-gray-600">
                    简单三步，立即体验专业版完整功能
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- 步骤 1 -->
                <div class="step-card bg-white rounded-xl p-8 shadow-lg text-center">
                    <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-3xl font-bold text-blue-600">1</span>
                    </div>
                    <div class="mb-6">
                        <img src="https://focusee.imobie-resource.com/img/invite-campaign-img1.png" 
                             alt="下载 FocuSee" 
                             class="w-full max-w-xs mx-auto rounded-lg shadow-md">
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">下载</h3>
                    <p class="text-gray-600 mb-6">
                        点击下载，安装并启动 FocuSee
                    </p>
                    <a href="https://focusee.imobie.com/go/download.php?product=fs&id=0" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                        <i class="fas fa-download mr-2"></i>
                        免费下载
                    </a>
                </div>

                <!-- 步骤 2 -->
                <div class="step-card bg-white rounded-xl p-8 shadow-lg text-center">
                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-3xl font-bold text-green-600">2</span>
                    </div>
                    <div class="mb-6">
                        <img src="https://focusee.imobie-resource.com/img/invite-campaign-img2.png" 
                             alt="复制激活码" 
                             class="w-full max-w-xs mx-auto rounded-lg shadow-md">
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">复制激活码</h3>
                    <p class="text-gray-600 mb-6">
                        获取您的专属激活码
                    </p>
                    <div id="activation-section">
                        <button onclick="getActivationCode()" 
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition duration-300">
                            <i class="fas fa-key mr-2"></i>
                            获取激活码
                        </button>
                        <div id="activation-code-display" class="hidden mt-4">
                            <div class="bg-gray-100 p-4 rounded-lg border-2 border-dashed border-gray-300">
                                <p class="text-sm text-gray-600 mb-2">您的激活码：</p>
                                <div class="activation-code bg-white p-3 rounded border border-gray-300 mb-3 select-text cursor-text"
                                     id="activation-code-text"
                                     title="点击选中激活码">
                                    正在获取...
                                </div>
                                <div class="flex gap-2">
                                    <button onclick="copyActivationCode()"
                                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm transition duration-300 flex-1">
                                        <i class="fas fa-copy mr-1"></i>
                                        复制
                                    </button>
                                    <button onclick="selectActivationCode()"
                                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm transition duration-300">
                                        <i class="fas fa-mouse-pointer mr-1"></i>
                                        选中
                                    </button>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">
                                请下载最新版本的应用程序以使用激活码。如果复制按钮无效，请点击"选中"按钮后使用 Ctrl+C 复制。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 步骤 3 -->
                <div class="step-card bg-white rounded-xl p-8 shadow-lg text-center">
                    <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-3xl font-bold text-purple-600">3</span>
                    </div>
                    <div class="mb-6">
                        <div class="w-full max-w-xs mx-auto bg-gray-100 rounded-lg p-6">
                            <i class="fas fa-user-circle text-6xl text-gray-400 mb-4"></i>
                            <div class="text-sm text-gray-600">
                                <div class="mb-2">1. 点击右上角账户图标</div>
                                <div class="mb-2">2. 用常用邮箱注册</div>
                                <div>3. 输入激活码</div>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">激活</h3>
                    <p class="text-gray-600">
                        输入激活码，获得价值 ¥299 的一个月许可证
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- 激活说明 -->
    <section class="py-20 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    如何激活
                </h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-download text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">下载</h3>
                    <p class="text-gray-600">点击下载，安装并启动 FocuSee</p>
                </div>
                <div>
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user-plus text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">注册</h3>
                    <p class="text-gray-600">点击右上角账户图标，用常用邮箱注册</p>
                </div>
                <div>
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-key text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">激活</h3>
                    <p class="text-gray-600">输入激活码，获得一个月许可证</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-gray-900 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-gray-400 text-sm">
                    Copyright © 2011 - 2025 iMobie Inc. All rights reserved. 
                    <a href="https://www.imobie.com/company/privacy.htm?ref=footer" class="hover:text-white transition duration-300">隐私政策</a> | 
                    <a href="https://www.imobie.com/company/end-user-license-agreement.htm?ref=footer" class="hover:text-white transition duration-300">最终用户许可协议</a> | 
                    <a href="https://www.imobie.com/company/terms.htm?ref=footer" class="hover:text-white transition duration-300">使用条款</a>
                </p>
            </div>
        </div>
    </footer>

    <script>
        let activationCode = '';

        async function getActivationCode() {
            const button = document.querySelector('#activation-section button');
            const display = document.getElementById('activation-code-display');
            const codeText = document.getElementById('activation-code-text');
            
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>获取中...';
            
            try {
                const response = await fetch('https://anytrans.imobie.com/api/focusee/invitation/code?sequence=ee2f4b8704807032c7c0a15f6371f19a');

                if (response.ok) {
                    const data = await response.json();

                    if (data.code === 200 && data.data && data.data.code) {
                        activationCode = data.data.code;
                        codeText.textContent = activationCode;
                        display.classList.remove('hidden');
                        button.style.display = 'none';
                    } else {
                        codeText.textContent = '暂无可用激活码';
                        codeText.className = 'text-red-500 font-semibold';
                        display.classList.remove('hidden');
                        button.innerHTML = '<i class="fas fa-key mr-2"></i>重新获取';
                        button.disabled = false;
                    }
                } else {
                    throw new Error('获取失败');
                }
            } catch (error) {
                console.error('获取激活码失败:', error);
                button.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>获取失败，请重试';
                button.disabled = false;
                
                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-key mr-2"></i>获取激活码';
                }, 3000);
            }
        }

        function copyActivationCode() {
            if (activationCode) {
                // 尝试使用现代的 Clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(activationCode).then(() => {
                        showCopySuccess();
                    }).catch(err => {
                        console.error('Clipboard API 复制失败:', err);
                        fallbackCopy();
                    });
                } else {
                    // 降级到传统方法
                    fallbackCopy();
                }
            }
        }

        function fallbackCopy() {
            try {
                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = activationCode;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                // 尝试复制
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);

                if (successful) {
                    showCopySuccess();
                } else {
                    showCopyError();
                }
            } catch (err) {
                console.error('传统复制方法失败:', err);
                showCopyError();
            }
        }

        function showCopySuccess() {
            const button = document.querySelector('#activation-code-display button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check mr-1"></i>已复制';
            button.classList.remove('bg-blue-500', 'hover:bg-blue-600');
            button.classList.add('bg-green-500');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-500');
                button.classList.add('bg-blue-500', 'hover:bg-blue-600');
            }, 2000);
        }

        function showCopyError() {
            // 显示手动复制提示
            const codeText = document.getElementById('activation-code-text');
            codeText.style.userSelect = 'text';
            codeText.style.cursor = 'text';

            // 选中文本
            const range = document.createRange();
            range.selectNode(codeText);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            // 显示提示
            const button = document.querySelector('#activation-code-display button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>请手动复制';
            button.classList.remove('bg-blue-500', 'hover:bg-blue-600');
            button.classList.add('bg-yellow-500');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-yellow-500');
                button.classList.add('bg-blue-500', 'hover:bg-blue-600');
                selection.removeAllRanges();
            }, 3000);

            alert('自动复制失败，激活码已选中，请使用 Ctrl+C (Windows) 或 Cmd+C (Mac) 手动复制');
        }

        function selectActivationCode() {
            const codeText = document.getElementById('activation-code-text');
            if (codeText && activationCode) {
                // 选中文本
                const range = document.createRange();
                range.selectNode(codeText);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);

                // 显示提示
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check mr-1"></i>已选中';
                button.classList.remove('bg-gray-500', 'hover:bg-gray-600');
                button.classList.add('bg-green-500');

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('bg-green-500');
                    button.classList.add('bg-gray-500', 'hover:bg-gray-600');
                }, 2000);
            }
        }
    </script>
</body>
</html>
