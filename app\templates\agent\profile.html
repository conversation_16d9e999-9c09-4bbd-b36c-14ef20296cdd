{% extends "agent/base.html" %}

{% block title %}个人设置{% endblock %}
{% block page_title %}个人设置{% endblock %}

{% block content %}
<div>
    <el-row :gutter="20">
        <!-- 基本信息 -->
        <el-col :span="12">
            <el-card>
                <template #header>
                    <span>基本信息</span>
                </template>
                
                <el-form :model="profileForm" label-width="100px" disabled>
                    <el-form-item label="用户名">
                        <el-input v-model="profileForm.username"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="公司名称">
                        <el-input v-model="profileForm.company_name"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="联系人">
                        <el-input v-model="profileForm.contact_name"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="联系邮箱">
                        <el-input v-model="profileForm.contact_email"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="联系电话">
                        <el-input v-model="profileForm.contact_phone"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="公司地址">
                        <el-input v-model="profileForm.address" type="textarea" :rows="3"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="创建时间">
                        <el-input :value="formatDate(profileForm.created_at)" disabled></el-input>
                    </el-form-item>
                </el-form>
                
                <div style="text-align: center; margin-top: 20px;">
                    <el-alert 
                        title="基本信息由管理员管理，如需修改请联系管理员" 
                        type="info" 
                        :closable="false">
                    </el-alert>
                </div>
            </el-card>
        </el-col>
        
        <!-- 修改密码 -->
        <el-col :span="12">
            <el-card>
                <template #header>
                    <span>修改密码</span>
                </template>
                
                <el-form 
                    :model="passwordForm" 
                    :rules="passwordRules" 
                    ref="passwordFormRef" 
                    label-width="100px">
                    
                    <el-form-item label="当前密码" prop="old_password">
                        <el-input 
                            v-model="passwordForm.old_password" 
                            type="password" 
                            placeholder="请输入当前密码"
                            show-password>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="新密码" prop="new_password">
                        <el-input 
                            v-model="passwordForm.new_password" 
                            type="password" 
                            placeholder="请输入新密码"
                            show-password>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="确认密码" prop="confirm_password">
                        <el-input 
                            v-model="passwordForm.confirm_password" 
                            type="password" 
                            placeholder="请再次输入新密码"
                            show-password>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button 
                            type="primary" 
                            @click="changePassword" 
                            :loading="changingPassword">
                            修改密码
                        </el-button>
                        <el-button @click="resetPasswordForm">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </el-col>
    </el-row>
</div>
{% endblock %}


