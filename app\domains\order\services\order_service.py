from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from app.models import Order, Product, Agent
from app.models.order import OrderStatus
from app.schemas.order import OrderCreate, OrderUpdate
import logging
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)

class OrderService:
    """订单管理服务"""
    
    @staticmethod
    def generate_order_number() -> str:
        """生成订单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = str(uuid.uuid4())[:8].upper()
        return f"ORD{timestamp}{random_suffix}"

    @staticmethod
    def get_agent_orders(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        agent_id: Optional[int] = None,
        status: Optional[OrderStatus] = None,
        product_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        search: Optional[str] = None
    ) -> List[Order]:
        """获取代理商订单列表，支持多条件搜索"""
        query = db.query(Order)

        # 基础过滤条件
        if agent_id is not None:
            query = query.filter(Order.agent_id == agent_id)
        
        # 状态过滤
        if status:
            query = query.filter(Order.status == status)
        
        # 产品过滤
        if product_id:
            query = query.filter(Order.product_id == product_id)
        
        # 日期范围过滤
        if start_date:
            query = query.filter(Order.created_at >= start_date)
        if end_date:
            query = query.filter(Order.created_at <= end_date)
        
        # 关键词搜索
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Order.order_number.ilike(search_term),
                    Order.customer_info.ilike(search_term),
                    Order.notes.ilike(search_term)
                )
            )
        
        # 排序和分页
        query = query.order_by(desc(Order.created_at))
        query = query.offset(skip).limit(limit)
        
        return query.all()

    @staticmethod
    def get_agent_orders_count(
        db: Session,
        agent_id: Optional[int] = None,
        status: Optional[OrderStatus] = None,
        product_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        search: Optional[str] = None
    ) -> int:
        """获取符合条件的订单总数"""
        query = db.query(Order)

        # 基础过滤条件
        if agent_id is not None:
            query = query.filter(Order.agent_id == agent_id)
        
        # 状态过滤
        if status:
            query = query.filter(Order.status == status)
        
        # 产品过滤
        if product_id:
            query = query.filter(Order.product_id == product_id)
        
        # 日期范围过滤
        if start_date:
            query = query.filter(Order.created_at >= start_date)
        if end_date:
            query = query.filter(Order.created_at <= end_date)
        
        # 关键词搜索
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Order.order_number.ilike(search_term),
                    Order.customer_info.ilike(search_term),
                    Order.notes.ilike(search_term)
                )
            )
        
        return query.count()

    @staticmethod
    def create_order(db: Session, order_data: OrderCreate) -> Optional[Order]:
        """创建订单"""
        try:
            # 验证产品是否存在
            product = db.query(Product).filter(Product.id == order_data.product_id).first()
            if not product:
                logger.error(f"Product {order_data.product_id} not found")
                return None
            
            # 验证代理商是否存在（如果指定了代理商）
            if order_data.agent_id:
                agent = db.query(Agent).filter(Agent.id == order_data.agent_id).first()
                if not agent:
                    logger.error(f"Agent {order_data.agent_id} not found")
                    return None
            
            # 生成订单号（如果没有提供）
            if not order_data.order_number:
                order_data.order_number = OrderService.generate_order_number()
            
            # 计算总价（如果没有提供）
            if not order_data.total_price and order_data.unit_price:
                order_data.total_price = order_data.unit_price * order_data.quantity
            elif not order_data.total_price and product.price:
                order_data.total_price = product.price * order_data.quantity
                order_data.unit_price = product.price
            
            # 创建订单
            order = Order(**order_data.dict())
            db.add(order)
            db.commit()
            db.refresh(order)
            return order
            
        except Exception as e:
            logger.error(f"Error creating order: {str(e)}", exc_info=True)
            db.rollback()
            return None

    @staticmethod
    def update_order(db: Session, order_id: int, order_data: OrderUpdate) -> Optional[Order]:
        """更新订单"""
        try:
            order = db.query(Order).filter(Order.id == order_id).first()
            if not order:
                return None
            
            # 更新订单数据
            for key, value in order_data.dict(exclude_unset=True).items():
                setattr(order, key, value)
            
            db.commit()
            db.refresh(order)
            return order
            
        except Exception as e:
            logger.error(f"Error updating order {order_id}: {str(e)}", exc_info=True)
            db.rollback()
            return None

    @staticmethod
    def delete_order(db: Session, order_id: int) -> bool:
        """删除订单"""
        try:
            order = db.query(Order).filter(Order.id == order_id).first()
            if not order:
                return False
            
            db.delete(order)
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error deleting order {order_id}: {str(e)}", exc_info=True)
            db.rollback()
            return False

    @staticmethod
    def get_order_by_id(db: Session, order_id: int) -> Optional[Order]:
        """根据ID获取订单"""
        return db.query(Order).filter(Order.id == order_id).first()
    
    @staticmethod
    def get_order_by_number(db: Session, order_number: str) -> Optional[Order]:
        """根据订单号获取订单"""
        try:
            return db.query(Order).filter(Order.order_number == order_number).first()
        except Exception as e:
            logger.error(f"Error getting order by number {order_number}: {str(e)}")
            return None
    
    @staticmethod
    def get_orders(db: Session, skip: int = 0, limit: int = 100,
                   agent_id: Optional[int] = None,
                   status: Optional[OrderStatus] = None,
                   created_by_admin: Optional[bool] = None) -> List[Order]:
        """获取订单列表"""
        try:
            query = db.query(Order)

            if agent_id is not None:
                query = query.filter(Order.agent_id == agent_id)

            if status is not None:
                query = query.filter(Order.status == status)

            if created_by_admin is not None:
                query = query.filter(Order.created_by_admin == created_by_admin)

            return query.order_by(Order.created_at.desc()).offset(skip).limit(limit).all()

        except Exception as e:
            logger.error(f"Error getting orders: {str(e)}")
            return []
    
    @staticmethod
    def search_orders(db: Session, keyword: str, skip: int = 0, limit: int = 100) -> List[Order]:
        """搜索订单"""
        try:
            query = db.query(Order).filter(
                or_(
                    Order.order_number.contains(keyword),
                    Order.customer_info.contains(keyword),
                    Order.notes.contains(keyword)
                )
            )

            return query.order_by(Order.created_at.desc()).offset(skip).limit(limit).all()

        except Exception as e:
            logger.error(f"Error searching orders with keyword {keyword}: {str(e)}")
            return []
    
    @staticmethod
    def get_order_count(db: Session, agent_id: Optional[int] = None, 
                       status: Optional[OrderStatus] = None,
                       created_by_admin: Optional[bool] = None) -> int:
        """获取订单总数"""
        try:
            query = db.query(Order)
            
            if agent_id is not None:
                query = query.filter(Order.agent_id == agent_id)
            
            if status is not None:
                query = query.filter(Order.status == status)
            
            if created_by_admin is not None:
                query = query.filter(Order.created_by_admin == created_by_admin)
            
            return query.count()
            
        except Exception as e:
            logger.error(f"Error getting order count: {str(e)}")
            return 0
    
    @staticmethod
    def verify_order(db: Session, order_number: str, user_id: Optional[str] = None) -> Optional[Order]:
        """验证订单"""
        try:
            order = db.query(Order).filter(Order.order_number == order_number).first()
            if not order:
                logger.error(f"Order {order_number} not found")
                return None
            
            # 检查订单状态
            if order.status not in [OrderStatus.CONFIRMED, OrderStatus.COMPLETED]:
                logger.error(f"Order {order_number} status is {order.status.value}, cannot be used")
                return None
            
            # 检查订单是否过期
            if order.expire_date and order.expire_date < datetime.now():
                logger.error(f"Order {order_number} has expired")
                return None
            
            logger.info(f"Order verified: {order_number}")
            return order
            
        except Exception as e:
            logger.error(f"Error verifying order {order_number}: {str(e)}")
            return None

    @staticmethod
    def get_search_order_count(db: Session, keyword: str, agent_id: Optional[int] = None) -> int:
        """获取搜索订单的总数"""
        try:
            query = db.query(Order)

            if keyword:
                # 搜索订单号、客户信息
                search_filter = or_(
                    Order.order_number.ilike(f"%{keyword}%"),
                    Order.customer_info.ilike(f"%{keyword}%"),
                    Order.notes.ilike(f"%{keyword}%")
                )
                query = query.filter(search_filter)

            # 添加代理商过滤
            if agent_id is not None:
                query = query.filter(Order.agent_id == agent_id)

            return query.count()

        except Exception as e:
            logger.error(f"Error getting search order count: {str(e)}")
            return 0
