{% extends "admin/base.html" %}

{% block title %}支付调试 - FocuSee管理系统{% endblock %}
{% block page_title %}支付调试{% endblock %}

{% block content %}
<style>
    .debug-card {
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .status-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }
    .status-created { background: #e6f7ff; color: #1890ff; }
    .status-payment_created { background: #fff7e6; color: #fa8c16; }
    .status-paid { background: #f6ffed; color: #52c41a; }
    .status-failed { background: #fff2f0; color: #ff4d4f; }
    .status-timeout { background: #f5f5f5; color: #8c8c8c; }

    .qr-code {
        max-width: 200px;
        max-height: 200px;
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 8px;
    }
</style>

<div id="payment-debug-app">
    <!-- 操作按钮 -->
    <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon> 新建调试
        </el-button>
        <el-button @click="showConfigDialog = true">
            <el-icon><Setting /></el-icon> 配置管理
        </el-button>
        <el-button @click="loadDebugList">
            <el-icon><Refresh /></el-icon> 刷新
        </el-button>
    </div>
    <!-- 调试记录列表 -->
    <el-card>
        <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 16px; font-weight: 500;">调试记录</span>
            </div>
        </template>
        <div v-if="debugList.length === 0" style="text-align: center; padding: 40px; color: #909399;">
            暂无调试记录
        </div>

        <div v-for="debug in debugList" :key="debug.id" class="debug-card">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h3 class="text-lg font-medium">{{ debug.debug_name }}</h3>
                                    <p class="text-sm text-gray-500">{{ debug.created_at }}</p>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span :class="'status-badge status-' + debug.debug_status">
                                        {{ getStatusText(debug.debug_status) }}
                                    </span>
                                    <el-button size="small" @click="queryPayment(debug)">查询状态</el-button>
                                    <el-button size="small" type="danger" @click="deleteDebug(debug.id)">删除</el-button>
                                </div>
                            </div>
                            
                            <el-row :gutter="20">
                                <!-- 基本信息 -->
                                <el-col :span="8">
                                    <h4 style="font-weight: 500; margin-bottom: 12px;">基本信息</h4>
                                    <p><strong>支付模式:</strong> {{ debug.payment_mode }}</p>
                                    <p><strong>用户ID:</strong> {{ debug.user_id }}</p>
                                    <p><strong>产品ID:</strong> {{ debug.product_id }}</p>
                                    <p><strong>金额:</strong> ¥{{ debug.amount }}</p>
                                    <p><strong>订单号:</strong>
                                        <span v-if="debug.order_no">{% raw %}{{ debug.order_no }}{% endraw %}</span>
                                        <span v-else>未生成</span>
                                    </p>
                                </el-col>

                                <!-- 支付信息 -->
                                <el-col :span="8">
                                    <h4 style="font-weight: 500; margin-bottom: 12px;">支付信息</h4>
                                    <p><strong>交易号:</strong>
                                        <span v-if="debug.trade_no">{% raw %}{{ debug.trade_no }}{% endraw %}</span>
                                        <span v-else>无</span>
                                    </p>
                                    <p><strong>支付状态:</strong>
                                        <span v-if="debug.payment_status">{% raw %}{{ debug.payment_status }}{% endraw %}</span>
                                        <span v-else>无</span>
                                    </p>
                                    <p><strong>回调状态:</strong>
                                        <span v-if="debug.callback_received">已收到</span>
                                        <span v-else>未收到</span>
                                    </p>
                                    <p><strong>最后查询:</strong>
                                        <span v-if="debug.last_query_time">{% raw %}{{ debug.last_query_time }}{% endraw %}</span>
                                        <span v-else>无</span>
                                    </p>
                                </el-col>

                                <!-- 二维码 -->
                                <el-col :span="8" v-if="debug.qr_code">
                                    <h4 style="font-weight: 500; margin-bottom: 12px;">支付二维码</h4>
                                    <div :id="'qr-' + debug.id" class="qr-code"></div>
                                    <p style="font-size: 12px; color: #909399; margin-top: 8px;">{{ debug.qr_code }}</p>
                                </el-col>
                            </el-row>
                            
                            <!-- 错误信息 -->
                            <el-alert v-if="debug.error_message"
                                     title="错误信息"
                                     type="error"
                                     :description="debug.error_message"
                                     style="margin-top: 16px;"
                                     show-icon>
                            </el-alert>

                            <!-- 查询结果 -->
                            <el-alert v-if="debug.query_result"
                                     title="查询结果"
                                     type="info"
                                     style="margin-top: 16px;"
                                     show-icon>
                                <pre style="font-size: 12px; margin: 0;">{{ JSON.stringify(debug.query_result, null, 2) }}</pre>
                            </el-alert>
                        </div>
    </el-card>

        <!-- 创建调试对话框 -->
        <el-dialog title="创建支付调试" :visible.sync="showCreateDialog" width="600px">
            <el-form :model="createForm" label-width="120px">
                <el-form-item label="调试名称">
                    <el-input v-model="createForm.debug_name" placeholder="输入调试名称"></el-input>
                </el-form-item>
                
                <el-form-item label="支付模式">
                    <el-select v-model="createForm.payment_mode" placeholder="选择支付模式">
                        <el-option label="当面付" value="face_to_face"></el-option>
                        <el-option label="条码支付" value="barcode"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="用户ID">
                    <el-input v-model="createForm.user_id" placeholder="输入用户ID"></el-input>
                </el-form-item>
                
                <el-form-item label="产品ID">
                    <el-input-number v-model="createForm.product_id" :min="1" placeholder="选择产品"></el-input-number>
                </el-form-item>
                
                <el-form-item label="支付金额">
                    <el-input-number v-model="createForm.amount" :precision="2" :min="0.01" placeholder="输入金额"></el-input-number>
                </el-form-item>
                
                <el-form-item label="订单标题">
                    <el-input v-model="createForm.subject" placeholder="输入订单标题"></el-input>
                </el-form-item>
                
                <el-form-item label="订单描述">
                    <el-input v-model="createForm.body" type="textarea" placeholder="输入订单描述"></el-input>
                </el-form-item>
                
                <el-form-item label="超时时间">
                    <el-input-number v-model="createForm.timeout_minutes" :min="1" :max="120" placeholder="分钟"></el-input-number>
                </el-form-item>
            </el-form>
            
            <div slot="footer">
                <el-button @click="showCreateDialog = false">取消</el-button>
                <el-button type="primary" @click="createDebugPayment" :loading="creating">创建</el-button>
            </div>
        </el-dialog>

        <!-- 配置管理对话框 -->
        <el-dialog title="支付配置管理" :visible.sync="showConfigDialog" width="800px">
            <div class="mb-4">
                <el-button type="primary" @click="showCreateConfigDialog = true">
                    <i class="el-icon-plus"></i> 新建配置
                </el-button>
            </div>

            <el-table :data="configList" style="width: 100%">
                <el-table-column prop="config_name" label="配置名称" width="120"></el-table-column>
                <el-table-column prop="environment" label="环境" width="80"></el-table-column>
                <el-table-column prop="app_id" label="APP_ID" width="150"></el-table-column>
                <el-table-column label="状态" width="80">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.is_active ? 'success' : 'info'">
                            {{ scope.row.is_active ? '激活' : '未激活' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="editConfig(scope.row)">编辑</el-button>
                        <el-button size="mini" type="success" @click="activateConfig(scope.row.id)" v-if="!scope.row.is_active">激活</el-button>
                        <el-button size="mini" type="danger" @click="deleteConfig(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>

        <!-- 创建配置对话框 -->
        <el-dialog title="创建支付配置" :visible.sync="showCreateConfigDialog" width="600px">
            <el-form :model="configForm" label-width="120px">
                <el-form-item label="配置名称">
                    <el-input v-model="configForm.config_name" placeholder="输入配置名称"></el-input>
                </el-form-item>

                <el-form-item label="环境">
                    <el-select v-model="configForm.environment" placeholder="选择环境">
                        <el-option label="沙箱环境" value="sandbox"></el-option>
                        <el-option label="生产环境" value="production"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="APP_ID">
                    <el-input v-model="configForm.app_id" placeholder="输入支付宝APP_ID"></el-input>
                </el-form-item>

                <el-form-item label="应用私钥">
                    <el-input v-model="configForm.app_private_key" type="textarea" :rows="4" placeholder="输入应用私钥"></el-input>
                </el-form-item>

                <el-form-item label="支付宝公钥">
                    <el-input v-model="configForm.alipay_public_key" type="textarea" :rows="4" placeholder="输入支付宝公钥"></el-input>
                </el-form-item>

                <el-form-item label="网关地址">
                    <el-input v-model="configForm.gateway_url" placeholder="输入网关地址"></el-input>
                </el-form-item>

                <el-form-item label="回调地址">
                    <el-input v-model="configForm.notify_url" placeholder="输入回调地址"></el-input>
                </el-form-item>

                <el-form-item label="跳转地址">
                    <el-input v-model="configForm.return_url" placeholder="输入跳转地址（可选）"></el-input>
                </el-form-item>

                <el-form-item label="描述">
                    <el-input v-model="configForm.description" type="textarea" placeholder="输入配置描述"></el-input>
                </el-form-item>
            </el-form>

            <div slot="footer">
                <el-button @click="showCreateConfigDialog = false">取消</el-button>
                <el-button type="primary" @click="createConfig" :loading="creatingConfig">创建</el-button>
            </div>
        </el-dialog>

        <!-- 编辑配置对话框 -->
        <el-dialog title="编辑支付配置" :visible.sync="showEditConfigDialog" width="600px">
            <el-form :model="editConfigForm" label-width="120px">
                <el-form-item label="配置名称">
                    <el-input v-model="editConfigForm.config_name" placeholder="输入配置名称"></el-input>
                </el-form-item>

                <el-form-item label="环境">
                    <el-select v-model="editConfigForm.environment" placeholder="选择环境">
                        <el-option label="沙箱环境" value="sandbox"></el-option>
                        <el-option label="生产环境" value="production"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="APP_ID">
                    <el-input v-model="editConfigForm.app_id" placeholder="输入支付宝APP_ID"></el-input>
                </el-form-item>

                <el-form-item label="应用私钥">
                    <el-input v-model="editConfigForm.app_private_key" type="textarea" :rows="4" placeholder="输入应用私钥"></el-input>
                </el-form-item>

                <el-form-item label="支付宝公钥">
                    <el-input v-model="editConfigForm.alipay_public_key" type="textarea" :rows="4" placeholder="输入支付宝公钥"></el-input>
                </el-form-item>

                <el-form-item label="网关地址">
                    <el-input v-model="editConfigForm.gateway_url" placeholder="输入网关地址"></el-input>
                </el-form-item>

                <el-form-item label="回调地址">
                    <el-input v-model="editConfigForm.notify_url" placeholder="输入回调地址"></el-input>
                </el-form-item>

                <el-form-item label="跳转地址">
                    <el-input v-model="editConfigForm.return_url" placeholder="输入跳转地址（可选）"></el-input>
                </el-form-item>

                <el-form-item label="描述">
                    <el-input v-model="editConfigForm.description" type="textarea" placeholder="输入配置描述"></el-input>
                </el-form-item>
            </el-form>

            <div slot="footer">
                <el-button @click="showEditConfigDialog = false">取消</el-button>
                <el-button type="primary" @click="updateConfig" :loading="updatingConfig">更新</el-button>
            </div>
        </el-dialog>
</div>

<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
{% endblock %}

{% block data %}
    debugList: [],
    configList: [],
    showCreateDialog: false,
    showConfigDialog: false,
    showCreateConfigDialog: false,
    showEditConfigDialog: false,
    creating: false,
    creatingConfig: false,
    updatingConfig: false,
    currentEditConfigId: null,
    createForm: {
        debug_name: '',
        payment_mode: 'face_to_face',
        user_id: '',
        product_id: 1,
        amount: 0.01,
        subject: '',
        body: '',
        timeout_minutes: 30
    },
    configForm: {
        config_name: '',
        config_type: 'alipay',
        app_id: '',
        app_private_key: '',
        alipay_public_key: '',
        gateway_url: 'https://openapi-sandbox.dl.alipaydev.com/gateway.do',
        notify_url: '',
        return_url: '',
        environment: 'sandbox',
        description: ''
    },
    editConfigForm: {
        config_name: '',
        config_type: 'alipay',
        app_id: '',
        app_private_key: '',
        alipay_public_key: '',
        gateway_url: '',
        notify_url: '',
        return_url: '',
        environment: 'sandbox',
        description: ''
    }
{% endblock %}

{% block mounted %}
    this.loadDebugList();
    this.loadConfigList();
{% endblock %}

{% block methods %}
    async loadDebugList() {
        try {
            const response = await fetch('/api/payment-debug/debug/list');
            const data = await response.json();
            if (data.success) {
                this.debugList = data.data.records;
                this.$nextTick(() => {
                    this.generateQRCodes();
                });
            }
        } catch (error) {
            this.$message.error('加载调试记录失败');
        }
    },

    async loadConfigList() {
        try {
            const response = await fetch('/api/payment-debug/config/list');
            const data = await response.json();
            if (data.success) {
                this.configList = data.data;
            }
        } catch (error) {
            this.$message.error('加载配置列表失败');
        }
    },

    async createDebugPayment() {
        this.creating = true;
        try {
            const response = await fetch('/api/payment-debug/debug/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.createForm)
            });
            const data = await response.json();

            if (data.success) {
                this.$message.success('调试支付创建成功');
                this.showCreateDialog = false;
                this.loadDebugList();
            } else {
                this.$message.error(data.error || '创建失败');
            }
        } catch (error) {
            this.$message.error('创建调试支付失败');
        } finally {
            this.creating = false;
        }
    },

    async queryPayment(debug) {
        try {
            const response = await fetch(`/api/payment-debug/debug/${debug.id}/query`);
            const data = await response.json();

            if (data.success) {
                this.$message.success('查询成功');
                this.loadDebugList();
            } else {
                this.$message.error(data.error || '查询失败');
            }
        } catch (error) {
            this.$message.error('查询支付状态失败');
        }
    },

    async deleteDebug(id) {
        try {
            await this.$confirm('确定要删除这条调试记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });

            const response = await fetch(`/api/payment-debug/debug/${id}`, {
                method: 'DELETE'
            });
            const data = await response.json();

            if (data.success) {
                this.$message.success('删除成功');
                this.loadDebugList();
            } else {
                this.$message.error('删除失败');
            }
        } catch (error) {
            // 用户取消删除
        }
    },

    generateQRCodes() {
        this.debugList.forEach(debug => {
            if (debug.qr_code) {
                const element = document.getElementById(`qr-${debug.id}`);
                if (element) {
                    element.innerHTML = '';
                    QRCode.toCanvas(element, debug.qr_code, { width: 150 }, function (error) {
                        if (error) console.error(error);
                    });
                }
            }
        });
    },

    getStatusText(status) {
        const statusMap = {
            'created': '已创建',
            'payment_created': '支付已创建',
            'paid': '已支付',
            'failed': '失败',
            'timeout': '超时'
        };
        return statusMap[status] || status;
    }
{% endblock %}
