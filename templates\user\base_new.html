<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}FocuSee 用户中心{% endblock %}</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
        .layout-container {
            height: 100vh;
        }
        .header {
            background-color: #409eff;
            color: white;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }
        .logo {
            font-size: 20px;
            font-weight: bold;
            margin-right: 20px;
        }
        .main-content {
            padding: 20px;
            background-color: #f5f7fa;
            min-height: calc(100vh - 60px);
            transition: margin-left 0.3s ease;
        }
        .sidebar {
            background-color: #304156;
            width: 200px;
            transition: width 0.3s ease;
        }
        .sidebar.collapsed {
            width: 64px;
        }
        .sidebar-menu {
            border: none;
            background-color: transparent;
        }
        .sidebar-menu .el-menu-item {
            color: #bfcbd9;
            border-bottom: none;
        }
        .sidebar-menu .el-menu-item:hover {
            background-color: #263445;
            color: #409eff;
        }
        .sidebar-menu .el-menu-item.is-active {
            background-color: #409eff;
            color: white;
        }
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 20px;
        }
        .el-dropdown-link {
            cursor: pointer;
            color: white;
            display: flex;
            align-items: center;
        }
        .el-dropdown-link:hover {
            color: #c6e2ff;
        }
        .user-info {
            margin-left: auto;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container class="layout-container">
            <!-- 顶部导航 -->
            <el-header class="header" height="60px">
                <div class="logo">
                    <el-icon><User /></el-icon>
                    FocuSee 用户中心
                </div>
                <div class="user-info">
                    <el-dropdown @command="handleCommand">
                        <span class="el-dropdown-link">
                            <el-icon><User /></el-icon>
                            {{ "{{ userInfo.username || '用户' }}" }}
                            <el-icon class="el-icon--right"><arrow-down /></el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </el-header>

            <el-container>
                <!-- 侧边栏 -->
                <el-aside class="sidebar" :class="{ collapsed: sidebarCollapsed }" width="200px">
                    <el-menu
                        :default-active="activeMenu"
                        class="sidebar-menu"
                        :collapse="sidebarCollapsed"
                        :collapse-transition="false"
                        background-color="#304156"
                        text-color="#bfcbd9"
                        active-text-color="#409eff">
                        
                        <el-menu-item index="/user/dashboard" @click="navigate('/user/dashboard')">
                            <el-icon><Odometer /></el-icon>
                            <span>仪表板</span>
                        </el-menu-item>
                        <el-menu-item index="/user/licenses" @click="navigate('/user/licenses')">
                            <el-icon><Key /></el-icon>
                            <span>我的授权</span>
                        </el-menu-item>
                        <el-menu-item index="/user/payment" @click="navigate('/user/payment')">
                            <el-icon><CreditCard /></el-icon>
                            <span>支付中心</span>
                        </el-menu-item>
                        <el-menu-item index="/user/api-usage" @click="navigate('/user/api-usage')">
                            <el-icon><DataAnalysis /></el-icon>
                            <span>API使用统计</span>
                        </el-menu-item>
                        <el-menu-item index="/user/profile" @click="navigate('/user/profile')">
                            <el-icon><Setting /></el-icon>
                            <span>个人设置</span>
                        </el-menu-item>
                    </el-menu>
                </el-aside>

                <!-- 主内容区 -->
                <el-main class="main-content">
                    <div class="page-title">{% block page_title %}{% endblock %}</div>
                    {% block content %}{% endblock %}
                </el-main>
            </el-container>
        </el-container>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;
        
        const app = createApp({
            data() {
                return {
                    activeMenu: '{{ request.url.path }}',
                    sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true' || false,
                    userInfo: JSON.parse(localStorage.getItem('user_info') || '{"username": "用户"}'),
                    {% block data %}{% endblock %}
                };
            },
            computed: {
                {% block computed %}{% endblock %}
            },
            methods: {
                navigate(path) {
                    window.location.href = path;
                },
                formatTime(timeStr) {
                    if (!timeStr) return '';
                    const date = new Date(timeStr);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                },
                handleCommand(command) {
                    if (command === 'profile') {
                        this.navigate('/user/profile');
                    } else if (command === 'logout') {
                        ElMessageBox.confirm('确定要退出登录吗？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            localStorage.removeItem('user_info');
                            localStorage.removeItem('access_token');
                            window.location.href = '/user/login';
                        });
                    }
                },
                toggleSidebar() {
                    this.sidebarCollapsed = !this.sidebarCollapsed;
                    localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed.toString());
                },
                {% block methods %}{% endblock %}
            },
            mounted() {
                {% block mounted %}{% endblock %}
            }
        });
        
        // 注册 Element Plus 图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        app.use(ElementPlus);
        app.mount('#app');
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
