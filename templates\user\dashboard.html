{% extends "user/base.html" %}

{% block title %}仪表板{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<div id="user-dashboard-app">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
            <el-card class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon licenses">
                        <el-icon><Key /></el-icon>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number" v-text="stats?.totalLicenses || 0"></div>
                        <div class="stat-label">总授权数</div>
                    </div>
                </div>
            </el-card>
        </el-col>

        <el-col :span="6">
            <el-card class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon active">
                        <el-icon><CircleCheck /></el-icon>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number" v-text="stats?.activeLicenses || 0"></div>
                        <div class="stat-label">激活授权</div>
                    </div>
                </div>
            </el-card>
        </el-col>

        <el-col :span="6">
            <el-card class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon api-calls">
                        <el-icon><DataAnalysis /></el-icon>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number" v-text="stats?.totalApiCalls || 0"></div>
                        <div class="stat-label">API调用次数</div>
                    </div>
                </div>
            </el-card>
        </el-col>
        
        <el-col :span="6">
            <el-card class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon products">
                        <el-icon><Box /></el-icon>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number" v-text="stats?.authorizedProducts || 0"></div>
                        <div class="stat-label">授权产品</div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
        <el-col :span="12">
            <el-card>
                <template #header>
                    <div class="card-header">
                        <span>快速操作</span>
                    </div>
                </template>
                <div class="action-buttons">
                    <el-button type="primary" @click="navigate('/user/licenses')" style="margin-bottom: 10px;">
                        <el-icon><Key /></el-icon>
                        查看我的授权
                    </el-button>
                    <el-button type="success" @click="navigate('/user/api-usage')" style="margin-bottom: 10px;">
                        <el-icon><DataAnalysis /></el-icon>
                        API使用统计
                    </el-button>
                    <el-button type="info" @click="navigate('/user/profile')">
                        <el-icon><Setting /></el-icon>
                        个人设置
                    </el-button>
                </div>
            </el-card>
        </el-col>
        
        <el-col :span="12">
            <el-card>
                <template #header>
                    <div class="card-header">
                        <span>最近活动</span>
                    </div>
                </template>
                <div class="recent-activities">
                    <div v-if="recentActivities.length === 0" class="no-data">
                        暂无最近活动
                    </div>
                    <div v-else>
                        <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                            <div class="activity-icon">
                                <el-icon><Clock /></el-icon>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title" v-text="activity.title"></div>
                                <div class="activity-time" v-text="formatDate(activity.time)"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
    
    <!-- 我的授权概览 -->
    <el-row>
        <el-col :span="24">
            <el-card>
                <template #header>
                    <div class="card-header">
                        <span>我的授权概览</span>
                        <el-button type="text" @click="navigate('/user/licenses')">查看全部</el-button>
                    </div>
                </template>
                <el-table :data="userLicenses" v-loading="loading">
                    <el-table-column prop="license_code" label="授权码" min-width="200">
                        <template #default="scope">
                            <el-text class="license-code" @click="copyToClipboard(scope.row.license_code)">
                                <span v-text="scope.row.license_code"></span>
                            </el-text>
                        </template>
                    </el-table-column>
                    <el-table-column prop="product.name" label="产品名称" min-width="150">
                        <template #default="scope">
                            <span v-text="scope.row.product && scope.row.product.name ? scope.row.product.name : '-'"></span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="max_api_calls" label="API限制" width="120">
                        <template #default="scope">
                            <span v-text="scope.row.max_api_calls ? scope.row.max_api_calls : '无限制'"></span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="used_api_calls" label="已使用" width="100"></el-table-column>
                    <el-table-column label="使用率" width="120">
                        <template #default="scope">
                            <el-progress 
                                v-if="scope.row.max_api_calls > 0"
                                :percentage="Math.round((scope.row.used_api_calls / scope.row.max_api_calls) * 100)"
                                :color="getProgressColor(scope.row.used_api_calls / scope.row.max_api_calls)">
                            </el-progress>
                            <span v-else>无限制</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="expire_date" label="过期时间" width="180">
                        <template #default="scope">
                            <span v-text="formatDate(scope.row.expire_date)"></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" width="100">
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                <span v-text="getStatusText(scope.row.status)"></span>
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </el-col>
    </el-row>
</div>
{% endblock %}

{% block scripts %}
<script>
const userDashboardApp = createApp({
    setup() {
        console.log('Vue app setup started');

        const loading = ref(false);
        const stats = reactive({
            totalLicenses: 0,
            activeLicenses: 0,
            totalApiCalls: 0,
            authorizedProducts: 0
        });

        console.log('Stats initialized:', stats);
        
        const userLicenses = ref([]);
        const recentActivities = ref([]);
        
        const loadStats = async () => {
            try {
                const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
                
                // TODO: 实现统计数据加载
                stats.totalLicenses = 5;
                stats.activeLicenses = 3;
                stats.totalApiCalls = 1250;
                stats.authorizedProducts = 2;
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        };
        
        const loadUserLicenses = async () => {
            loading.value = true;
            try {
                const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
                
                // TODO: 实现用户授权加载
                userLicenses.value = [
                    {
                        id: 1,
                        license_code: 'FS-PRO-2024-ABCD1234',
                        product: { name: 'FocuSee Pro' },
                        max_api_calls: 1000,
                        used_api_calls: 650,
                        expire_date: '2024-12-31T23:59:59',
                        status: 'ACTIVE'
                    },
                    {
                        id: 2,
                        license_code: 'FS-BASIC-2024-EFGH5678',
                        product: { name: 'FocuSee Basic' },
                        max_api_calls: 500,
                        used_api_calls: 120,
                        expire_date: '2024-12-31T23:59:59',
                        status: 'ACTIVE'
                    }
                ];
            } catch (error) {
                console.error('Failed to load user licenses:', error);
            } finally {
                loading.value = false;
            }
        };
        
        const loadRecentActivities = async () => {
            try {
                // TODO: 实现最近活动加载
                recentActivities.value = [
                    {
                        id: 1,
                        title: '激活了新的授权码',
                        time: new Date()
                    },
                    {
                        id: 2,
                        title: 'API调用达到500次',
                        time: new Date(Date.now() - 3600000)
                    }
                ];
            } catch (error) {
                console.error('Failed to load recent activities:', error);
            }
        };
        
        const navigate = (path) => {
            window.location.href = path;
        };
        
        const copyToClipboard = async (text) => {
            try {
                await navigator.clipboard.writeText(text);
                ElMessage.success('授权码已复制到剪贴板');
            } catch (error) {
                ElMessage.error('复制失败');
            }
        };
        
        const getProgressColor = (percentage) => {
            if (percentage < 0.5) return '#67c23a';
            if (percentage < 0.8) return '#e6a23c';
            return '#f56c6c';
        };
        
        const getStatusType = (status) => {
            const statusMap = {
                'INACTIVE': 'info',
                'ACTIVE': 'success',
                'EXPIRED': 'warning',
                'SUSPENDED': 'warning',
                'REVOKED': 'danger'
            };
            return statusMap[status] || 'info';
        };
        
        const getStatusText = (status) => {
            const statusMap = {
                'INACTIVE': '未激活',
                'ACTIVE': '激活',
                'EXPIRED': '过期',
                'SUSPENDED': '暂停',
                'REVOKED': '撤销'
            };
            return statusMap[status] || status;
        };
        
        const formatDate = (dateString) => {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleString('zh-CN');
        };
        
        onMounted(() => {
            console.log('Component mounted, loading data...');
            loadStats();
            loadUserLicenses();
            loadRecentActivities();
        });
        
        return {
            loading,
            stats,
            userLicenses,
            recentActivities,
            navigate,
            copyToClipboard,
            getProgressColor,
            getStatusType,
            getStatusText,
            formatDate
        };
    }
});

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    userDashboardApp.component(key, component);
}

userDashboardApp.use(ElementPlus);
userDashboardApp.mount('#user-dashboard-app');
</script>

<style>
.stats-row {
    margin-bottom: 20px;
}

.stat-card {
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.stat-icon.licenses {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.stat-icon.active {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
}

.stat-icon.api-calls {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.stat-icon.products {
    background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
}

.stat-number {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.quick-actions {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.action-buttons {
    display: flex;
    flex-direction: column;
}

.action-buttons .el-button {
    width: 100%;
}

.recent-activities {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    margin-right: 10px;
    color: #666;
}

.activity-title {
    font-size: 14px;
    color: #333;
}

.activity-time {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
}

.no-data {
    text-align: center;
    color: #999;
    padding: 20px;
}

.license-code {
    cursor: pointer;
    color: #74b9ff;
    font-family: monospace;
}

.license-code:hover {
    text-decoration: underline;
}
</style>
{% endblock %}
