<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FocuSee | 智能屏幕录制，自动生成专业演示视频</title>
    <meta name="description" content="FocuSee 是一款革命性的屏幕录制工具，能够自动跟踪鼠标移动，添加动态缩放效果，一键生成专业级产品演示视频。无需复杂的后期编辑，让您专注于内容创作。">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .hero-bg {
            background: linear-gradient(135deg, #252b3c 0%, #6a5b4b 100%);
        }
        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="bg-white">
    <!-- 导航栏 -->
    <nav class="bg-black shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <img class="h-8 w-auto" src="https://focusee.imobie-resource.com/img/focusee_logo_index.svg" alt="FocuSee">
                    </div>
                    <div class="hidden md:block ml-10">
                        <div class="flex items-baseline space-x-4">
                            <a href="#features" class="text-white hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">功能特色</a>
                            <a href="#pricing" class="text-white hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">价格方案</a>
                            <a href="#testimonials" class="text-white hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">用户评价</a>
                        </div>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/downloads/FocuSee1.1.1.zip" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-300">
                        免费下载
                    </a>
                    <a href="/trial" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-300">
                        一个月试用
                    </a>
                    <a href="#pricing" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-300">
                        立即购买
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-bg pt-20 pb-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- 左侧文字内容 -->
                <div class="text-center lg:text-left">
                    <div class="mb-8">
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-500 text-white backdrop-blur-sm pulse-animation">
                            <i class="fas fa-fire mr-2"></i>
                            🔥 7月限时特惠：专业版仅需 ¥99（原价 ¥299）- 立省 ¥200！
                        </span>
                    </div>
                    <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                        将屏幕录制自动转换为<br>
                        <span class="text-yellow-300">专业演示视频</span>
                    </h1>
                    <p class="text-xl text-white text-opacity-90 mb-8">
                        您只需专注于屏幕录制，让 FocuSee 自动处理后期制作。智能跟踪鼠标移动，添加动态缩放效果，
                        一键生成视觉震撼的专业视频。无需手动编辑，录制完成即可使用。
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center">
                        <a href="#download" class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-lg text-lg font-semibold transition duration-300 shadow-lg">
                            <i class="fas fa-download mr-2"></i>
                            免费下载
                        </a>
                        <a href="/trial" class="bg-orange-500 text-white hover:bg-orange-600 px-8 py-4 rounded-lg text-lg font-semibold transition duration-300 shadow-lg">
                            <i class="fas fa-gift mr-2"></i>
                            一个月试用
                        </a>
                        <a href="#pricing" class="bg-yellow-400 text-gray-900 hover:bg-yellow-300 px-8 py-4 rounded-lg text-lg font-semibold transition duration-300 shadow-lg">
                            <i class="fas fa-crown mr-2"></i>
                            查看价格
                        </a>
                    </div>
                    <p class="text-white text-opacity-75 mt-4 text-sm">
                        支持 Windows 10+ 和 macOS 10.13+ | 🎯 已有50,000+用户选择 | ⏰ 特惠仅剩3天
                    </p>

                    <!-- 用户数量和评分 -->
                    <div class="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-6 mt-6 text-white text-opacity-90">
                        <div class="flex items-center">
                            <i class="fas fa-users mr-2"></i>
                            <span class="text-sm">50,000+ 活跃用户</span>
                        </div>
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 mr-2">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-sm">4.9/5 用户评分</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-download mr-2"></i>
                            <span class="text-sm">100万+ 下载量</span>
                        </div>
                    </div>
                </div>

                <!-- 右侧产品演示视频 -->
                <div class="relative">
                    <div class="relative z-10">
                        <!-- 产品演示视频 -->
                        <div class="relative rounded-lg overflow-hidden shadow-2xl">
                            <!-- 16:9 宽高比容器 -->
                            <div class="relative w-full" style="padding-bottom: 56.25%;">
                                <video
                                    class="absolute inset-0 w-full h-full object-cover"
                                    autoplay
                                    muted
                                    loop
                                    playsinline
                                    poster="https://focusee.imobie-resource.com/img/available_img.png">
                                    <source src="https://focusee.imobie-resource.com/img/banner_index.mp4" type="video/mp4">
                                    <!-- 视频不支持时的后备图片 -->
                                    <img src="https://focusee.imobie-resource.com/img/available_img.png"
                                         alt="FocuSee 产品界面演示"
                                         class="absolute inset-0 w-full h-full object-cover">
                                </video>
                            </div>

                            <!-- 播放控制按钮覆盖层 -->
                            <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 bg-black bg-opacity-20">
                                <button class="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-4 transition-all duration-300 transform hover:scale-110" onclick="toggleVideo(this)">
                                    <i class="fas fa-pause text-2xl text-gray-800"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 视频说明 -->
                        <div class="mt-4 text-center">
                            <p class="text-white text-opacity-90 text-sm">
                                <i class="fas fa-play-circle mr-2"></i>
                                观看 FocuSee 实时演示 - 自动缩放和专业效果
                            </p>
                        </div>
                    </div>
                    <!-- 装饰性背景元素 -->
                    <div class="absolute -top-4 -right-4 w-72 h-72 bg-white bg-opacity-10 rounded-full blur-3xl"></div>
                    <div class="absolute -bottom-4 -left-4 w-64 h-64 bg-yellow-300 bg-opacity-20 rounded-full blur-3xl"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- 产品特色 -->
    <section id="features" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    屏幕录制配备自动化编辑
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    像往常一样录制您的屏幕、自拍和画外音。FocuSee 自动跟踪光标移动，应用动态缩放效果，
                    添加精美背景，生成专业级视频。为您节省宝贵的时间和额外的编辑工作。
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 功能卡片 1 -->
                <div class="feature-card bg-white rounded-xl p-8 shadow-lg">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-magic text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">智能自动缩放</h3>
                    <p class="text-gray-600">
                        自动跟踪鼠标移动，智能添加缩放效果，让观众始终关注重点内容，提升视频观看体验。
                    </p>
                </div>

                <!-- 功能卡片 2 -->
                <div class="feature-card bg-white rounded-xl p-8 shadow-lg">
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-video text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">一键生成专业视频</h3>
                    <p class="text-gray-600">
                        录制完成即可获得专业级视频，无需复杂的后期编辑。自动添加背景、特效和过渡动画。
                    </p>
                </div>

                <!-- 功能卡片 3 -->
                <div class="feature-card bg-white rounded-xl p-8 shadow-lg">
                    <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-desktop text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">跨平台支持</h3>
                    <p class="text-gray-600">
                        完美支持 Windows 和 macOS 系统，无论您使用哪个平台，都能享受流畅的录制体验。
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- 使用场景 -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    适用于各种场景
                </h2>
                <p class="text-xl text-gray-600">
                    无论是产品演示、教程制作还是营销推广，FocuSee 都能满足您的需求
                </p>
            </div>

            <!-- 跨平台支持展示 -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 mb-16">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <div>
                        <h3 class="text-3xl font-bold text-gray-900 mb-4">支持 Windows 和 Mac</h3>
                        <p class="text-lg text-gray-600 mb-6">
                            专为在 Windows 和 macOS 上流畅运行而构建，FocuSee 让您无论使用哪个系统都能灵活创建专业视频。
                            一个屏幕录制器，在两个平台上都表现出色——没有限制。
                        </p>
                        <a href="#download" class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition duration-300">
                            立即免费下载 →
                        </a>
                    </div>
                    <div class="text-center">
                        
                        <img src="https://focusee.imobie-resource.com/img/available_img.png"
                             alt="跨平台界面展示"
                             class="w-full max-w-md mx-auto rounded-lg shadow-lg">
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 场景 1 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-person text-3xl text-blue-600"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">产品演示 & 教程</h3>
                    <p class="text-gray-600 mb-6">
                        制作引人入胜的产品演示和教程视频，既能聚焦细节又能提供全局视角。
                        添加互动问答、表单和行动按钮，即时收集反馈和邮箱，促进用户行动。
                    </p>
                    <a href="#download" class="text-blue-600 hover:text-blue-700 font-medium">
                        立即免费下载 →
                    </a>
                </div>

                <!-- 场景 2 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-rocket text-3xl text-green-600"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">推广 & 发布视频</h3>
                    <p class="text-gray-600 mb-6">
                        为产品发布和更新录制引人注目的视频。一键调整为各种社交媒体平台尺寸。
                        导出高清视频或 GIF 格式，完美适配着陆页，吸引注意力并提高转化率。
                    </p>
                    <a href="#download" class="text-green-600 hover:text-green-700 font-medium">
                        立即免费下载 →
                    </a>
                </div>

                <!-- 场景 3 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-handshake text-3xl text-purple-600"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">销售 & 推介视频</h3>
                    <p class="text-gray-600 mb-6">
                        通过引人注目的视频提升推广成功率，支持自拍和屏幕并排显示，
                        配合画外音解说。增加个人化元素，与客户建立更好的联系和信任。
                    </p>
                    <a href="#download" class="text-purple-600 hover:text-purple-700 font-medium">
                        立即免费下载 →
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- 高级功能展示 -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    需要个性化润色？当然可以！
                </h2>
                <p class="text-xl text-gray-600">
                    FocuSee 帮助您完善视频创作的每一个步骤
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                <!-- 可定制缩放效果 -->
                <div class="feature-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="w-full h-48 rounded-lg mb-6 overflow-hidden">
                        <img src="https://focusee.imobie-resource.com/img/salesvideos.png"
                             alt="可定制缩放效果"
                             class="w-full h-full object-cover">
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">可定制缩放效果</h3>
                    <p class="text-gray-600">
                        调整缩放位置、级别和持续时间，或添加新的缩放点以获得最大视觉冲击力。
                    </p>
                </div>

                <!-- 聚光灯高亮 -->
                <div class="feature-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="w-full h-48 rounded-lg mb-6 overflow-hidden">
                        <img src="https://focusee.imobie-resource.com/img/tutorials.png"
                             alt="2倍聚光灯高亮"
                             class="w-full h-full object-cover">
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">2倍聚光灯高亮</h3>
                    <p class="text-gray-600">
                        让您的焦点更加突出。精确引导观众注意力到您希望的位置。
                    </p>
                </div>

                <!-- 多样化光标样式 -->
                <div class="feature-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="w-full h-48 rounded-lg mb-6 overflow-hidden">
                        <img src="https://www.gemoo-resource.com/focusee/img/img11_watermark.png"
                             alt="多样化光标样式和点击效果"
                             class="w-full h-full object-cover">
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">多样化光标样式和点击效果</h3>
                    <p class="text-gray-600">
                        从8种鼠标光标样式和3种按钮点击效果中选择，匹配您的视频风格。
                    </p>
                </div>

                <!-- 内置提词器 -->
                <div class="feature-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="w-full h-48 rounded-lg mb-6 overflow-hidden">
                        <img src="https://focusee.imobie-resource.com/img/img11_video.png"
                             alt="内置提词器覆盖"
                             class="w-full h-full object-cover">
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">内置提词器覆盖</h3>
                    <p class="text-gray-600">
                        录制时在屏幕上显示您的脚本，帮助您保持正轨，每次都能流畅自信地制作教程和演示。
                    </p>
                </div>

                <!-- 键盘快捷键显示 -->
                <div class="feature-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="w-full h-48 rounded-lg mb-6 overflow-hidden">
                        <img src="https://focusee.imobie-resource.com/img/img9_video.png"
                             alt="捕获并显示键盘快捷键"
                             class="w-full h-full object-cover">
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">捕获并显示键盘快捷键</h3>
                    <p class="text-gray-600">
                        在屏幕录制过程中自动捕获并显示键盘快捷键，让教程或分步指南更清晰、更吸引人。
                    </p>
                </div>

                <!-- 自动字幕生成 -->
                <div class="feature-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="w-full h-48 rounded-lg mb-6 overflow-hidden">
                        <img src="https://www.gemoo-resource.com/focusee/img/img8_captions.png"
                             alt="即时自动字幕生成"
                             class="w-full h-full object-cover">
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">即时自动字幕生成</h3>
                    <p class="text-gray-600">
                        自动为您的视频生成字幕，并根据需要进行编辑以确保100%准确性。
                    </p>
                </div>

                <!-- 同时录制麦克风和系统音频 -->
                <div class="feature-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="w-full h-48 rounded-lg mb-6 overflow-hidden">
                        <img src="https://focusee.imobie-resource.com/img/img10_video.png"
                             alt="同时录制麦克风和系统音频"
                             class="w-full h-full object-cover">
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">同时录制麦克风和系统音频</h3>
                    <p class="text-gray-600">
                        捕获麦克风解说和系统声音，分别录制音轨。完美适用于教程、画外音和清晰可编辑的屏幕录制。
                    </p>
                </div>

                <!-- 导入本地视频文件 -->
                <div class="feature-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="w-full h-48 rounded-lg mb-6 overflow-hidden">
                        <img src="https://focusee.imobie-resource.com/img/img12_video.png"
                             alt="导入本地视频文件"
                             class="w-full h-full object-cover">
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">导入本地视频文件</h3>
                    <p class="text-gray-600">
                        从您的设备上传任何视频，立即开始新项目。编辑现有素材，添加增强效果，快速导出专业结果。
                    </p>
                </div>

                <!-- 修剪和变速 -->
                <div class="feature-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="w-full h-48 rounded-lg mb-6 overflow-hidden">
                        <img src="https://www.gemoo-resource.com/focusee/img/img4_trim.png"
                             alt="修剪和变速"
                             class="w-full h-full object-cover">
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">修剪和变速</h3>
                    <p class="text-gray-600">
                        修剪、剪切、裁剪、加速或减慢特定部分。让您的视频简洁明了。
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- 导出和分享选项 -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    多种导出和分享选项
                </h2>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 社交媒体预设尺寸 -->
                <div class="text-center">
                    <div class="mb-6">
                        <img src="https://www.gemoo-resource.com/focusee/img/icon_socialmedia.png"
                             alt="社交媒体预设尺寸"
                             class="w-16 h-16 mx-auto mb-4">
                        <img src="https://focusee.imobie-resource.com/img/img1_socialmedia.png"
                             alt="社交媒体平台展示"
                             class="w-full max-w-sm mx-auto rounded-lg shadow-lg">
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">社交媒体预设尺寸</h3>
                    <p class="text-gray-600 mb-6">
                        从不同平台的预设宽高比中选择，如YouTube、Twitter、TikTok等。
                        所有动画和效果将自动调整以获得最佳体验。
                    </p>
                </div>

                <!-- 视频或GIF -->
                <div class="text-center">
                    <div class="mb-6">
                        <img src="https://www.gemoo-resource.com/focusee/img/icon_videofile.png"
                             alt="视频或GIF导出"
                             class="w-16 h-16 mx-auto mb-4">
                        <img src="https://focusee.imobie-resource.com/img/img2_videoorgif.png"
                             alt="视频和GIF格式展示"
                             class="w-full max-w-sm mx-auto rounded-lg shadow-lg">
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">视频或GIF，您来决定</h3>
                    <p class="text-gray-600 mb-6">
                        将您的内容导出为高达4K的精美视频或高质量GIF，
                        提供灵活性以适应您的特定用例和受众偏好。
                    </p>
                </div>

                <!-- 可分享链接 -->
                <div class="text-center">
                    <div class="mb-6">
                        <img src="https://focusee.imobie-resource.com/img/icon_onlineshare.png"
                             alt="可分享链接"
                             class="w-16 h-16 mx-auto mb-4">
                        <img src="https://focusee.imobie-resource.com/img/img3_onlineshare.png"
                             alt="在线分享展示"
                             class="w-full max-w-sm mx-auto rounded-lg shadow-lg">
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">通过可分享链接即时交付</h3>
                    <p class="text-gray-600 mb-6">
                        轻松上传您的最终视频并生成干净的可分享链接。
                        与客户、团队成员或学习者快速分享，同时通过密码保持对隐私和访问的完全控制。
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- 价格对比 -->
    <section id="pricing" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    专业品质视频，预算友好的解决方案
                </h2>
                <p class="text-xl text-gray-600">
                    告别昂贵的外包、复杂的编辑和数小时的努力。使用FocuSee，您可以用不到1/10的时间、精力和成本创建专业级视频。
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16 items-center">
                <!-- 使用前 -->
                <div class="bg-red-50 rounded-xl p-8 border-2 border-red-200">
                    <div class="text-center mb-6">
                        <img src="https://www.gemoo-resource.com/focusee/img/img_before.png"
                             alt="使用前的复杂流程"
                             class="h-24 mx-auto mb-4">
                        <h3 class="text-2xl font-bold text-red-800">使用 FocuSee 之前</h3>
                    </div>
                    <ul class="space-y-4">
                        <li class="flex items-start">
                            <i class="fas fa-times text-red-500 mt-1 mr-3"></i>
                            <span class="text-red-700">需要专业技能的复杂编辑</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-times text-red-500 mt-1 mr-3"></i>
                            <span class="text-red-700">每个视频需要数小时</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-times text-red-500 mt-1 mr-3"></i>
                            <span class="text-red-700">需要多个应用程序进行录制、编辑、分享等</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-times text-red-500 mt-1 mr-3"></i>
                            <span class="text-red-700">1个视频的制作服务费用超过1000元</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-times text-red-500 mt-1 mr-3"></i>
                            <span class="text-red-700">在社交媒体上发布的录制视频观看量低？</span>
                        </li>
                    </ul>
                </div>

                <!-- VS 对比 -->
                <div class="text-center">
                    <img src="https://www.gemoo-resource.com/focusee/img/vs.png"
                         alt="VS 对比"
                         class="w-24 h-24 mx-auto">
                </div>

                <!-- 使用后 -->
                <div class="bg-green-50 rounded-xl p-8 border-2 border-green-200">
                    <div class="text-center mb-6">
                        <img src="https://www.gemoo-resource.com/focusee/img/img_after.png"
                             alt="使用后的简单流程"
                             class="h-24 mx-auto mb-4">
                        <h3 class="text-2xl font-bold text-green-800">使用 FocuSee 之后</h3>
                    </div>
                    <ul class="space-y-4">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-3"></i>
                            <span class="text-green-700">零输入的自动编辑</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-3"></i>
                            <span class="text-green-700">每个视频只需几分钟</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-3"></i>
                            <span class="text-green-700">一个应用程序完成整个工作流程</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-3"></i>
                            <span class="text-green-700">限时特惠仅需299元即可永久制作无限视频</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-3"></i>
                            <span class="text-green-700">酷炫动画和智能缩放提升观看量</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 价格方案 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <!-- 免费版 -->
                <div class="bg-white rounded-xl p-8 shadow-lg border-2 border-gray-200">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">免费版</h3>
                        <div class="text-4xl font-bold text-gray-900 mb-2">¥0</div>
                        <p class="text-gray-600 mb-6">永久免费</p>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>基础屏幕录制</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>自动缩放效果</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>720P导出</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-times text-red-500 mr-3"></i>
                                <span>带水印</span>
                            </li>
                        </ul>
                        <a href="#download" class="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg font-semibold transition duration-300 block text-center">
                            免费下载
                        </a>
                    </div>
                </div>

                <!-- 专业版 -->
                <div class="bg-white rounded-xl p-8 shadow-lg border-2 border-blue-500 relative">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                            最受欢迎
                        </span>
                    </div>
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">专业版</h3>
                        <div class="mb-2">
                            <span class="text-2xl text-gray-500 line-through">¥499</span>
                            <div class="text-4xl font-bold text-red-600">¥299</div>
                        </div>
                        <p class="text-gray-600 mb-2">一次购买，终身使用</p>
                        <p class="text-red-600 text-sm font-semibold mb-4">🔥 限时特惠，立省¥200</p>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>所有免费版功能</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>4K高清导出</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>无水印</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>高级编辑功能</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>优先技术支持</span>
                            </li>
                        </ul>
                        <a href="#buy" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition duration-300 block text-center">
                            立即购买
                        </a>
                    </div>
                </div>

                <!-- 企业版 -->
                <div class="bg-white rounded-xl p-8 shadow-lg border-2 border-purple-500">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">企业版</h3>
                        <div class="text-4xl font-bold text-purple-600 mb-2">¥999</div>
                        <p class="text-gray-600 mb-6">团队协作版本</p>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>所有专业版功能</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>多用户许可证</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>团队协作功能</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>品牌定制</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>专属客服支持</span>
                            </li>
                        </ul>
                        <a href="#contact" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-lg font-semibold transition duration-300 block text-center">
                            联系销售
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 用户作品展示 -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    FocuSee 用户分享他们的创作
                </h2>
                <p class="text-xl text-gray-600">
                    发现用户使用 FocuSee 创建的精彩视频：演示、发布、推广、教程等等
                </p>
            </div>

            <!-- 用户作品网格 -->
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
                <div class="bg-gray-100 rounded-lg p-4 hover:shadow-lg transition duration-300">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm mb-1">产品演示</h4>
                    <p class="text-gray-600 text-xs">@科技创业者</p>
                </div>
                <div class="bg-gray-100 rounded-lg p-4 hover:shadow-lg transition duration-300">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm mb-1">教程制作</h4>
                    <p class="text-gray-600 text-xs">@教育工作者</p>
                </div>
                <div class="bg-gray-100 rounded-lg p-4 hover:shadow-lg transition duration-300">
                    <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm mb-1">营销推广</h4>
                    <p class="text-gray-600 text-xs">@市场营销</p>
                </div>
                <div class="bg-gray-100 rounded-lg p-4 hover:shadow-lg transition duration-300">
                    <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm mb-1">软件介绍</h4>
                    <p class="text-gray-600 text-xs">@开发者</p>
                </div>
                <div class="bg-gray-100 rounded-lg p-4 hover:shadow-lg transition duration-300">
                    <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm mb-1">操作指南</h4>
                    <p class="text-gray-600 text-xs">@技术支持</p>
                </div>
                <div class="bg-gray-100 rounded-lg p-4 hover:shadow-lg transition duration-300">
                    <div class="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm mb-1">功能展示</h4>
                    <p class="text-gray-600 text-xs">@产品经理</p>
                </div>
                <div class="bg-gray-100 rounded-lg p-4 hover:shadow-lg transition duration-300">
                    <div class="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm mb-1">课程录制</h4>
                    <p class="text-gray-600 text-xs">@在线讲师</p>
                </div>
                <div class="bg-gray-100 rounded-lg p-4 hover:shadow-lg transition duration-300">
                    <div class="w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm mb-1">客户案例</h4>
                    <p class="text-gray-600 text-xs">@销售团队</p>
                </div>
            </div>

            <!-- 统计数据 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div>
                    <div class="text-3xl font-bold text-blue-600 mb-2">50,000+</div>
                    <div class="text-gray-600">活跃用户</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-green-600 mb-2">1,000,000+</div>
                    <div class="text-gray-600">视频创作</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-purple-600 mb-2">4.9/5</div>
                    <div class="text-gray-600">用户评分</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-red-600 mb-2">99%</div>
                    <div class="text-gray-600">满意度</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 用户评价 -->
    <section id="testimonials" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    用户喜爱 FocuSee 是有原因的
                </h2>
                <p class="text-xl text-gray-600">
                    看看真实用户如何评价 FocuSee
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 评价 1 -->
                <div class="bg-gray-50 rounded-xl p-6 shadow-lg">
                    <div class="flex items-center mb-4">
                        <img src="https://www.gemoo-resource.com/focusee/img/review_img1.png" alt="用户头像" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">张小明</h4>
                            <p class="text-gray-600 text-sm">产品经理</p>
                        </div>
                    </div>
                    <p class="text-gray-700">
                        "录制产品演示、编辑、写脚本和录制画外音是痛苦且耗时的。使用FocuSee，产品演示的一切都可以在一个地方完成。
                        另外，您可以一次性获得不同类型的社交媒体版本。"
                    </p>
                    <div class="flex text-yellow-400 mt-4">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <!-- 评价 2 -->
                <div class="bg-gray-50 rounded-xl p-6 shadow-lg">
                    <div class="flex items-center mb-4">
                        <img src="https://www.gemoo-resource.com/focusee/img/review_img2.png" alt="用户头像" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">李华</h4>
                            <p class="text-gray-600 text-sm">自媒体创作者</p>
                        </div>
                    </div>
                    <p class="text-gray-700">
                        "我刚刚购买了，为客户录制了一个演示。界面很好，非常容易使用。我甚至不需要做任何调整；
                        我只是改变了光标效果。喜欢这个产品。"
                    </p>
                    <div class="flex text-yellow-400 mt-4">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <!-- 评价 3 -->
                <div class="bg-gray-50 rounded-xl p-6 shadow-lg">
                    <div class="flex items-center mb-4">
                        <img src="https://www.gemoo-resource.com/focusee/img/<EMAIL>" alt="用户头像" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">王小红</h4>
                            <p class="text-gray-600 text-sm">教育工作者</p>
                        </div>
                    </div>
                    <p class="text-gray-700">
                        "喜欢这个，据我所知，这是Windows上唯一带有自动缩放效果的录制器。
                        感谢FocuSee团队让这成为可能。"
                    </p>
                    <div class="flex text-yellow-400 mt-4">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <!-- 评价 4 -->
                <div class="bg-gray-50 rounded-xl p-6 shadow-lg">
                    <div class="flex items-center mb-4">
                        <img src="https://www.gemoo-resource.com/focusee/img/review_img4.png" alt="用户头像" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">陈大伟</h4>
                            <p class="text-gray-600 text-sm">软件开发者</p>
                        </div>
                    </div>
                    <p class="text-gray-700">
                        "这将在编辑方面帮助我很多，并提供高质量的视频。感觉很好，资源占用轻量，这很好。
                        我期待将其添加到客户快速指南的工具箱中。"
                    </p>
                    <div class="flex text-yellow-400 mt-4">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <!-- 评价 5 -->
                <div class="bg-gray-50 rounded-xl p-6 shadow-lg">
                    <div class="flex items-center mb-4">
                        <img src="	https://www.gemoo-resource.com/focusee/img/<EMAIL>" alt="用户头像" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">刘小芳</h4>
                            <p class="text-gray-600 text-sm">市场营销</p>
                        </div>
                    </div>
                    <p class="text-gray-700">
                        "这是一个令人惊叹的屏幕录制应用程序，真正有助于视频编辑，创建教程和产品演示视频用于推广和销售。
                        这太棒了，方便且用户友好。"
                    </p>
                    <div class="flex text-yellow-400 mt-4">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <!-- 评价 6 -->
                <div class="bg-gray-50 rounded-xl p-6 shadow-lg">
                    <div class="flex items-center mb-4">
                        <img src="https://www.gemoo-resource.com/focusee/img/<EMAIL>" alt="用户头像" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">赵强</h4>
                            <p class="text-gray-600 text-sm">创业者</p>
                        </div>
                    </div>
                    <p class="text-gray-700">
                        "美丽。这样的时间节省器 - 特别是对于我们这些处于早期/启动阶段的人来说，时间和资源都很宝贵。
                        我自己使用FocuSee，对此非常满意。"
                    </p>
                    <div class="flex text-yellow-400 mt-4">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 保证和FAQ -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- 保证 -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
                    我们的承诺
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-shield-alt text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">30天退款保证</h3>
                        <p class="text-gray-600">不满意？30天内无条件退款</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-infinity text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">终身免费更新</h3>
                        <p class="text-gray-600">一次购买，享受所有未来更新</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-headset text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">24/7 技术支持</h3>
                        <p class="text-gray-600">专业团队随时为您解答问题</p>
                    </div>
                </div>
            </div>

            <!-- FAQ -->
            <div class="max-w-3xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">常见问题</h2>
                <div class="space-y-6">
                    <div class="bg-white rounded-lg p-6 shadow-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">FocuSee 支持哪些操作系统？</h3>
                        <p class="text-gray-600">FocuSee 支持 Windows 10+ 和 macOS 10.13+ 系统，确保在两个平台上都能流畅运行。</p>
                    </div>
                    <div class="bg-white rounded-lg p-6 shadow-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">免费版和专业版有什么区别？</h3>
                        <p class="text-gray-600">免费版提供基础录制功能和720P导出，但带有水印。专业版支持4K导出、无水印、高级编辑功能和优先技术支持。</p>
                    </div>
                    <div class="bg-white rounded-lg p-6 shadow-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">购买后可以在多台设备上使用吗？</h3>
                        <p class="text-gray-600">专业版许可证允许您在最多3台个人设备上使用。企业版提供多用户许可证选项。</p>
                    </div>
                    <div class="bg-white rounded-lg p-6 shadow-lg">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">如何获得技术支持？</h3>
                        <p class="text-gray-600">您可以通过邮件、在线客服或帮助中心获得支持。专业版用户享有优先支持服务。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 立即开始 -->
    <section id="download" class="py-20 gradient-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="mb-8">
                <img src="https://www.gemoo-resource.com/focusee/img/<EMAIL>" alt="FocuSee Logo" class="h-16 mx-auto mb-6">
            </div>
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                立即开始使用自动化后期制作录制您的屏幕
            </h2>
            <p class="text-xl text-white text-opacity-90 mb-8 max-w-2xl mx-auto">
                体验革命性的屏幕录制工具，让您的视频制作变得前所未有的简单
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <a href="/downloads/FocuSee1.1.1.zip"
                   class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-lg text-lg font-semibold transition duration-300 shadow-lg">
                    <i class="fas fa-download mr-2"></i>
                    免费下载
                </a>
                <a href="/trial"
                   class="bg-orange-500 text-white hover:bg-orange-600 px-8 py-4 rounded-lg text-lg font-semibold transition duration-300 shadow-lg">
                    <i class="fas fa-gift mr-2"></i>
                    一个月试用
                </a>
                <a href="#pricing"
                   class="bg-yellow-400 text-gray-900 hover:bg-yellow-300 px-8 py-4 rounded-lg text-lg font-semibold transition duration-300 shadow-lg">
                    <i class="fas fa-crown mr-2"></i>
                    查看价格
                </a>
            </div>
            <p class="text-white text-opacity-75 text-sm">
                支持 Windows 10+ 和 macOS 10.13+ | 限时早鸟优惠
            </p>

            <!-- 特色徽章 -->
            <div class="flex flex-wrap justify-center items-center gap-6 mt-12">
                <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg p-4">
                    <img src="https://focusee.imobie-resource.com/img/featured-on-taaft.png"
                         alt="Product Hunt Featured" class="h-12">
                </div>
                <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg p-4">
                    <img src="https://focusee.imobie-resource.com/img/featured-on-taaft.png"
                         alt="Product Hunt Top Post" class="h-12">
                </div>
                <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg p-4">
                    <img src="https://focusee.imobie-resource.com/img/featured-on-taaft.png"
                         alt="Featured on TAAFT" class="h-12">
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- 公司信息 -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <img src="https://www.gemoo-resource.com/focusee/img/<EMAIL>" alt="FocuSee" class="h-8 mr-3">
                        <span class="text-xl font-bold">FocuSee</span>
                    </div>
                    <p class="text-gray-400 mb-6 max-w-md">
                        FocuSee 是一款革命性的屏幕录制工具，能够自动跟踪鼠标移动，添加动态缩放效果，
                        一键生成专业级产品演示视频。让视频制作变得简单高效。
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-youtube text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>

                <!-- 产品链接 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">产品</h3>
                    <ul class="space-y-2">
                        <li><a href="#features" class="text-gray-400 hover:text-white transition duration-300">功能特色</a></li>
                        <li><a href="#pricing" class="text-gray-400 hover:text-white transition duration-300">价格方案</a></li>
                        <li><a href="#download" class="text-gray-400 hover:text-white transition duration-300">免费下载</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">系统要求</a></li>
                    </ul>
                </div>

                <!-- 支持链接 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">支持</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">帮助中心</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">用户指南</a></li>
                        <li><a href="/contact" class="text-gray-400 hover:text-white transition duration-300">联系我们</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">技术支持</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">
                    © 2024 FocuSee. 保留所有权利. |
                    <a href="#" class="hover:text-white transition duration-300">隐私政策</a> |
                    <a href="#" class="hover:text-white transition duration-300">服务条款</a>
                </p>
            </div>
        </div>
    </footer>

    <!-- 平滑滚动脚本 -->
    <script>
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.classList.add('bg-white', 'shadow-lg');
            } else {
                nav.classList.remove('bg-white', 'shadow-lg');
            }
        });

        // 添加动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有功能卡片
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // 倒计时功能
        function updateCountdown() {
            // 设置倒计时结束时间（3天后）
            const endTime = new Date().getTime() + (3 * 24 * 60 * 60 * 1000);

            function update() {
                const now = new Date().getTime();
                const timeLeft = endTime - now;

                if (timeLeft > 0) {
                    const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                    document.getElementById('days').textContent = days.toString().padStart(2, '0');
                    document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                    document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                    document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
                } else {
                    // 倒计时结束
                    document.getElementById('days').textContent = '00';
                    document.getElementById('hours').textContent = '00';
                    document.getElementById('minutes').textContent = '00';
                    document.getElementById('seconds').textContent = '00';
                }
            }

            update();
            setInterval(update, 1000);
        }

        // 启动倒计时
        updateCountdown();

        // 视频播放控制
        function toggleVideo(button) {
            const video = button.closest('.relative').querySelector('video');
            const icon = button.querySelector('i');

            if (video.paused) {
                video.play();
                icon.className = 'fas fa-pause text-2xl text-gray-800';
            } else {
                video.pause();
                icon.className = 'fas fa-play text-2xl text-gray-800';
            }
        }

        // 视频自动播放优化
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.querySelector('video');
            if (video) {
                // 尝试自动播放
                video.play().catch(function(error) {
                    console.log('自动播放被阻止，用户需要手动播放');
                });

                // 视频加载完成后的处理
                video.addEventListener('loadeddata', function() {
                    console.log('视频加载完成');
                });

                // 视频播放错误处理
                video.addEventListener('error', function() {
                    console.log('视频加载失败，显示后备图片');
                    const container = video.parentElement;
                    video.style.display = 'none';
                    const fallbackImg = video.querySelector('img');
                    if (fallbackImg) {
                        fallbackImg.style.display = 'block';
                    }
                });
            }
        });
    </script>

</body>
</html>
