<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权产品 - FocuSee代理商管理系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
        }

        .layout-container {
            height: 100vh;
        }

        .header {
            background: #fff;
            border-bottom: 1px solid #e6e6e6;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-size: 20px;
            font-weight: 600;
            color: #409EFF;
        }

        .sidebar {
            background: #304156;
            overflow: hidden;
        }

        .main-content {
            background: #f0f2f5;
            padding: 24px;
            overflow-y: auto;
        }

        .content-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e6e6e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .product-grid {
            margin-top: 20px;
        }

        .product-card {
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border: 1px solid #ebeef5;
        }

        .product-card:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .product-card.expired {
            opacity: 0.7;
            border-color: #f56c6c;
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .product-name {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }

        .product-info {
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
        }

        .info-item .label {
            color: #606266;
            font-size: 14px;
            font-weight: 500;
        }

        .info-item .value {
            color: #303133;
            font-weight: 600;
        }

        .info-item .value.warning {
            color: #e6a23c;
        }

        .progress-container {
            flex: 1;
            margin-left: 12px;
        }

        .product-actions {
            display: flex;
            gap: 12px;
            margin-top: 16px;
        }

        .product-actions .el-button {
            flex: 1;
            height: 40px;
            font-size: 14px;
            font-weight: 500;
        }

        .empty-state, .loading-state {
            text-align: center;
            padding: 60px 20px;
        }

        .empty-state .el-empty {
            padding: 40px 0;
        }

        .loading-state .el-skeleton {
            padding: 20px;
        }

        /* 确保按钮样式正确 */
        .el-button {
            height: 32px;
            padding: 8px 15px;
            font-size: 14px;
            border-radius: 4px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .el-button--primary {
            background-color: #409eff;
            border-color: #409eff;
            color: #ffffff;
        }

        .el-button--primary:hover {
            background-color: #66b1ff;
            border-color: #66b1ff;
        }

        .el-button--info {
            background-color: #909399;
            border-color: #909399;
            color: #ffffff;
        }

        .el-button--info:hover {
            background-color: #a6a9ad;
            border-color: #a6a9ad;
        }

        .el-button .el-icon {
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container class="layout-container">
            <!-- 顶部导航 -->
            <el-header class="header" height="60px">
                <div class="logo">
                    <el-icon><Box /></el-icon>
                    FocuSee 代理商管理系统
                </div>
                <div>
                    <el-dropdown @command="handleCommand">
                        <span class="el-dropdown-link">
                            <el-icon><User /></el-icon>
                            <span v-text="agentInfo.company_name || '代理商'"></span>
                            <el-icon class="el-icon--right"><arrow-down /></el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="dashboard">仪表板</el-dropdown-item>
                                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </el-header>

            <el-container>
                <!-- 侧边栏 -->
                <el-aside class="sidebar" width="200px">
                    <el-menu
                        default-active="/agent/products"
                        class="el-menu-vertical"
                        background-color="#304156"
                        text-color="#bfcbd9"
                        active-text-color="#409EFF"
                        @select="navigate">
                        <el-menu-item index="/agent/dashboard">
                            <el-icon><Odometer /></el-icon>
                            <span>仪表板</span>
                        </el-menu-item>
                        <el-menu-item index="/agent/products">
                            <el-icon><Box /></el-icon>
                            <span>授权产品</span>
                        </el-menu-item>
                        <el-menu-item index="/agent/licenses">
                            <el-icon><Key /></el-icon>
                            <span>授权码管理</span>
                        </el-menu-item>
                        <el-menu-item index="/agent/orders">
                            <el-icon><Document /></el-icon>
                            <span>订单管理</span>
                        </el-menu-item>
                        <el-menu-item index="/agent/profile">
                            <el-icon><Setting /></el-icon>
                            <span>个人设置</span>
                        </el-menu-item>
                    </el-menu>
                </el-aside>

                <!-- 主内容区 -->
                <el-main class="main-content">
                    <div class="content-card">
                        <div class="page-header">
                            <h1 class="page-title">授权产品管理</h1>
                            <div class="header-actions">
                                <el-button type="primary" @click="loadProducts" :loading="loading">
                                    <el-icon><Refresh /></el-icon>
                                    刷新数据
                                </el-button>
                            </div>
                        </div>

                        <!-- 产品列表 -->
                        <div class="product-grid" v-if="products && products.length > 0">
                            <el-row :gutter="24">
                                <el-col :span="8" v-for="product in products" :key="product.id">
                                    <el-card class="product-card" :class="{ 'expired': product.is_expired }" shadow="hover">
                                        <template #header>
                                            <div class="product-header">
                                                <span class="product-name" v-text="product.product_name"></span>
                                                <el-tag :type="product.is_expired ? 'danger' : 'success'" size="large">
                                                    <span v-text="product.is_expired ? '已过期' : '正常'"></span>
                                                </el-tag>
                                            </div>
                                        </template>

                                        <div class="product-info">
                                            <div class="info-item">
                                                <span class="label">产品代码</span>
                                                <span class="value" v-text="product.product_code"></span>
                                            </div>

                                            <div class="info-item">
                                                <span class="label">最大授权数</span>
                                                <span class="value" v-text="product.max_licenses"></span>
                                            </div>

                                            <div class="info-item">
                                                <span class="label">已使用</span>
                                                <span class="value" v-text="product.used_licenses"></span>
                                            </div>

                                            <div class="info-item">
                                                <span class="label">剩余数量</span>
                                                <span class="value" :class="{ 'warning': product.remaining_licenses < 10 }" v-text="product.remaining_licenses"></span>
                                            </div>

                                            <div class="info-item">
                                                <span class="label">使用率</span>
                                                <div class="progress-container">
                                                    <el-progress
                                                        :percentage="Math.round((product.used_licenses / product.max_licenses) * 100)"
                                                        :color="getProgressColor(product.used_licenses / product.max_licenses)"
                                                        :stroke-width="10"
                                                        :show-text="true">
                                                    </el-progress>
                                                </div>
                                            </div>

                                            <div class="info-item" v-if="product.expire_date">
                                                <span class="label">授权到期</span>
                                                <span class="value" v-text="formatDate(product.expire_date)"></span>
                                            </div>

                                            <div class="info-item">
                                                <span class="label">授权时间</span>
                                                <span class="value" v-text="formatDate(product.created_at)"></span>
                                            </div>
                                        </div>

                                        <div class="product-actions">
                                            <el-button
                                                type="primary"
                                                @click="navigate('/agent/licenses?product_id=' + product.product_id)"
                                                :disabled="product.remaining_licenses <= 0 || product.is_expired">
                                                <el-icon><Plus /></el-icon>
                                                生成授权码
                                            </el-button>

                                            <el-button
                                                type="info"
                                                @click="navigate('/agent/licenses?product_id=' + product.product_id)">
                                                <el-icon><View /></el-icon>
                                                查看授权码
                                            </el-button>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 空状态 -->
                        <div v-if="(!products || products.length === 0) && !loading" class="empty-state">
                            <el-empty description="暂无授权产品">
                                <el-button type="primary" @click="loadProducts">刷新</el-button>
                            </el-empty>
                        </div>

                        <!-- 加载状态 -->
                        <div v-if="loading" class="loading-state">
                            <el-skeleton :rows="3" animated />
                        </div>
                    </div>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const loading = ref(false);
                const products = ref([]);
                const agentInfo = ref({});

                // 检查认证状态
                const checkAuth = () => {
                    const token = localStorage.getItem('agent_token');
                    const info = localStorage.getItem('agent_info');

                    if (!token) {
                        window.location.href = '/agent/login';
                        return false;
                    }

                    if (info) {
                        agentInfo.value = JSON.parse(info);
                    }

                    return true;
                };

                // 加载授权产品
                const loadProducts = async () => {
                    if (!checkAuth()) return;

                    loading.value = true;
                    try {
                        const token = localStorage.getItem('agent_token');
                        const response = await fetch('/api/agent/dashboard/authorized-products', {
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            products.value = data;
                            ElMessage.success(`成功加载 ${data.length} 个授权产品`);
                        } else if (response.status === 401) {
                            localStorage.removeItem('agent_token');
                            localStorage.removeItem('agent_info');
                            window.location.href = '/agent/login';
                        } else {
                            const error = await response.json();
                            ElMessage.error(error.detail || '加载失败');
                        }
                    } catch (error) {
                        console.error('Failed to load products:', error);
                        ElMessage.error('网络错误');
                    } finally {
                        loading.value = false;
                    }
                };

                // 导航到其他页面
                const navigate = (path) => {
                    window.location.href = path;
                };

                // 处理下拉菜单命令
                const handleCommand = (command) => {
                    switch (command) {
                        case 'dashboard':
                            navigate('/agent/dashboard');
                            break;
                        case 'profile':
                            navigate('/agent/profile');
                            break;
                        case 'logout':
                            localStorage.removeItem('agent_token');
                            localStorage.removeItem('agent_info');
                            window.location.href = '/agent/login';
                            break;
                    }
                };

                // 获取进度条颜色
                const getProgressColor = (percentage) => {
                    if (percentage < 0.5) return '#67c23a';
                    if (percentage < 0.8) return '#e6a23c';
                    return '#f56c6c';
                };

                // 格式化日期
                const formatDate = (dateString) => {
                    if (!dateString) return '-';
                    return new Date(dateString).toLocaleString('zh-CN');
                };

                onMounted(() => {
                    checkAuth();
                    loadProducts();
                });

                return {
                    loading,
                    products,
                    agentInfo,
                    loadProducts,
                    navigate,
                    handleCommand,
                    getProgressColor,
                    formatDate
                };
            }
        });

        // 注册 Element Plus 图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>