from fastapi import APIRouter, Depends, HTTPException, Request, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from app.database import get_db
from app.services.download_service import DownloadService
from app.config import settings
import logging
import os

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/downloads/{filename}")
@router.head("/downloads/{filename}")
async def download_file(
    filename: str,
    user: str = Query(..., description="用户ID"),
    key: str = Query(..., description="授权码"),
    request: Request = None,
    db: Session = Depends(get_db)
):
    """文件下载接口"""
    try:
        # 验证下载权限
        has_permission = DownloadService.verify_download_permission(db, user, key)
        if not has_permission:
            raise HTTPException(status_code=403, detail="Access denied: Invalid credentials")
        
        # 获取文件路径
        file_path = DownloadService.get_file_path(filename, settings.downloads_dir)
        if not file_path:
            raise HTTPException(status_code=404, detail="File not found")
        
        # 记录下载行为
        client_ip = request.client.host if request else None
        user_agent = request.headers.get("user-agent") if request else None
        
        DownloadService.log_download(db, user, key, filename, client_ip, user_agent)
        
        # 返回文件
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/octet-stream'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in download_file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")

@router.get("/downloads")
async def list_available_files():
    """列出可用的下载文件"""
    try:
        if not os.path.exists(settings.downloads_dir):
            return {"files": []}
        
        files = []
        for filename in os.listdir(settings.downloads_dir):
            file_path = os.path.join(settings.downloads_dir, filename)
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                files.append({
                    "filename": filename,
                    "size": file_size
                })
        
        return {"files": files}
        
    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")
