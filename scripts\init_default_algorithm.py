#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化默认算法
将当前使用的HMAC-SHA256算法作为默认算法插入到数据库中
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app.models.activation_algorithm import ActivationAlgorithm
from app.models import Base

# 默认算法代码
DEFAULT_ALGORITHM_CODE = '''# HMAC-SHA256 激活码生成算法
# 这是系统默认使用的算法，基于HMAC-SHA256和Base64编码

import hashlib
import hmac
import time
import base64

# 使用与原系统相同的密钥
master_key = "FocuSee_Master_2025_Secret_Key_V1"
hmac_key = hashlib.sha256((master_key + "_HMAC").encode()).digest()[:32]

try:
    current_time = int(time.time())
    machine_short = machine_id.upper().strip()[:8]
    time_short = str(current_time)[-4:]

    # 构造数据：机器码|天数|时间戳
    compact_data = f"{machine_short}|{days}|{time_short}"

    # 计算校验码
    hmac_hash = hmac.new(hmac_key, compact_data.encode(), hashlib.sha256).hexdigest()[:8]

    # 最终数据
    final_data = compact_data + "|" + hmac_hash

    # Base64编码并格式化
    encoded = base64.b64encode(final_data.encode()).decode().rstrip('=')
    activation_code = '-'.join([encoded[i:i+4] for i in range(0, len(encoded), 4)])

except Exception as e:
    activation_code = None
'''

def init_default_algorithm():
    """初始化默认算法"""
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    try:
        # 检查是否已存在默认算法
        existing = db.query(ActivationAlgorithm).filter(
            ActivationAlgorithm.name == "HMAC-SHA256 默认算法"
        ).first()
        
        if existing:
            print("默认算法已存在，跳过初始化")
            return
        
        # 创建默认算法
        default_algorithm = ActivationAlgorithm(
            name="HMAC-SHA256 默认算法",
            description="系统默认使用的HMAC-SHA256激活码生成算法，提供高安全性和兼容性",
            algorithm_code=DEFAULT_ALGORITHM_CODE,
            is_active=True,
            is_default=True,
            version="1.0",
            author="FocuSee System"
        )
        
        db.add(default_algorithm)
        db.commit()
        
        print("默认算法初始化成功！")
        print(f"算法名称: {default_algorithm.name}")
        print(f"算法版本: {default_algorithm.version}")
        print(f"算法作者: {default_algorithm.author}")
        
    except Exception as e:
        print(f"初始化默认算法失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_default_algorithm()
