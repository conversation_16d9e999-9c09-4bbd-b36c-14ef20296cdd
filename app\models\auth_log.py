from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Text, Boolean, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum

class AuthAction(enum.Enum):
    VERIFY_ORDER = "verify_order"           # 订单号验证
    VERIFY_LICENSE = "verify_license"       # 授权码验证
    VERIFY_PRODUCT = "verify_product"       # 产品类型验证
    VERIFY_MIXED = "verify_mixed"           # 混合验证
    ACTIVATE_LICENSE = "activate_license"   # 激活授权
    DEACTIVATE_LICENSE = "deactivate_license" # 停用授权
    EXTEND_LICENSE = "extend_license"       # 延期授权
    UPDATE_QUOTA = "update_quota"           # 更新配额
    API_CALL = "api_call"                   # API调用

class AuthResult(enum.Enum):
    SUCCESS = "success"                     # 成功
    FAILED = "failed"                       # 失败
    EXPIRED = "expired"                     # 已过期
    QUOTA_EXCEEDED = "quota_exceeded"       # 配额超限
    INVALID_LICENSE = "invalid_license"     # 无效授权
    INVALID_PRODUCT = "invalid_product"     # 无效产品
    UNAUTHORIZED = "unauthorized"           # 未授权

class AuthLog(Base):
    __tablename__ = "auth_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    action = Column(Enum(AuthAction), nullable=False, comment="操作类型")
    result = Column(Enum(AuthResult), nullable=False, comment="操作结果")
    
    # 授权相关信息
    license_id = Column(Integer, ForeignKey("licenses.id"), nullable=True, comment="授权码ID")
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=True, comment="订单ID")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=True, comment="产品ID")
    
    # 用户和请求信息
    user_id = Column(String(100), nullable=True, comment="用户ID")
    ip_address = Column(String(45), comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    
    # 验证参数和结果
    request_params = Column(Text, comment="请求参数（JSON格式）")
    response_data = Column(Text, comment="响应数据（JSON格式）")
    error_message = Column(Text, comment="错误信息")
    
    # 配额信息
    quota_before = Column(Integer, comment="操作前配额")
    quota_after = Column(Integer, comment="操作后配额")
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关联关系
    license = relationship("License", backref="auth_logs")
    order = relationship("Order", backref="auth_logs")
    product = relationship("Product", backref="auth_logs")
    
    def __repr__(self):
        return f"<AuthLog(action='{self.action.value}', result='{self.result.value}', user_id='{self.user_id}')>"
