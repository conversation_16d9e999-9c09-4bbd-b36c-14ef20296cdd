{% extends "agent/base.html" %}

{% block title %}仪表板{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<div>
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
            <el-card>
                <el-statistic title="授权产品" :value="stats.authorizedProducts">
                    <template #suffix>
                        <el-icon><Box /></el-icon>
                    </template>
                </el-statistic>
            </el-card>
        </el-col>
        
        <el-col :span="6">
            <el-card>
                <el-statistic title="总授权码" :value="stats.totalLicenses">
                    <template #suffix>
                        <el-icon><Key /></el-icon>
                    </template>
                </el-statistic>
            </el-card>
        </el-col>
        
        <el-col :span="6">
            <el-card>
                <el-statistic title="激活授权" :value="stats.activeLicenses">
                    <template #suffix>
                        <el-icon><Check /></el-icon>
                    </template>
                </el-statistic>
            </el-card>
        </el-col>
        
        <el-col :span="6">
            <el-card>
                <el-statistic title="总订单" :value="stats.totalOrders">
                    <template #suffix>
                        <el-icon><Document /></el-icon>
                    </template>
                </el-statistic>
            </el-card>
        </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="12">
            <el-card>
                <template #header>
                    <span>快速操作</span>
                </template>
                <div style="text-align: center;">
                    <el-button type="primary" @click="navigate('/agent/licenses')" style="margin: 5px;">
                        <el-icon><Plus /></el-icon>
                        生成授权码
                    </el-button>
                    <el-button type="success" @click="navigate('/agent/products')" style="margin: 5px;">
                        <el-icon><View /></el-icon>
                        查看产品
                    </el-button>
                </div>
            </el-card>
        </el-col>
        
        <el-col :span="12">
            <el-card>
                <template #header>
                    <span>最近活动</span>
                </template>
                <div v-if="recentActivities.length === 0" style="text-align: center; color: #999;">
                    暂无最近活动
                </div>
                <div v-else>
                    <div v-for="activity in recentActivities" :key="activity.id" style="margin-bottom: 10px;">
                        <div>{% raw %}{{ activity.title }}{% endraw %}</div>
                        <div style="font-size: 12px; color: #999;">{% raw %}{{ formatDate(activity.time) }}{% endraw %}</div>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>

    <!-- 授权产品 -->
    <el-row>
        <el-col :span="24">
            <el-card>
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>授权产品</span>
                        <el-button type="text" @click="navigate('/agent/products')">查看全部</el-button>
                    </div>
                </template>
                <el-table :data="authorizedProducts" v-loading="loading">
                    <el-table-column prop="name" label="产品名称"></el-table-column>
                    <el-table-column prop="max_licenses" label="最大授权数" width="120"></el-table-column>
                    <el-table-column prop="used_licenses" label="已使用" width="100"></el-table-column>
                    <el-table-column label="使用率" width="150">
                        <template #default="scope">
                            <el-progress 
                                :percentage="getUsagePercentage(scope.row.used_licenses, scope.row.max_licenses)"
                                :color="getUsageColor(getUsagePercentage(scope.row.used_licenses, scope.row.max_licenses) / 100)">
                            </el-progress>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" width="100">
                        <template #default="scope">
                            <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                                {% raw %}{{ scope.row.is_active ? '正常' : '已停用' }}{% endraw %}
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </el-col>
    </el-row>
</div>
{% endblock %}

{% block data %}
stats: {
    authorizedProducts: 0,
    totalLicenses: 0,
    activeLicenses: 0,
    totalOrders: 0
},
authorizedProducts: [],
recentActivities: [],
loading: false
{% endblock %}

{% block methods %}
async loadStats() {
    try {
        const token = localStorage.getItem('agent_token');
        if (!token) {
            window.location.href = '/agent/login';
            return;
        }
        
        const response = await fetch('/api/agent/dashboard/stats', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            this.stats.authorizedProducts = data.authorized_products;
            this.stats.totalLicenses = data.total_licenses;
            this.stats.activeLicenses = data.active_licenses;
            this.stats.totalOrders = data.total_orders;
        } else if (response.status === 401) {
            localStorage.removeItem('agent_token');
            window.location.href = '/agent/login';
        }
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
},

async loadAuthorizedProducts() {
    this.loading = true;
    try {
        const token = localStorage.getItem('agent_token');
        if (!token) return;
        
        const response = await fetch('/api/agent/dashboard/authorized-products', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            this.authorizedProducts = data.map(item => ({
                id: item.id,
                name: item.product_name,
                max_licenses: item.max_licenses,
                used_licenses: item.used_licenses,
                is_active: item.is_active && !item.is_expired
            }));
        }
    } catch (error) {
        console.error('Failed to load authorized products:', error);
    } finally {
        this.loading = false;
    }
},

async loadRecentActivities() {
    try {
        const token = localStorage.getItem('agent_token');
        if (!token) return;
        
        const response = await fetch('/api/agent/dashboard/recent-activities?limit=5', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            this.recentActivities = data;
        }
    } catch (error) {
        console.error('Failed to load recent activities:', error);
    }
},

formatDate(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('zh-CN');
},

getUsagePercentage(used, max) {
    return max > 0 ? Math.round((used / max) * 100) : 0;
},

getUsageColor(percentage) {
    if (percentage < 0.5) return '#67c23a';
    if (percentage < 0.8) return '#e6a23c';
    return '#f56c6c';
}
{% endblock %}

{% block mounted %}
this.loadStats();
this.loadAuthorizedProducts();
this.loadRecentActivities();
{% endblock %}
