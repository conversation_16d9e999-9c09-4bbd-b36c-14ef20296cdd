from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime
from sqlalchemy.sql import func
from app.database import Base

class Role(Base):
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False, comment="角色名称")
    code = Column(String(20), unique=True, nullable=False, comment="角色代码")
    description = Column(Text, comment="角色描述")
    permissions = Column(Text, comment="权限列表（JSON格式）")
    is_system = Column(Boolean, default=False, comment="是否系统角色")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<Role(name='{self.name}', code='{self.code}', is_active={self.is_active})>"
