from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models import AgentProductAuth, Agent, Product
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class AgentProductAuthService:
    """代理商产品授权服务"""
    
    @staticmethod
    def create_agent_product_auth(
        db: Session, 
        agent_id: int, 
        product_id: int, 
        max_licenses: int,
        expire_date: Optional[datetime] = None
    ) -> Optional[AgentProductAuth]:
        """为代理商创建产品授权"""
        try:
            # 检查代理商是否存在
            agent = db.query(Agent).filter(Agent.id == agent_id).first()
            if not agent:
                logger.error(f"Agent {agent_id} not found")
                return None
            
            # 检查产品是否存在
            product = db.query(Product).filter(Product.id == product_id).first()
            if not product:
                logger.error(f"Product {product_id} not found")
                return None
            
            # 检查是否已经存在授权
            existing_auth = db.query(AgentProductAuth).filter(
                and_(
                    AgentProductAuth.agent_id == agent_id,
                    AgentProductAuth.product_id == product_id,
                    AgentProductAuth.is_active == True
                )
            ).first()
            
            if existing_auth:
                logger.error(f"Agent {agent_id} already has authorization for product {product_id}")
                return None
            
            # 创建授权
            auth = AgentProductAuth(
                agent_id=agent_id,
                product_id=product_id,
                max_licenses=max_licenses,
                used_licenses=0,
                expire_date=expire_date,
                is_active=True
            )
            
            db.add(auth)
            db.commit()
            db.refresh(auth)
            
            logger.info(f"Created product authorization for agent {agent_id}, product {product_id}")
            return auth
            
        except Exception as e:
            logger.error(f"Error creating agent product authorization: {str(e)}")
            db.rollback()
            return None
    
    @staticmethod
    def get_agent_product_auths(db: Session, agent_id: int) -> List[AgentProductAuth]:
        """获取代理商的产品授权列表"""
        try:
            from sqlalchemy.orm import joinedload
            return db.query(AgentProductAuth).options(
                joinedload(AgentProductAuth.product)
            ).filter(
                AgentProductAuth.agent_id == agent_id
            ).all()

        except Exception as e:
            logger.error(f"Error getting agent product auths: {str(e)}")
            return []
    
    @staticmethod
    def get_agent_product_auth(db: Session, agent_id: int, product_id: int) -> Optional[AgentProductAuth]:
        """获取代理商特定产品的授权"""
        try:
            return db.query(AgentProductAuth).filter(
                and_(
                    AgentProductAuth.agent_id == agent_id,
                    AgentProductAuth.product_id == product_id,
                    AgentProductAuth.is_active == True
                )
            ).first()
            
        except Exception as e:
            logger.error(f"Error getting agent product auth: {str(e)}")
            return None
    
    @staticmethod
    def update_agent_product_auth(
        db: Session,
        auth_id: int,
        max_licenses: Optional[int] = None,
        expire_date: Optional[datetime] = None,
        is_active: Optional[bool] = None
    ) -> Optional[AgentProductAuth]:
        """更新代理商产品授权"""
        try:
            auth = db.query(AgentProductAuth).filter(AgentProductAuth.id == auth_id).first()
            if not auth:
                logger.error(f"Agent product auth {auth_id} not found")
                return None

            if max_licenses is not None:
                auth.max_licenses = max_licenses

            if expire_date is not None:
                auth.expire_date = expire_date

            if is_active is not None:
                auth.is_active = is_active

            db.commit()
            db.refresh(auth)

            logger.info(f"Updated agent product authorization {auth_id}")
            return auth

        except Exception as e:
            logger.error(f"Error updating agent product authorization: {str(e)}")
            db.rollback()
            return None
    
    @staticmethod
    def delete_agent_product_auth(db: Session, auth_id: int) -> bool:
        """删除代理商产品授权"""
        try:
            from app.models.license import License

            auth = db.query(AgentProductAuth).filter(AgentProductAuth.id == auth_id).first()
            if not auth:
                logger.error(f"Agent product auth {auth_id} not found")
                return False

            # 检查是否有相关的授权码
            active_licenses = db.query(License).filter(
                License.agent_id == auth.agent_id,
                License.product_id == auth.product_id,
                License.status.in_(['active', 'inactive'])  # 只检查活跃和未激活的授权码
            ).count()

            if active_licenses > 0:
                logger.error(f"Cannot delete agent product auth {auth_id}: has {active_licenses} active licenses")
                return False

            # 删除授权记录
            db.delete(auth)
            db.commit()

            logger.info(f"Deleted agent product authorization {auth_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting agent product authorization: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def increment_used_licenses(db: Session, agent_id: int, product_id: int) -> bool:
        """增加已使用的授权数量"""
        try:
            auth = db.query(AgentProductAuth).filter(
                and_(
                    AgentProductAuth.agent_id == agent_id,
                    AgentProductAuth.product_id == product_id,
                    AgentProductAuth.is_active == True
                )
            ).first()
            
            if not auth:
                logger.error(f"Agent product auth not found for agent {agent_id}, product {product_id}")
                return False
            
            if auth.used_licenses >= auth.max_licenses:
                logger.error(f"Agent {agent_id} has reached license limit for product {product_id}")
                return False
            
            auth.used_licenses += 1
            db.commit()
            
            logger.info(f"Incremented used licenses for agent {agent_id}, product {product_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error incrementing used licenses: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def decrement_used_licenses(db: Session, agent_id: int, product_id: int) -> bool:
        """减少已使用的授权数量"""
        try:
            auth = db.query(AgentProductAuth).filter(
                and_(
                    AgentProductAuth.agent_id == agent_id,
                    AgentProductAuth.product_id == product_id,
                    AgentProductAuth.is_active == True
                )
            ).first()
            
            if not auth:
                logger.error(f"Agent product auth not found for agent {agent_id}, product {product_id}")
                return False
            
            if auth.used_licenses <= 0:
                logger.warning(f"Agent {agent_id} used licenses already at 0 for product {product_id}")
                return False
            
            auth.used_licenses -= 1
            db.commit()
            
            logger.info(f"Decremented used licenses for agent {agent_id}, product {product_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error decrementing used licenses: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def check_license_availability(db: Session, agent_id: int, product_id: int) -> bool:
        """检查代理商是否还有可用的授权数量"""
        try:
            auth = db.query(AgentProductAuth).filter(
                and_(
                    AgentProductAuth.agent_id == agent_id,
                    AgentProductAuth.product_id == product_id,
                    AgentProductAuth.is_active == True
                )
            ).first()
            
            if not auth:
                return False
            
            # 检查是否过期
            if auth.expire_date and auth.expire_date < datetime.now():
                return False
            
            # 检查是否还有可用授权
            return auth.used_licenses < auth.max_licenses
            
        except Exception as e:
            logger.error(f"Error checking license availability: {str(e)}")
            return False
    
    @staticmethod
    def get_all_agent_product_auths(db: Session, skip: int = 0, limit: int = 100) -> List[AgentProductAuth]:
        """获取所有代理商产品授权（管理员用）"""
        try:
            return db.query(AgentProductAuth).filter(
                AgentProductAuth.is_active == True
            ).offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error getting all agent product auths: {str(e)}")
            return []
