#!/usr/bin/env python3
"""
测试模板渲染
"""

from fastapi.templating import Jinja2Templates
from fastapi import Request
import os

def test_template():
    """测试模板渲染"""
    print("🔍 测试模板渲染...")
    
    try:
        # 检查模板目录
        template_dir = "templates"
        if os.path.exists(template_dir):
            print(f"✅ 模板目录存在: {template_dir}")
        else:
            print(f"❌ 模板目录不存在: {template_dir}")
            return False
        
        # 检查模板文件
        template_file = "templates/admin/payment_debug.html"
        if os.path.exists(template_file):
            print(f"✅ 模板文件存在: {template_file}")
        else:
            print(f"❌ 模板文件不存在: {template_file}")
            return False
        
        # 测试模板初始化
        templates = Jinja2Templates(directory="templates")
        print("✅ 模板引擎初始化成功")
        
        # 检查模板内容
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"✅ 模板文件大小: {len(content)} 字符")
            
            # 检查关键内容
            if "支付调试" in content:
                print("✅ 模板包含支付调试内容")
            if "Vue.js" in content:
                print("✅ 模板包含Vue.js")
            if "Element UI" in content:
                print("✅ 模板包含Element UI")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_template()
    if success:
        print("\n🎉 模板测试通过！")
    else:
        print("\n❌ 模板测试失败")
