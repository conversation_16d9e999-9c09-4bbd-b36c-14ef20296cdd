{% extends "admin/base.html" %}

{% block title %}下载统计 - FocuSee 管理系统{% endblock %}
{% block page_title %}下载统计{% endblock %}

{% block data %}
    downloads: [
        {% for download in downloads %}
        {
            id: {{ download.id }},
            user_id: "{{ download.user_id }}",
            license_key: "{{ download.license_key }}",
            system_id: "{{ download.system_id or '' }}",
            computer_name: "{{ download.computer_name or '' }}",
            ip_address: "{{ download.ip_address or '' }}",
            file_name: "{{ download.file_name or '' }}",
            download_time: "{{ download.download_time.isoformat() if download.download_time else '' }}",
            user_agent: "{{ download.user_agent or '' }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    loading: false,
    searchForm: {
        user_id: '',
        file_name: '',
        date_range: []
    },
    currentPage: 1,
    pageSize: 20
{% endblock %}

{% block content %}
<div class="content-card">
    <!-- 页面标题和操作栏 -->
    <div style="margin-bottom: 20px; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
        <el-row :gutter="20" style="align-items: center;">
            <el-col :span="18">
                <h3 style="margin: 0 0 10px 0; color: #303133;">
                    <el-icon><Download /></el-icon>
                    下载统计管理
                </h3>
                <p style="margin: 0; color: #606266;">
                    查看和管理所有产品的下载记录
                </p>
            </el-col>
            <el-col :span="6" style="text-align: right;">
                <el-button type="primary" @click="refreshDownloads">
                    <el-icon><Refresh /></el-icon>
                    刷新数据
                </el-button>
                <el-button @click="exportDownloads">
                    <el-icon><Download /></el-icon>
                    导出数据
                </el-button>
            </el-col>
        </el-row>
    </div>

    <!-- 搜索栏 -->
    <el-form :model="searchForm" :inline="true" style="margin-bottom: 20px;">
        <el-form-item label="用户ID">
            <el-input v-model="searchForm.user_id" placeholder="请输入用户ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="文件名">
            <el-input v-model="searchForm.file_name" placeholder="请输入文件名" clearable></el-input>
        </el-form-item>
        <el-form-item label="日期范围">
            <el-date-picker
                v-model="searchForm.date_range"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD">
            </el-date-picker>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="searchDownloads">
                <el-icon><Search /></el-icon>
                搜索
            </el-button>
            <el-button @click="resetSearch">
                <el-icon><Refresh /></el-icon>
                重置
            </el-button>
        </el-form-item>
    </el-form>
    
    <!-- 统计信息 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
            <el-statistic title="总下载次数" :value="filteredDownloads ? filteredDownloads.length : 0">
                <template #suffix>
                    <el-icon><Download /></el-icon>
                </template>
            </el-statistic>
        </el-col>
        <el-col :span="6">
            <el-statistic title="独立用户数" :value="uniqueUsers || 0">
                <template #suffix>
                    <el-icon><User /></el-icon>
                </template>
            </el-statistic>
        </el-col>
        <el-col :span="6">
            <div style="text-align: center;">
                <div style="font-size: 14px; color: #909399; margin-bottom: 8px;">热门文件</div>
                <div style="font-size: 24px; font-weight: bold; color: #303133;">
                    {% raw %}{{ popularFile || '-' }}{% endraw %}
                    <el-icon style="margin-left: 8px; color: #f56c6c;"><Star /></el-icon>
                </div>
            </div>
        </el-col>
        <el-col :span="6">
            <el-statistic title="今日下载" :value="todayDownloads || 0">
                <template #suffix>
                    <el-icon><Calendar /></el-icon>
                </template>
            </el-statistic>
        </el-col>
    </el-row>
    
    <!-- 下载记录表格 -->
    <el-table :data="paginatedDownloads" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="user_id" label="用户ID" width="120"></el-table-column>
        <el-table-column prop="file_name" label="文件名" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="ip_address" label="IP地址" width="130"></el-table-column>
        <el-table-column prop="computer_name" label="计算机名" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="system_id" label="系统ID" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="download_time" label="下载时间" width="180">
            <template #default="scope">
                {% raw %}{{ formatTime(scope.row.download_time) }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column prop="user_agent" label="用户代理" show-overflow-tooltip>
            <template #default="scope">
                {% raw %}{{ scope.row.user_agent || '-' }}{% endraw %}
            </template>
        </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div style="margin-top: 20px; text-align: center;">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredDownloads ? filteredDownloads.length : 0"
            layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
    </div>
</div>
{% endblock %}

{% block methods %}
formatTime(timeStr) {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
},
searchDownloads() {
    // 搜索功能现在通过计算属性自动实现
    this.currentPage = 1;
},
resetSearch() {
    this.searchForm = {
        user_id: '',
        file_name: '',
        date_range: []
    };
    this.currentPage = 1;
},
refreshDownloads() {
    window.location.reload();
},
exportDownloads() {
    // 导出下载数据功能
    const csvContent = this.generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `downloads_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
},
generateCSV() {
    const headers = ['ID', '用户ID', '许可证密钥', '系统ID', '计算机名', 'IP地址', '文件名', '下载时间', '用户代理'];
    const rows = this.filteredDownloads.map(download => [
        download.id,
        download.user_id,
        download.license_key,
        download.system_id,
        download.computer_name,
        download.ip_address,
        download.file_name,
        download.download_time,
        download.user_agent
    ]);

    const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field || ''}"`).join(','))
        .join('\n');

    return csvContent;
}
{% endblock %}



{% block computed %}
filteredDownloads() {
    if (!this.downloads || !Array.isArray(this.downloads)) {
        return [];
    }

    return this.downloads.filter(download => {
        let match = true;

        // 用户ID过滤
        if (this.searchForm.user_id && !download.user_id.includes(this.searchForm.user_id)) {
            match = false;
        }

        // 文件名过滤
        if (this.searchForm.file_name && !download.file_name.includes(this.searchForm.file_name)) {
            match = false;
        }

        // 日期范围过滤
        if (this.searchForm.date_range && this.searchForm.date_range.length === 2) {
            const downloadDate = new Date(download.download_time).toISOString().split('T')[0];
            if (downloadDate < this.searchForm.date_range[0] || downloadDate > this.searchForm.date_range[1]) {
                match = false;
            }
        }

        return match;
    });
},
uniqueUsers() {
    if (!this.filteredDownloads || !Array.isArray(this.filteredDownloads)) {
        return 0;
    }
    const users = new Set(this.filteredDownloads.map(d => d.user_id));
    return users.size;
},
popularFile() {
    if (!this.filteredDownloads || !Array.isArray(this.filteredDownloads) || this.filteredDownloads.length === 0) {
        return '-';
    }
    const fileCount = {};
    this.filteredDownloads.forEach(d => {
        fileCount[d.file_name] = (fileCount[d.file_name] || 0) + 1;
    });
    const popular = Object.keys(fileCount).reduce((a, b) => fileCount[a] > fileCount[b] ? a : b, '');
    return popular || '-';
},
todayDownloads() {
    if (!this.filteredDownloads || !Array.isArray(this.filteredDownloads)) {
        return 0;
    }
    const today = new Date().toISOString().split('T')[0];
    return this.filteredDownloads.filter(d => {
        const downloadDate = new Date(d.download_time).toISOString().split('T')[0];
        return downloadDate === today;
    }).length;
},
paginatedDownloads() {
    if (!this.filteredDownloads || !Array.isArray(this.filteredDownloads)) {
        return [];
    }
    const start = (this.currentPage - 1) * this.pageSize;
    const end = start + this.pageSize;
    return this.filteredDownloads.slice(start, end);
}
{% endblock %}

{% block mounted %}
// 计算统计数据
{% endblock %}
