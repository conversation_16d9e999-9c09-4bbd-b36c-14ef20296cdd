from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from app.database import get_db
from app.models.agent import Agent
from app.models.order import OrderStatus
from app.services.order_service import OrderService
from app.api.agent_auth import get_current_agent_user
from app.schemas.order import OrderCreate, OrderUpdate, OrderResponse
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/")
async def get_agent_orders(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    status: Optional[OrderStatus] = Query(None),
    product_id: Optional[int] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_agent: Agent = Depends(get_current_agent_user),
    db: Session = Depends(get_db)
):
    """获取代理商的订单列表"""
    try:
        skip = (page - 1) * size
        
        # 处理日期范围
        start_datetime = None
        end_datetime = None
        if start_date:
            start_datetime = datetime.strptime(f"{start_date} 00:00:00", "%Y-%m-%d %H:%M:%S")
        if end_date:
            end_datetime = datetime.strptime(f"{end_date} 23:59:59", "%Y-%m-%d %H:%M:%S")
        
        # 获取订单列表
        orders = OrderService.get_agent_orders(
            db, 
            skip=skip, 
            limit=size, 
            agent_id=current_agent.id,
            status=status,
            product_id=product_id,
            start_date=start_datetime,
            end_date=end_datetime,
            search=search
        )
        
        # 获取总数
        total = OrderService.get_agent_orders_count(
            db,
            agent_id=current_agent.id,
            status=status,
            product_id=product_id,
            start_date=start_datetime,
            end_date=end_datetime,
            search=search
        )
        
        # 格式化订单数据
        formatted_orders = []
        for order in orders:
            formatted_orders.append({
                "id": order.id,
                "order_number": order.order_number,
                "product_id": order.product_id,
                "product_name": order.product.name if order.product else None,
                "product_code": order.product.code if order.product else None,
                "quantity": order.quantity,
                "unit_price": float(order.unit_price) if order.unit_price else 0,
                "total_price": float(order.total_price) if order.total_price else 0,
                "status": order.status.value if order.status else "unknown",
                "expire_date": order.expire_date.isoformat() if order.expire_date else None,
                "customer_info": order.customer_info,
                "notes": order.notes,
                "created_at": order.created_at.isoformat() if order.created_at else None,
                "updated_at": order.updated_at.isoformat() if order.updated_at else None
            })
        
        return {
            "items": formatted_orders,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }
        
    except Exception as e:
        logger.error(f"Error getting agent orders: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get orders"
        )

@router.get("/stats")
async def get_agent_order_stats(
    current_agent: Agent = Depends(get_current_agent_user),
    db: Session = Depends(get_db)
):
    """获取代理商订单统计"""
    try:
        # 获取各状态的订单数量
        total_orders = OrderService.get_order_count(db, agent_id=current_agent.id)
        pending_orders = OrderService.get_order_count(db, agent_id=current_agent.id, status=OrderStatus.PENDING)
        completed_orders = OrderService.get_order_count(db, agent_id=current_agent.id, status=OrderStatus.COMPLETED)
        cancelled_orders = OrderService.get_order_count(db, agent_id=current_agent.id, status=OrderStatus.CANCELLED)
        
        # 计算总金额（只计算已完成的订单）
        completed_order_list = OrderService.get_orders(db, 0, 1000, agent_id=current_agent.id, status=OrderStatus.COMPLETED)
        total_revenue = sum(float(order.total_price) for order in completed_order_list if order.total_price)
        
        return {
            "total_orders": total_orders,
            "pending_orders": pending_orders,
            "completed_orders": completed_orders,
            "cancelled_orders": cancelled_orders,
            "total_revenue": total_revenue
        }
        
    except Exception as e:
        logger.error(f"Error getting agent order stats: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get order statistics"
        )

@router.get("/{order_id}")
async def get_agent_order(
    order_id: int,
    current_agent: Agent = Depends(get_current_agent_user),
    db: Session = Depends(get_db)
):
    """获取代理商的特定订单详情"""
    try:
        order = OrderService.get_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )
        
        # 检查订单是否属于当前代理商
        if order.agent_id != current_agent.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return {
            "id": order.id,
            "order_number": order.order_number,
            "product_name": order.product.name if order.product else None,
            "product_code": order.product.code if order.product else None,
            "quantity": order.quantity,
            "unit_price": float(order.unit_price) if order.unit_price else 0,
            "total_price": float(order.total_price) if order.total_price else 0,
            "status": order.status.value if order.status else "unknown",
            "customer_info": order.customer_info,
            "notes": order.notes,
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "updated_at": order.updated_at.isoformat() if order.updated_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent order {order_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get order"
        )

@router.post("/", response_model=OrderResponse, status_code=status.HTTP_201_CREATED)
async def create_agent_order(
    order_data: OrderCreate,
    current_agent: Agent = Depends(get_current_agent_user),
    db: Session = Depends(get_db)
):
    """代理商创建订单"""
    try:
        # 设置代理商ID
        order_data.agent_id = current_agent.id
        order_data.created_by_admin = False

        # 验证产品ID（如果提供了）
        if order_data.product_id:
            from app.models.product import Product
            product = db.query(Product).filter(Product.id == order_data.product_id).first()
            if not product:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"product_id": f"产品ID {order_data.product_id} 不存在"}
                )
            if not product.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"product_id": f"产品 {product.name} 已停用"}
                )

        order = OrderService.create_order(db, order_data)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建订单失败，请检查输入数据是否正确"
            )

        return order

    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Validation error creating agent order: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating agent order: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建订单时发生错误：{str(e)}"
        )

@router.put("/{order_id}", response_model=OrderResponse)
async def update_agent_order(
    order_id: int,
    order_data: OrderUpdate,
    current_agent: Agent = Depends(get_current_agent_user),
    db: Session = Depends(get_db)
):
    """代理商更新订单"""
    try:
        # 检查订单是否存在且属于当前代理商
        existing_order = OrderService.get_order_by_id(db, order_id)
        if not existing_order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"订单 {order_id} 不存在"
            )

        if existing_order.agent_id != current_agent.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权操作此订单"
            )

        # 验证产品ID（如果要更新）
        if order_data.product_id:
            from app.models.product import Product
            product = db.query(Product).filter(Product.id == order_data.product_id).first()
            if not product:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"product_id": f"产品ID {order_data.product_id} 不存在"}
                )
            if not product.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"product_id": f"产品 {product.name} 已停用"}
                )

        order = OrderService.update_order(db, order_id, order_data)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新订单失败，请检查输入数据是否正确"
            )

        return order

    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Validation error updating agent order: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating agent order: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新订单时发生错误：{str(e)}"
        )

@router.put("/{order_id}/status")
async def update_agent_order_status(
    order_id: int,
    status_data: dict = Body(...),
    current_agent: Agent = Depends(get_current_agent_user),
    db: Session = Depends(get_db)
):
    """更新代理商订单状态"""
    try:
        # 检查订单是否存在且属于当前代理商
        existing_order = OrderService.get_order_by_id(db, order_id)
        if not existing_order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"订单 {order_id} 不存在"
            )

        if existing_order.agent_id != current_agent.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权操作此订单"
            )
        
        # 验证状态值
        new_status = status_data.get("status")
        if not new_status:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未提供状态值"
            )
        
        try:
            # 尝试将字符串转换为枚举值
            if isinstance(new_status, str):
                try:
                    new_status = OrderStatus[new_status.upper()]
                except KeyError:
                    raise ValueError(f"无效的状态值: {new_status}")
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        
        # 更新订单状态
        update_data = OrderUpdate(status=new_status)
        updated_order = OrderService.update_order(db, order_id, update_data)
        
        if not updated_order:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新订单状态失败"
            )
        
        return {"message": f"订单状态已更新为 {new_status.value}"}
    
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Validation error updating order status: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating order status: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新订单状态时发生错误：{str(e)}"
        )

@router.delete("/{order_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_agent_order(
    order_id: int,
    current_agent: Agent = Depends(get_current_agent_user),
    db: Session = Depends(get_db)
):
    """代理商删除订单"""
    try:
        # 检查订单是否存在且属于当前代理商
        existing_order = OrderService.get_order_by_id(db, order_id)
        if not existing_order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )

        if existing_order.agent_id != current_agent.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # 只允许删除待处理状态的订单
        if existing_order.status != OrderStatus.PENDING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only pending orders can be deleted"
            )

        success = OrderService.delete_order(db, order_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting agent order {order_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete order"
        )
