#!/usr/bin/env python3
"""
测试服务器状态
"""

import requests
import time

def test_server_status():
    """测试服务器状态"""
    print("🔍 测试服务器状态...")
    
    try:
        # 测试基本连接
        response = requests.get("http://localhost:8008/", timeout=5)
        print(f"✅ 服务器连接成功: {response.status_code}")
        
        # 测试支付调试页面
        response = requests.get("http://localhost:8008/api/payment-debug", timeout=5)
        print(f"✅ 支付调试页面: {response.status_code}")
        
        # 测试API接口
        response = requests.get("http://localhost:8008/api/payment-debug/config/list", timeout=5)
        print(f"✅ 配置API: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   配置数量: {len(data.get('data', []))}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 服务器连接失败，请检查服务器是否启动")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_server_status()
    if success:
        print("\n🎉 服务器运行正常！")
        print("可以访问: http://localhost:8008/api/payment-debug")
    else:
        print("\n❌ 服务器有问题，请检查启动日志")
