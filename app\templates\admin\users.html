{% extends "admin/base.html" %}

{% block title %}用户管理 - FocuSee 管理系统{% endblock %}
{% block page_title %}用户管理{% endblock %}

{% block data %}
    users: [
        {% for user in users %}
        {
            id: {{ user.id }},
            user_id: "{{ user.user_id }}",
            license_key: "{{ user.license_key or '' }}",
            user_type: "{{ user.user_type.value if user.user_type else 'regular' }}",
            email: "{{ user.email or '' }}",
            phone: "{{ user.phone or '' }}",
            description: "{{ user.description or '' }}",
            is_active: {{ 'true' if user.is_active else 'false' }},
            last_login_at: "{{ user.last_login_at.isoformat() if user.last_login_at else '' }}",
            created_at: "{{ user.created_at.isoformat() if user.created_at else '' }}",
            updated_at: "{{ user.updated_at.isoformat() if user.updated_at else '' }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    dialogVisible: false,
    editMode: false,
    currentUser: {
        user_id: '',
        license_key: '',
        user_type: 'regular',
        email: '',
        phone: '',
        password: '',
        description: '',
        is_active: true
    },
    userForm: {
        user_id: '',
        license_key: '',
        user_type: 'regular',
        email: '',
        phone: '',
        password: '',
        description: '',
        is_active: true
    },
    rules: {
        user_id: [
            { required: true, message: '请输入用户ID', trigger: 'blur' }
        ],
        user_type: [
            { required: true, message: '请选择用户类型', trigger: 'change' }
        ],
        email: [
            { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
    },
    userTypeOptions: [
        { value: 'regular', label: '普通用户' },
        { value: 'agent', label: '代理商' },
        { value: 'admin', label: '管理员' }
    ],
    loading: false
{% endblock %}

{% block content %}
<div class="content-card">
    <!-- 操作栏 -->
    <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="openAddDialog">
            <el-icon><Plus /></el-icon>
            添加用户
        </el-button>
        <el-button @click="refreshUsers">
            <el-icon><Refresh /></el-icon>
            刷新
        </el-button>
    </div>
    
    <!-- 用户表格 -->
    <el-table :data="users" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="user_id" label="用户ID" width="120"></el-table-column>
        <el-table-column prop="user_type" label="用户类型" width="100">
            <template #default="scope">
                <el-tag :type="getUserTypeColor(scope.row.user_type)">
                    {% raw %}{{ getUserTypeLabel(scope.row.user_type) }}{% endraw %}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="phone" label="电话" width="120"></el-table-column>
        <el-table-column prop="license_key" label="授权码" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="is_active" label="状态" width="80">
            <template #default="scope">
                <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                    {% raw %}{{ scope.row.is_active ? '激活' : '禁用' }}{% endraw %}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="last_login_at" label="最后登录" width="150">
            <template #default="scope">
                {% raw %}{{ scope.row.last_login_at ? formatTime(scope.row.last_login_at) : '从未登录' }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="150">
            <template #default="scope">
                {% raw %}{{ formatTime(scope.row.created_at) }}{% endraw %}
            </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
            <template #default="scope">
                <el-button size="small" @click="editUser(scope.row)">编辑</el-button>
                <el-button 
                    size="small" 
                    :type="scope.row.is_active ? 'warning' : 'success'"
                    @click="toggleUserStatus(scope.row)">
                    {% raw %}{{ scope.row.is_active ? '禁用' : '启用' }}{% endraw %}
                </el-button>
                <el-button size="small" type="danger" @click="deleteUser(scope.row)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>
</div>

<!-- 添加/编辑用户对话框 -->
<el-dialog 
    :title="editMode ? '编辑用户' : '添加用户'" 
    v-model="dialogVisible" 
    width="500px">
    <el-form ref="userFormRef" :model="userForm" :rules="rules" label-width="100px">
        <el-form-item label="用户ID" prop="user_id">
            <el-input v-model="userForm.user_id" :disabled="editMode" placeholder="请输入用户ID"></el-input>
        </el-form-item>

        <el-form-item label="用户类型" prop="user_type">
            <el-select v-model="userForm.user_type" placeholder="请选择用户类型" style="width: 100%">
                <el-option
                    v-for="option in userTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value">
                </el-option>
            </el-select>
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
            <el-input v-model="userForm.email" placeholder="请输入邮箱地址"></el-input>
        </el-form-item>

        <el-form-item label="电话">
            <el-input v-model="userForm.phone" placeholder="请输入电话号码"></el-input>
        </el-form-item>

        <el-form-item label="密码" v-if="!editMode">
            <el-input v-model="userForm.password" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>

        <el-form-item label="授权码">
            <el-input v-model="userForm.license_key" placeholder="可选，绑定的授权码"></el-input>
        </el-form-item>

        <el-form-item label="描述">
            <el-input v-model="userForm.description" type="textarea" rows="3" placeholder="用户描述信息"></el-input>
        </el-form-item>

        <el-form-item label="状态">
            <el-switch v-model="userForm.is_active" active-text="激活" inactive-text="禁用"></el-switch>
        </el-form-item>
    </el-form>
    
    <template #footer>
        <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveUser">确定</el-button>
        </span>
    </template>
</el-dialog>
{% endblock %}

{% block methods %}
formatTime(timeStr) {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
},
getUserTypeLabel(userType) {
    const typeMap = {
        'regular': '普通用户',
        'agent': '代理商',
        'admin': '管理员'
    };
    return typeMap[userType] || userType;
},
getUserTypeColor(userType) {
    const colorMap = {
        'regular': '',
        'agent': 'warning',
        'admin': 'danger'
    };
    return colorMap[userType] || '';
},
openAddDialog() {
    this.editMode = false;
    this.userForm = {
        user_id: '',
        license_key: '',
        user_type: 'regular',
        email: '',
        phone: '',
        password: '',
        description: '',
        is_active: true
    };
    this.dialogVisible = true;
},
editUser(user) {
    this.editMode = true;
    this.currentUser = user;
    this.userForm = {
        user_id: user.user_id,
        license_key: user.license_key || '',
        user_type: user.user_type || 'regular',
        email: user.email || '',
        phone: user.phone || '',
        description: user.description || '',
        is_active: user.is_active
    };
    this.dialogVisible = true;
},
saveUser() {
    this.$refs.userFormRef.validate((valid) => {
        if (valid) {
            const url = this.editMode ? `/api/admin/users/${this.currentUser.user_id}` : '/api/admin/users';
            const method = this.editMode ? 'PUT' : 'POST';
            
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.userForm)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => Promise.reject(err));
                }
                return response.json();
            })
            .then(data => {
                ElMessage.success(this.editMode ? '用户更新成功' : '用户创建成功');
                this.dialogVisible = false;
                this.refreshUsers();
            })
            .catch(error => {
                console.error('Error saving user:', error);
                ElMessage.error('操作失败：' + (error.detail || error.message || '未知错误'));
            });
        }
    });
},
toggleUserStatus(user) {
    const action = user.is_active ? '禁用' : '启用';
    ElMessageBox.confirm(`确定要${action}用户 ${user.user_id} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        fetch(`/api/admin/users/${user.user_id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_active: !user.is_active
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            return response.json();
        })
        .then(data => {
            ElMessage.success(`用户${action}成功`);
            this.refreshUsers();
        })
        .catch(error => {
            console.error('Error toggling user status:', error);
            ElMessage.error('操作失败：' + (error.detail || error.message || '未知错误'));
        });
    });
},
deleteUser(user) {
    ElMessageBox.confirm(`确定要删除用户 ${user.user_id} 吗？此操作不可恢复！`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        fetch(`/api/admin/users/${user.user_id}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            return response.json();
        })
        .then(data => {
            ElMessage.success('用户删除成功');
            this.refreshUsers();
        })
        .catch(error => {
            console.error('Error deleting user:', error);
            ElMessage.error('删除失败：' + (error.detail || error.message || '未知错误'));
        });
    });
},
refreshUsers() {
    this.loading = true;
    window.location.reload();
}
{% endblock %}
