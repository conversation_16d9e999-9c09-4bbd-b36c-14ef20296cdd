from fastapi import APIRouter, Depends, HTTPException, Request, Form
from fastapi.responses import PlainTextResponse
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging

from app.database import get_db
from app.schemas.payment import (
    CreateFaceToFacePaymentRequest,
    CreateOrderCodePaymentRequest,
    PaymentQueryRequest,
    PaymentResponse,
    PaymentOrderListResponse,
    PaymentOrderInfo,
    AlipayNotifyData
)
from app.services.payment_service import PaymentService
from app.models.payment_order import PaymentOrder
from app.models.product import Product

logger = logging.getLogger(__name__)

router = APIRouter()
payment_service = PaymentService()

def get_client_info(request: Request) -> Dict[str, str]:
    """获取客户端信息"""
    return {
        "ip_address": request.client.host,
        "user_agent": request.headers.get("user-agent", "")
    }

@router.post("/face-to-face", response_model=PaymentResponse)
async def create_face_to_face_payment(
    request: CreateFaceToFacePaymentRequest,
    db: Session = Depends(get_db),
    client_info: Dict[str, str] = Depends(get_client_info)
):
    """
    创建当面付支付订单
    """
    try:
        result = payment_service.create_face_to_face_payment(
            db=db,
            user_id=request.user_id,
            product_id=request.product_id,
            agent_id=request.agent_id,
            timeout_minutes=request.timeout_minutes
        )
        
        return PaymentResponse(**result)
        
    except Exception as e:
        logger.error(f"创建当面付支付失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建支付失败: {str(e)}")

@router.post("/order-code", response_model=PaymentResponse)
async def create_order_code_payment(
    request: CreateOrderCodePaymentRequest,
    db: Session = Depends(get_db),
    client_info: Dict[str, str] = Depends(get_client_info)
):
    """
    创建订单码支付
    """
    try:
        result = payment_service.create_order_code_payment(
            db=db,
            user_id=request.user_id,
            product_id=request.product_id,
            auth_code=request.auth_code,
            agent_id=request.agent_id
        )
        
        return PaymentResponse(**result)
        
    except Exception as e:
        logger.error(f"订单码支付失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"支付失败: {str(e)}")

@router.post("/query", response_model=PaymentResponse)
async def query_payment_status(
    request: PaymentQueryRequest,
    db: Session = Depends(get_db)
):
    """
    查询支付状态
    """
    try:
        logger.info(f"收到支付状态查询请求 - 订单号: {request.order_no}")

        result = payment_service.query_payment_status(
            db=db,
            order_no=request.order_no
        )

        logger.info(f"支付状态查询完成 - 订单号: {request.order_no}, 成功: {result.get('success')}")

        return PaymentResponse(**result)

    except Exception as e:
        logger.error(f"支付状态查询API异常 - 订单号: {request.order_no}, 错误: {str(e)}")
        logger.exception("支付状态查询API异常详情:")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@router.get("/orders/{user_id}", response_model=PaymentOrderListResponse)
async def get_user_payment_orders(
    user_id: str,
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """
    获取用户的支付订单列表
    """
    try:
        orders = payment_service.get_user_payment_orders(
            db=db,
            user_id=user_id,
            skip=skip,
            limit=limit
        )
        
        # 转换为响应格式
        order_infos = []
        for order in orders:
            product = db.query(Product).filter(Product.id == order.product_id).first()
            order_info = PaymentOrderInfo(
                id=order.id,
                order_no=order.order_no,
                user_id=order.user_id,
                product_id=order.product_id,
                product_name=product.name if product else None,
                payment_method=order.payment_method.value,
                amount=float(order.amount),
                currency=order.currency,
                status=order.status.value,
                subject=order.subject,
                body=order.body,
                alipay_trade_no=order.alipay_trade_no,
                expire_time=order.expire_time,
                paid_at=order.paid_at,
                created_at=order.created_at,
                updated_at=order.updated_at
            )
            order_infos.append(order_info)
        
        return PaymentOrderListResponse(
            success=True,
            orders=order_infos,
            total=len(order_infos)
        )
        
    except Exception as e:
        logger.error(f"获取用户支付订单失败: {str(e)}")
        return PaymentOrderListResponse(
            success=False,
            orders=[],
            total=0,
            error=f"获取订单失败: {str(e)}"
        )

@router.post("/alipay/notify", response_class=PlainTextResponse)
async def alipay_notify_post(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    支付宝异步通知处理 (POST)
    """
    try:
        # 记录请求信息
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        logger.info(f"收到支付宝回调请求 - IP: {client_ip}, User-Agent: {user_agent}")

        # 获取表单数据
        form_data = await request.form()
        notify_data = dict(form_data)

        logger.info(f"支付宝POST通知数据: {notify_data}")

        # 检查关键字段
        out_trade_no = notify_data.get('out_trade_no')
        trade_status = notify_data.get('trade_status')
        total_amount = notify_data.get('total_amount')

        logger.info(f"关键信息 - 订单号: {out_trade_no}, 交易状态: {trade_status}, 金额: {total_amount}")

        # 处理通知
        logger.info("开始处理支付宝通知...")
        result = payment_service.handle_alipay_notify(db, notify_data)

        if result:
            logger.info(f"支付宝通知处理成功 - 订单号: {out_trade_no}")
            return "success"
        else:
            logger.warning(f"支付宝通知处理失败 - 订单号: {out_trade_no}")
            return "fail"

    except Exception as e:
        logger.error(f"处理支付宝POST通知异常: {str(e)}")
        logger.exception("支付宝POST通知处理异常详情:")
        return "fail"

@router.get("/alipay/notify", response_class=PlainTextResponse)
async def alipay_notify_get(
    request: Request
):
    """
    支付宝通知URL验证 (GET)
    支付宝可能会发送GET请求来验证URL的可达性
    """
    try:
        logger.info(f"收到支付宝GET验证请求: {request.url}")

        # 对于GET请求，直接返回success表示URL可达
        return "success"

    except Exception as e:
        logger.error(f"处理支付宝GET验证失败: {str(e)}")
        return "fail"

@router.get("/test/products")
async def get_test_products(db: Session = Depends(get_db)):
    """
    获取测试产品列表（用于前端测试）
    """
    try:
        products = db.query(Product).filter(Product.is_active == True).all()
        
        product_list = []
        for product in products:
            product_list.append({
                "id": product.id,
                "name": product.name,
                "code": product.code,
                "price": float(product.price),
                "description": product.description
            })
        
        return {
            "success": True,
            "products": product_list
        }
        
    except Exception as e:
        logger.error(f"获取产品列表失败: {str(e)}")
        return {
            "success": False,
            "error": f"获取产品列表失败: {str(e)}",
            "products": []
        }

@router.get("/order/{order_no}")
async def get_payment_order_detail(
    order_no: str,
    db: Session = Depends(get_db)
):
    """
    获取支付订单详情
    """
    try:
        order = db.query(PaymentOrder).filter(PaymentOrder.order_no == order_no).first()
        
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")
        
        product = db.query(Product).filter(Product.id == order.product_id).first()
        
        return {
            "success": True,
            "order": {
                "id": order.id,
                "order_no": order.order_no,
                "user_id": order.user_id,
                "product_id": order.product_id,
                "product_name": product.name if product else None,
                "payment_method": order.payment_method.value,
                "amount": float(order.amount),
                "currency": order.currency,
                "status": order.status.value,
                "subject": order.subject,
                "body": order.body,
                "alipay_trade_no": order.alipay_trade_no,
                "alipay_qr_code": order.alipay_qr_code,
                "expire_time": order.expire_time.isoformat() if order.expire_time else None,
                "paid_at": order.paid_at.isoformat() if order.paid_at else None,
                "created_at": order.created_at.isoformat(),
                "updated_at": order.updated_at.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取订单详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取订单详情失败: {str(e)}")

@router.post("/cancel/{order_no}")
async def cancel_payment_order(
    order_no: str,
    db: Session = Depends(get_db)
):
    """
    取消支付订单
    """
    try:
        order = db.query(PaymentOrder).filter(PaymentOrder.order_no == order_no).first()
        
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")
        
        if order.status.value != "pending":
            raise HTTPException(status_code=400, detail="订单状态不允许取消")
        
        # 更新订单状态为已取消
        from app.models.payment_order import PaymentStatus
        order.status = PaymentStatus.CANCELLED
        db.commit()
        
        return {
            "success": True,
            "message": "订单已取消"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消订单失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"取消订单失败: {str(e)}")
