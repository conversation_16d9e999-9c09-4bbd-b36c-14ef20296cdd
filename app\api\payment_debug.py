"""
支付调试API
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional
from pydantic import BaseModel
import json
from datetime import datetime

from app.database import get_db
from app.models.payment_debug import PaymentDebug, PaymentConfig
from app.models.product import Product
from app.services.alipay_service import AlipayService
from app.services.payment_service import PaymentService
from app.utils.logger import logger

router = APIRouter()

# 请求模型
class PaymentDebugCreate(BaseModel):
    debug_name: str
    payment_mode: str  # face_to_face, barcode
    user_id: str
    product_id: int
    amount: float
    subject: str
    body: Optional[str] = None
    timeout_minutes: int = 30

class PaymentConfigCreate(BaseModel):
    config_name: str
    config_type: str = "alipay"
    app_id: str
    app_private_key: str
    alipay_public_key: str
    gateway_url: str
    notify_url: str
    return_url: Optional[str] = None
    environment: str = "sandbox"
    description: Optional[str] = None

class PaymentConfigUpdate(BaseModel):
    config_name: Optional[str] = None
    app_id: Optional[str] = None
    app_private_key: Optional[str] = None
    alipay_public_key: Optional[str] = None
    gateway_url: Optional[str] = None
    notify_url: Optional[str] = None
    return_url: Optional[str] = None
    environment: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

@router.get("/debug/list")
async def get_debug_list(
    page: int = 1,
    size: int = 20,
    db: Session = Depends(get_db)
):
    """获取调试记录列表"""
    try:
        offset = (page - 1) * size
        
        # 查询调试记录
        query = db.query(PaymentDebug).order_by(desc(PaymentDebug.created_at))
        total = query.count()
        records = query.offset(offset).limit(size).all()
        
        return {
            "success": True,
            "data": {
                "records": [record.to_dict() for record in records],
                "total": total,
                "page": page,
                "size": size
            }
        }
    except Exception as e:
        logger.error(f"获取调试记录列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/debug/create")
async def create_debug_payment(
    request: PaymentDebugCreate,
    db: Session = Depends(get_db)
):
    """创建调试支付"""
    try:
        # 验证产品存在
        product = db.query(Product).filter(Product.id == request.product_id).first()
        if not product:
            raise HTTPException(status_code=404, detail="产品不存在")
        
        # 创建调试记录
        debug_record = PaymentDebug(
            debug_name=request.debug_name,
            payment_mode=request.payment_mode,
            user_id=request.user_id,
            product_id=request.product_id,
            amount=request.amount,
            subject=request.subject,
            body=request.body,
            timeout_minutes=request.timeout_minutes,
            debug_status="created"
        )
        
        db.add(debug_record)
        db.commit()
        db.refresh(debug_record)
        
        # 调用支付服务
        try:
            payment_service = PaymentService()

            if request.payment_mode == "face_to_face":
                result = payment_service.create_face_to_face_payment(
                    db=db,
                    user_id=request.user_id,
                    product_id=request.product_id,
                    amount=request.amount,  # 使用页面输入的金额
                    timeout_minutes=request.timeout_minutes
                )
            else:
                raise HTTPException(status_code=400, detail="不支持的支付模式")
            
            if result.get("success"):
                # 更新调试记录
                debug_record.order_no = result.get("order_no")
                debug_record.qr_code = result.get("qr_code")
                debug_record.debug_status = "payment_created"
                
                # 保存支付宝配置信息
                alipay_service = AlipayService()
                debug_record.alipay_config = {
                    "app_id": alipay_service.app_id,
                    "gateway_url": alipay_service.gateway_url,
                    "notify_url": alipay_service.notify_url,
                    "return_url": alipay_service.return_url
                }
                
                db.commit()

                # 合并调试记录和支付结果
                debug_data = debug_record.to_dict()
                # 添加支付服务返回的qr_image
                if result.get("qr_image"):
                    debug_data["qr_image"] = result.get("qr_image")

                return {
                    "success": True,
                    "data": debug_data,
                    "payment_result": result
                }
            else:
                debug_record.debug_status = "failed"
                debug_record.error_message = result.get("error", "支付创建失败")
                db.commit()
                
                return {
                    "success": False,
                    "error": result.get("error", "支付创建失败"),
                    "data": debug_record.to_dict()
                }
                
        except Exception as payment_error:
            debug_record.debug_status = "failed"
            debug_record.error_message = str(payment_error)
            db.commit()
            
            return {
                "success": False,
                "error": str(payment_error),
                "data": debug_record.to_dict()
            }
            
    except Exception as e:
        logger.error(f"创建调试支付失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/debug/{debug_id}/query")
async def query_debug_payment(
    debug_id: int,
    db: Session = Depends(get_db)
):
    """查询调试支付状态"""
    try:
        # 获取调试记录
        debug_record = db.query(PaymentDebug).filter(PaymentDebug.id == debug_id).first()
        if not debug_record:
            raise HTTPException(status_code=404, detail="调试记录不存在")
        
        if not debug_record.order_no:
            return {
                "success": False,
                "error": "订单号不存在",
                "data": debug_record.to_dict()
            }
        
        try:
            # 查询支付状态
            payment_service = PaymentService()
            result = payment_service.query_payment_status(db, debug_record.order_no)
            
            # 更新调试记录
            debug_record.query_result = result
            debug_record.last_query_time = datetime.now()
            
            if result.get("success"):
                status = result.get("status")
                if status == "paid":
                    debug_record.debug_status = "paid"
                    debug_record.payment_status = "TRADE_SUCCESS"
                    debug_record.trade_no = result.get("trade_no")
                elif status == "timeout":
                    debug_record.debug_status = "timeout"
                    debug_record.payment_status = "TRADE_CLOSED"
            
            db.commit()
            
            return {
                "success": True,
                "data": debug_record.to_dict(),
                "query_result": result
            }
            
        except Exception as query_error:
            debug_record.error_message = str(query_error)
            debug_record.last_query_time = datetime.now()
            db.commit()
            
            return {
                "success": False,
                "error": str(query_error),
                "data": debug_record.to_dict()
            }
            
    except Exception as e:
        logger.error(f"查询调试支付失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/debug/{debug_id}")
async def delete_debug_record(
    debug_id: int,
    db: Session = Depends(get_db)
):
    """删除调试记录"""
    try:
        debug_record = db.query(PaymentDebug).filter(PaymentDebug.id == debug_id).first()
        if not debug_record:
            raise HTTPException(status_code=404, detail="调试记录不存在")
        
        db.delete(debug_record)
        db.commit()
        
        return {"success": True, "message": "调试记录已删除"}
        
    except Exception as e:
        logger.error(f"删除调试记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 支付配置管理
@router.get("/config/list")
async def get_config_list(db: Session = Depends(get_db)):
    """获取支付配置列表"""
    try:
        configs = db.query(PaymentConfig).order_by(desc(PaymentConfig.created_at)).all()
        return {
            "success": True,
            "data": [config.to_dict() for config in configs]
        }
    except Exception as e:
        logger.error(f"获取支付配置列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config/create")
async def create_payment_config(
    request: PaymentConfigCreate,
    db: Session = Depends(get_db)
):
    """创建支付配置"""
    try:
        # 检查配置名称是否已存在
        existing = db.query(PaymentConfig).filter(PaymentConfig.config_name == request.config_name).first()
        if existing:
            raise HTTPException(status_code=400, detail="配置名称已存在")
        
        config = PaymentConfig(
            config_name=request.config_name,
            config_type=request.config_type,
            app_id=request.app_id,
            app_private_key=request.app_private_key,
            alipay_public_key=request.alipay_public_key,
            gateway_url=request.gateway_url,
            notify_url=request.notify_url,
            return_url=request.return_url,
            environment=request.environment,
            description=request.description
        )
        
        db.add(config)
        db.commit()
        db.refresh(config)
        
        return {
            "success": True,
            "data": config.to_dict()
        }
        
    except Exception as e:
        logger.error(f"创建支付配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/config/{config_id}")
async def update_payment_config(
    config_id: int,
    request: PaymentConfigUpdate,
    db: Session = Depends(get_db)
):
    """更新支付配置"""
    try:
        config = db.query(PaymentConfig).filter(PaymentConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        # 更新字段
        for field, value in request.dict(exclude_unset=True).items():
            setattr(config, field, value)
        
        db.commit()
        db.refresh(config)
        
        return {
            "success": True,
            "data": config.to_dict()
        }
        
    except Exception as e:
        logger.error(f"更新支付配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config/{config_id}/activate")
async def activate_payment_config(
    config_id: int,
    db: Session = Depends(get_db)
):
    """激活支付配置"""
    try:
        # 先取消所有配置的激活状态
        db.query(PaymentConfig).update({"is_active": False})
        
        # 激活指定配置
        config = db.query(PaymentConfig).filter(PaymentConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        config.is_active = True
        db.commit()
        
        return {
            "success": True,
            "message": f"配置 {config.config_name} 已激活"
        }
        
    except Exception as e:
        logger.error(f"激活支付配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/config/{config_id}")
async def delete_payment_config(
    config_id: int,
    db: Session = Depends(get_db)
):
    """删除支付配置"""
    try:
        config = db.query(PaymentConfig).filter(PaymentConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        db.delete(config)
        db.commit()
        
        return {"success": True, "message": "配置已删除"}
        
    except Exception as e:
        logger.error(f"删除支付配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
