{% extends "admin/base.html" %}

{% block title %}算法管理 - FocuSee 管理系统{% endblock %}

{% block content %}
<div id="app">
    <div class="page-header">
        <h1>算法管理</h1>
        <p>管理机器码激活码生成算法</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
        <el-button type="primary" @click="showCreateDialog" :icon="Plus">
            新增算法
        </el-button>
        <el-button @click="loadAlgorithms" :icon="Refresh">
            刷新
        </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="算法名称">
                <el-input
                    v-model="searchForm.name"
                    placeholder="请输入算法名称"
                    clearable
                    @clear="loadAlgorithms"
                    @keyup.enter="loadAlgorithms">
                </el-input>
            </el-form-item>
            <el-form-item label="状态">
                <el-select
                    v-model="searchForm.is_active"
                    placeholder="请选择状态"
                    clearable
                    @change="loadAlgorithms">
                    <el-option label="启用" :value="true"></el-option>
                    <el-option label="禁用" :value="false"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="loadAlgorithms" :icon="Search">
                    搜索
                </el-button>
            </el-form-item>
        </el-form>
    </div>

    <!-- 算法列表 -->
    <div class="table-container">
        <el-table
            :data="algorithms"
            v-loading="loading"
            stripe
            border
            style="width: 100%">
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="name" label="算法名称" min-width="150">
                <template #default="scope">
                    <div class="algorithm-name">
                        <span>{% raw %}{{ scope.row.name }}{% endraw %}</span>
                        <el-tag v-if="scope.row.is_default" type="success" size="small">默认</el-tag>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column prop="version" label="版本" width="100"></el-table-column>
            <el-table-column prop="author" label="作者" width="120"></el-table-column>
            <el-table-column prop="is_active" label="状态" width="100">
                <template #default="scope">
                    <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                        {% raw %}{{ scope.row.is_active ? '启用' : '禁用' }}{% endraw %}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="scope">
                    {% raw %}{{ formatDateTime(scope.row.created_at) }}{% endraw %}
                </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                    <el-button
                        type="primary"
                        size="small"
                        @click="showEditDialog(scope.row)"
                        :icon="Edit">
                        编辑
                    </el-button>
                    <el-button
                        type="success"
                        size="small"
                        @click="testAlgorithm(scope.row)"
                        :icon="VideoPlay">
                        测试
                    </el-button>
                    <el-button
                        v-if="!scope.row.is_default"
                        type="danger"
                        size="small"
                        @click="deleteAlgorithm(scope.row)"
                        :icon="Delete">
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange">
            </el-pagination>
        </div>
    </div>

    <!-- 创建/编辑算法对话框 -->
    <el-dialog
        :title="dialogMode === 'create' ? '新增算法' : '编辑算法'"
        v-model="dialogVisible"
        width="800px"
        :close-on-click-modal="false">
        <el-form
            ref="algorithmFormRef"
            :model="algorithmForm"
            :rules="algorithmRules"
            label-width="100px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="算法名称" prop="name">
                        <el-input v-model="algorithmForm.name" placeholder="请输入算法名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="版本" prop="version">
                        <el-input v-model="algorithmForm.version" placeholder="请输入版本号"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="作者" prop="author">
                        <el-input v-model="algorithmForm.author" placeholder="请输入作者"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="状态">
                        <el-switch
                            v-model="algorithmForm.is_active"
                            active-text="启用"
                            inactive-text="禁用">
                        </el-switch>
                        <el-switch
                            v-model="algorithmForm.is_default"
                            active-text="默认算法"
                            inactive-text="普通算法"
                            style="margin-left: 20px;">
                        </el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="算法描述" prop="description">
                <el-input
                    v-model="algorithmForm.description"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入算法描述">
                </el-input>
            </el-form-item>
            <el-form-item label="算法代码" prop="algorithm_code">
                <div class="code-editor-container">
                    <el-input
                        v-model="algorithmForm.algorithm_code"
                        type="textarea"
                        :rows="15"
                        placeholder="请输入算法代码，必须设置 activation_code 变量"
                        class="code-editor">
                    </el-input>
                    <div class="code-help">
                        <el-alert
                            title="代码说明"
                            type="info"
                            :closable="false"
                            show-icon>
                            <template #default>
                                <p>• 可使用变量：machine_id (机器码), days (天数)</p>
                                <p>• 必须设置：activation_code = "生成的激活码"</p>
                                <p>• 可用模块：hashlib, hmac, time, base64, re</p>
                                <p>• 示例：activation_code = f"{machine_id}-{days}"</p>
                            </template>
                        </el-alert>
                    </div>
                </div>
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="info" @click="validateCode" :loading="validating">
                    验证代码
                </el-button>
                <el-button
                    type="primary"
                    @click="saveAlgorithm"
                    :loading="saving">
                    保存
                </el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 测试算法对话框 -->
    <el-dialog
        title="测试算法"
        v-model="testDialogVisible"
        width="600px">
        <el-form :model="testForm" label-width="100px">
            <el-form-item label="算法名称">
                <el-input :value="currentTestAlgorithm.name" disabled></el-input>
            </el-form-item>
            <el-form-item label="机器码">
                <el-input v-model="testForm.machine_id" placeholder="请输入测试机器码"></el-input>
            </el-form-item>
            <el-form-item label="有效期(天)">
                <el-input-number v-model="testForm.days" :min="1" :max="3650"></el-input-number>
            </el-form-item>
            <el-form-item label="生成结果" v-if="testResult">
                <el-input
                    :value="testResult.activation_code"
                    type="textarea"
                    :rows="3"
                    readonly>
                </el-input>
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="testDialogVisible = false">关闭</el-button>
                <el-button
                    type="primary"
                    @click="executeTest"
                    :loading="testing">
                    执行测试
                </el-button>
            </span>
        </template>
    </el-dialog>
</div>
{% endblock %}

{% block data %}
// 搜索表单
searchForm: {
    name: '',
    is_active: null
},

// 算法列表
algorithms: [],
loading: false,
total: 0,
currentPage: 1,
pageSize: 20,

// 对话框控制
dialogVisible: false,
dialogMode: 'create', // 'create' 或 'edit'
saving: false,
validating: false,

// 算法表单
algorithmForm: {
    name: '',
    description: '',
    algorithm_code: '',
    is_active: true,
    is_default: false,
    version: '1.0',
    author: ''
},
algorithmRules: {
    name: [
        { required: true, message: '请输入算法名称', trigger: 'blur' },
        { min: 1, max: 100, message: '算法名称长度应在1-100个字符之间', trigger: 'blur' }
    ],
    algorithm_code: [
        { required: true, message: '请输入算法代码', trigger: 'blur' }
    ],
    version: [
        { required: true, message: '请输入版本号', trigger: 'blur' }
    ]
},

// 测试对话框
testDialogVisible: false,
currentTestAlgorithm: {},
testForm: {
    machine_id: 'TEST123456',
    days: 30
},
testResult: null,
testing: false
{% endblock %}

{% block methods %}
// 加载算法列表
async loadAlgorithms() {
    this.loading = true;
    try {
        const params = new URLSearchParams({
            skip: (this.currentPage - 1) * this.pageSize,
            limit: this.pageSize
        });

        if (this.searchForm.name) {
            params.append('name', this.searchForm.name);
        }
        if (this.searchForm.is_active !== null) {
            params.append('is_active', this.searchForm.is_active);
        }

        const response = await fetch(`/api/activation-algorithm/?${params}`);
        if (response.ok) {
            const data = await response.json();
            this.algorithms = data.items;
            this.total = data.total;
        } else {
            ElMessage.error('加载算法列表失败');
        }
    } catch (error) {
        console.error('Load algorithms error:', error);
        ElMessage.error('网络连接失败');
    } finally {
        this.loading = false;
    }
},

// 显示创建对话框
showCreateDialog() {
    this.dialogMode = 'create';
    this.resetAlgorithmForm();
    this.dialogVisible = true;
},

// 显示编辑对话框
showEditDialog(algorithm) {
    this.dialogMode = 'edit';
    this.algorithmForm = {
        id: algorithm.id,
        name: algorithm.name,
        description: algorithm.description || '',
        algorithm_code: algorithm.algorithm_code,
        is_active: algorithm.is_active,
        is_default: algorithm.is_default,
        version: algorithm.version,
        author: algorithm.author || ''
    };
    this.dialogVisible = true;
},

// 关闭对话框
closeDialog() {
    this.dialogVisible = false;
    this.resetAlgorithmForm();
},

// 重置算法表单
resetAlgorithmForm() {
    this.algorithmForm = {
        name: '',
        description: '',
        algorithm_code: '',
        is_active: true,
        is_default: false,
        version: '1.0',
        author: ''
    };
    if (this.$refs.algorithmFormRef) {
        this.$refs.algorithmFormRef.clearValidate();
    }
},

// 验证算法代码
async validateCode() {
    if (!this.algorithmForm.algorithm_code) {
        ElMessage.warning('请先输入算法代码');
        return;
    }

    this.validating = true;
    try {
        const response = await fetch('/api/activation-algorithm/validate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.algorithmForm.algorithm_code)
        });

        if (response.ok) {
            const result = await response.json();
            if (result.valid) {
                ElMessage.success('算法代码验证通过');
            } else {
                ElMessage.error(`验证失败: ${result.error}`);
            }
        } else {
            ElMessage.error('验证请求失败');
        }
    } catch (error) {
        console.error('Validate code error:', error);
        ElMessage.error('网络连接失败');
    } finally {
        this.validating = false;
    }
},

// 保存算法
async saveAlgorithm() {
    try {
        const valid = await this.$refs.algorithmFormRef.validate();
        if (!valid) return;

        this.saving = true;

        const url = this.dialogMode === 'create'
            ? '/api/activation-algorithm/'
            : `/api/activation-algorithm/${this.algorithmForm.id}`;
        const method = this.dialogMode === 'create' ? 'POST' : 'PUT';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.algorithmForm)
        });

        if (response.ok) {
            ElMessage.success(`算法${this.dialogMode === 'create' ? '创建' : '更新'}成功`);
            this.closeDialog();
            this.loadAlgorithms();
        } else {
            const error = await response.json().catch(() => ({ detail: '未知错误' }));
            ElMessage.error(`保存失败: ${error.detail}`);
        }
    } catch (error) {
        console.error('Save algorithm error:', error);
        ElMessage.error('网络连接失败');
    } finally {
        this.saving = false;
    }
},

// 删除算法
async deleteAlgorithm(algorithm) {
    try {
        await ElMessageBox.confirm(
            `确定要删除算法 "${algorithm.name}" 吗？此操作不可恢复。`,
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        const response = await fetch(`/api/activation-algorithm/${algorithm.id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            ElMessage.success('算法删除成功');
            this.loadAlgorithms();
        } else {
            const error = await response.json().catch(() => ({ detail: '未知错误' }));
            ElMessage.error(`删除失败: ${error.detail}`);
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('Delete algorithm error:', error);
            ElMessage.error('网络连接失败');
        }
    }
},

// 测试算法
testAlgorithm(algorithm) {
    this.currentTestAlgorithm = algorithm;
    this.testForm = {
        machine_id: 'TEST123456',
        days: 30
    };
    this.testResult = null;
    this.testDialogVisible = true;
},

// 执行测试
async executeTest() {
    if (!this.testForm.machine_id) {
        ElMessage.warning('请输入测试机器码');
        return;
    }

    this.testing = true;
    try {
        const params = new URLSearchParams({
            algorithm_id: this.currentTestAlgorithm.id,
            machine_id: this.testForm.machine_id,
            days: this.testForm.days
        });

        const response = await fetch(`/api/activation-algorithm/test?${params}`, {
            method: 'POST'
        });

        if (response.ok) {
            this.testResult = await response.json();
            ElMessage.success('测试执行成功');
        } else {
            const error = await response.json().catch(() => ({ detail: '未知错误' }));
            ElMessage.error(`测试失败: ${error.detail}`);
        }
    } catch (error) {
        console.error('Test algorithm error:', error);
        ElMessage.error('网络连接失败');
    } finally {
        this.testing = false;
    }
},

// 分页处理
handleSizeChange(val) {
    this.pageSize = val;
    this.currentPage = 1;
    this.loadAlgorithms();
},

handleCurrentChange(val) {
    this.currentPage = val;
    this.loadAlgorithms();
},

// 格式化日期时间
formatDateTime(dateTime) {
    if (!dateTime) return '';
    return new Date(dateTime).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}
{% endblock %}

{% block mounted %}
// 页面加载时获取算法列表
this.loadAlgorithms();
{% endblock %}

{% block styles %}
<style>
.algorithm-name {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-editor-container {
    width: 100%;
}

.code-editor {
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.code-help {
    margin-top: 10px;
}

.code-help .el-alert {
    font-size: 12px;
}

.code-help p {
    margin: 2px 0;
    font-size: 12px;
}

.action-bar {
    margin-bottom: 20px;
}

.search-bar {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.search-form .el-form-item {
    margin-bottom: 0;
}

.table-container {
    background: white;
    border-radius: 4px;
    overflow: hidden;
}

.pagination-container {
    padding: 20px;
    text-align: right;
    background: white;
    border-top: 1px solid #ebeef5;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>
{% endblock %}
