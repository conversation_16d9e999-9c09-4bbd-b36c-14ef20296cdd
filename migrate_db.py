#!/usr/bin/env python3
"""
数据库迁移脚本
用于升级现有数据库结构以支持新的授权管理系统
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.config import settings
from app.database import Base
from app.models import *  # 导入所有模型
from app.services.permission_service import PermissionService
from app.services.admin_service import AdminService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_engine_and_session():
    """创建数据库引擎和会话"""
    engine = create_engine(
        settings.database_url,
        echo=True,
        pool_pre_ping=True,
        pool_recycle=300
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return engine, SessionLocal

def backup_existing_data(engine):
    """备份现有数据"""
    logger.info("Backing up existing data...")
    
    backup_queries = [
        "CREATE TABLE IF NOT EXISTS users_backup AS SELECT * FROM users;",
        "CREATE TABLE IF NOT EXISTS admin_users_backup AS SELECT * FROM admin_users;",
        "CREATE TABLE IF NOT EXISTS download_logs_backup AS SELECT * FROM download_logs;"
    ]
    
    try:
        with engine.connect() as conn:
            for query in backup_queries:
                conn.execute(text(query))
            conn.commit()
        logger.info("Data backup completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error backing up data: {str(e)}")
        return False

def create_new_tables(engine):
    """创建新的数据表"""
    logger.info("Creating new tables...")
    
    try:
        # 创建所有新表
        Base.metadata.create_all(bind=engine)
        logger.info("New tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating new tables: {str(e)}")
        return False

def update_users_table_structure(engine):
    """更新现有users表结构"""
    logger.info("Updating users table structure...")

    try:
        with engine.connect() as conn:
            # 添加新字段
            alter_queries = [
                "ALTER TABLE users ADD COLUMN user_type ENUM('REGULAR','AGENT','ADMIN') DEFAULT 'REGULAR' COMMENT '用户类型';",
                "ALTER TABLE users ADD COLUMN password_hash VARCHAR(255) COMMENT '密码哈希（用于登录）';",
                "ALTER TABLE users ADD COLUMN email VARCHAR(100) COMMENT '邮箱';",
                "ALTER TABLE users ADD COLUMN phone VARCHAR(20) COMMENT '电话';",
                "ALTER TABLE users ADD COLUMN last_login_at DATETIME COMMENT '最后登录时间';",
                "ALTER TABLE users MODIFY COLUMN license_key VARCHAR(255) NULL COMMENT '授权码（可为空）';"
            ]

            for query in alter_queries:
                try:
                    conn.execute(text(query))
                    logger.info(f"Executed: {query}")
                except Exception as e:
                    if "Duplicate column name" in str(e):
                        logger.info(f"Column already exists, skipping: {query}")
                    else:
                        logger.warning(f"Error executing query {query}: {str(e)}")

            conn.commit()

        logger.info("Users table structure updated successfully")
        return True

    except Exception as e:
        logger.error(f"Error updating users table structure: {str(e)}")
        return False

def migrate_existing_users(session):
    """迁移现有用户数据"""
    logger.info("Migrating existing users...")

    try:
        # 查询现有用户（使用原始SQL避免ORM问题）
        result = session.execute(text("SELECT user_id, license_key, is_active, description, created_at, updated_at FROM users"))
        existing_users = result.fetchall()

        for user_data in existing_users:
            # 为普通用户分配默认角色
            PermissionService.assign_role_to_user(session, user_data.user_id, "user", "system")

        session.commit()
        logger.info("User migration completed successfully")
        return True

    except Exception as e:
        logger.error(f"Error migrating users: {str(e)}")
        session.rollback()
        return False

def create_sample_data(session):
    """创建示例数据"""
    logger.info("Creating sample data...")
    
    try:
        # 创建示例产品
        sample_products = [
            {
                "name": "FocuSee Pro",
                "code": "FOCUSEE_PRO",
                "description": "FocuSee专业版，提供完整的屏幕录制和编辑功能",
                "version": "1.0.0",
                "price": 99.99
            },
            {
                "name": "FocuSee Basic",
                "code": "FOCUSEE_BASIC", 
                "description": "FocuSee基础版，提供基本的屏幕录制功能",
                "version": "1.0.0",
                "price": 49.99
            }
        ]
        
        for product_data in sample_products:
            existing_product = session.query(Product).filter(Product.code == product_data["code"]).first()
            if not existing_product:
                product = Product(**product_data)
                session.add(product)
        
        # 创建示例代理商
        sample_agent_data = {
            "username": "demo_agent",
            "password": "demo123",
            "company_name": "示例代理商公司",
            "contact_name": "张三",
            "contact_email": "<EMAIL>",
            "contact_phone": "13800138008",
            "description": "示例代理商账户"
        }
        
        existing_agent = session.query(Agent).filter(Agent.username == sample_agent_data["username"]).first()
        if not existing_agent:
            password_hash = AdminService.get_password_hash(sample_agent_data["password"])
            agent = Agent(
                username=sample_agent_data["username"],
                password_hash=password_hash,
                company_name=sample_agent_data["company_name"],
                contact_name=sample_agent_data["contact_name"],
                contact_email=sample_agent_data["contact_email"],
                contact_phone=sample_agent_data["contact_phone"],
                description=sample_agent_data["description"]
            )
            session.add(agent)
        
        session.commit()
        logger.info("Sample data created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error creating sample data: {str(e)}")
        session.rollback()
        return False

def main():
    """主迁移函数"""
    logger.info("Starting database migration...")
    
    try:
        # 创建数据库引擎和会话
        engine, SessionLocal = create_engine_and_session()
        session = SessionLocal()
        
        # 1. 备份现有数据
        if not backup_existing_data(engine):
            logger.error("Failed to backup existing data")
            return False
        
        # 2. 更新现有users表结构
        if not update_users_table_structure(engine):
            logger.error("Failed to update users table structure")
            return False

        # 3. 创建新表
        if not create_new_tables(engine):
            logger.error("Failed to create new tables")
            return False

        # 4. 初始化默认角色
        if not PermissionService.init_default_roles(session):
            logger.error("Failed to initialize default roles")
            return False

        # 5. 迁移现有用户
        if not migrate_existing_users(session):
            logger.error("Failed to migrate existing users")
            return False
        
        # 6. 创建示例数据
        if not create_sample_data(session):
            logger.error("Failed to create sample data")
            return False
        
        session.close()
        logger.info("Database migration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
