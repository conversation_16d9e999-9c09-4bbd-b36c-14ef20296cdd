from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from decimal import Decimal

class CreateFaceToFacePaymentRequest(BaseModel):
    """创建当面付支付请求"""
    user_id: str = Field(..., description="用户ID")
    product_id: int = Field(..., description="产品ID")
    agent_id: Optional[int] = Field(None, description="代理商ID")
    timeout_minutes: int = Field(30, description="超时时间（分钟）", ge=1, le=120)

class CreateOrderCodePaymentRequest(BaseModel):
    """创建订单码支付请求"""
    user_id: str = Field(..., description="用户ID")
    product_id: int = Field(..., description="产品ID")
    auth_code: str = Field(..., description="支付授权码")
    agent_id: Optional[int] = Field(None, description="代理商ID")

class PaymentQueryRequest(BaseModel):
    """支付状态查询请求"""
    order_no: str = Field(..., description="订单号")

class PaymentResponse(BaseModel):
    """支付响应"""
    success: bool = Field(..., description="是否成功")
    order_no: Optional[str] = Field(None, description="订单号")
    qr_code: Optional[str] = Field(None, description="二维码内容")
    qr_image: Optional[str] = Field(None, description="二维码图片（base64）")
    trade_no: Optional[str] = Field(None, description="支付宝交易号")
    amount: Optional[float] = Field(None, description="支付金额")
    status: Optional[str] = Field(None, description="支付状态")
    expire_time: Optional[str] = Field(None, description="过期时间")
    paid_at: Optional[str] = Field(None, description="支付时间")
    product_name: Optional[str] = Field(None, description="产品名称")
    error: Optional[str] = Field(None, description="错误信息")

class PaymentOrderInfo(BaseModel):
    """支付订单信息"""
    id: int
    order_no: str
    user_id: str
    product_id: int
    product_name: Optional[str] = None
    payment_method: str
    amount: float
    currency: str
    status: str
    subject: str
    body: Optional[str] = None
    alipay_trade_no: Optional[str] = None
    expire_time: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PaymentOrderListResponse(BaseModel):
    """支付订单列表响应"""
    success: bool
    orders: List[PaymentOrderInfo]
    total: int
    error: Optional[str] = None

class AlipayNotifyData(BaseModel):
    """支付宝异步通知数据"""
    notify_time: str
    notify_type: str
    notify_id: str
    app_id: str
    charset: str
    version: str
    sign_type: str
    sign: str
    trade_no: str
    out_trade_no: str
    out_biz_no: Optional[str] = None
    buyer_id: str
    buyer_logon_id: str
    seller_id: str
    seller_email: str
    trade_status: str
    total_amount: str
    receipt_amount: str
    invoice_amount: str
    buyer_pay_amount: str
    point_amount: Optional[str] = None
    refund_fee: Optional[str] = None
    subject: str
    body: Optional[str] = None
    gmt_create: str
    gmt_payment: str
    gmt_refund: Optional[str] = None
    gmt_close: Optional[str] = None
    fund_bill_list: str
    passback_params: Optional[str] = None
    voucher_detail_list: Optional[str] = None
