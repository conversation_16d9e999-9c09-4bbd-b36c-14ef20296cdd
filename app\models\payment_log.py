from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum

class PaymentLogType(enum.Enum):
    CREATE = "create"           # 创建支付订单
    QUERY = "query"            # 查询支付状态
    NOTIFY = "notify"          # 接收支付通知
    CANCEL = "cancel"          # 取消支付
    REFUND = "refund"          # 退款操作
    EXPIRE = "expire"          # 订单过期

class PaymentLog(Base):
    __tablename__ = "payment_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    payment_order_id = Column(Integer, ForeignKey("payment_orders.id"), nullable=False, comment="支付订单ID")
    log_type = Column(Enum(PaymentLogType), nullable=False, comment="日志类型")
    
    # 请求信息
    request_data = Column(Text, comment="请求数据（JSON格式）")
    response_data = Column(Text, comment="响应数据（JSON格式）")
    
    # 支付宝相关
    alipay_trade_no = Column(String(64), comment="支付宝交易号")
    alipay_response = Column(Text, comment="支付宝响应数据")
    
    # 状态信息
    is_success = Column(String(1), comment="操作是否成功（Y/N）")
    error_code = Column(String(32), comment="错误代码")
    error_message = Column(Text, comment="错误信息")
    
    # 网络信息
    ip_address = Column(String(45), comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关联关系
    payment_order = relationship("PaymentOrder", backref="payment_logs")
    
    def __repr__(self):
        return f"<PaymentLog(payment_order_id={self.payment_order_id}, log_type='{self.log_type.value}', is_success='{self.is_success}')>"
