#!/usr/bin/env python3
"""
修复用户登录问题的完整脚本
"""

import sys
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session
from app.config import settings
from app.database import SessionLocal, create_tables
from app.models.user import User, UserType
from app.utils.password import hash_password

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_user_table():
    """迁移用户表结构"""
    try:
        logger.info("开始迁移用户表结构...")
        
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        with engine.connect() as connection:
            # 检查 username 字段是否存在
            result = connection.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'username'
            """))
            
            username_exists = result.fetchone() is not None
            
            if not username_exists:
                logger.info("添加 username 字段...")
                connection.execute(text("""
                    ALTER TABLE users 
                    ADD COLUMN username VARCHAR(50) UNIQUE AFTER user_id,
                    ADD INDEX idx_users_username (username)
                """))
                logger.info("username 字段添加成功")
            else:
                logger.info("username 字段已存在，跳过")
            
            # 检查 full_name 字段是否存在
            result = connection.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'full_name'
            """))
            
            full_name_exists = result.fetchone() is not None
            
            if not full_name_exists:
                logger.info("添加 full_name 字段...")
                connection.execute(text("""
                    ALTER TABLE users 
                    ADD COLUMN full_name VARCHAR(100) AFTER phone
                """))
                logger.info("full_name 字段添加成功")
            else:
                logger.info("full_name 字段已存在，跳过")
            
            # 为现有用户设置 username（如果为空）
            logger.info("更新现有用户的 username 字段...")
            connection.execute(text("""
                UPDATE users 
                SET username = user_id 
                WHERE username IS NULL OR username = ''
            """))
            
            # 提交事务
            connection.commit()
            logger.info("用户表迁移完成")
            
    except Exception as e:
        logger.error(f"用户表迁移失败: {str(e)}")
        raise

def create_test_users():
    """创建测试用户"""
    db = SessionLocal()
    try:
        logger.info("开始创建测试用户...")
        
        # 创建普通测试用户
        existing_user = db.query(User).filter(User.username == "user").first()
        if existing_user:
            logger.info("用户 'user' 已存在，更新密码...")
            existing_user.password_hash = hash_password("123456")
            db.commit()
            logger.info("已更新用户 'user' 的密码为 '123456'")
        else:
            user = User(
                user_id="user",
                username="user",
                password_hash=hash_password("123456"),
                email="<EMAIL>",
                phone="13800138000",
                full_name="测试用户",
                user_type=UserType.REGULAR,
                is_active=True,
                description="测试用户账户"
            )
            
            db.add(user)
            db.commit()
            db.refresh(user)
            logger.info("创建普通用户成功: user / 123456")
        
        # 创建管理员用户
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if existing_admin:
            logger.info("管理员用户 'admin' 已存在，更新密码...")
            existing_admin.password_hash = hash_password("admin123")
            existing_admin.user_type = UserType.ADMIN
            db.commit()
            logger.info("已更新管理员用户 'admin' 的密码为 'admin123'")
        else:
            admin_user = User(
                user_id="admin",
                username="admin",
                password_hash=hash_password("admin123"),
                email="<EMAIL>",
                phone="13800138001",
                full_name="系统管理员",
                user_type=UserType.ADMIN,
                is_active=True,
                description="系统管理员账户"
            )
            
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            logger.info("创建管理员用户成功: admin / admin123")
        
        # 列出所有用户
        users = db.query(User).all()
        logger.info(f"当前系统中有 {len(users)} 个用户:")
        for user in users:
            logger.info(f"  ID: {user.id}, 用户名: {user.username}, 用户ID: {user.user_id}, 类型: {user.user_type.value}, 激活: {user.is_active}")
        
    except Exception as e:
        logger.error(f"创建测试用户失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def test_login():
    """测试登录功能"""
    import requests
    import json
    
    base_url = "http://localhost:8008"
    
    # 测试普通用户登录
    login_data = {
        "username": "user",
        "password": "123456"
    }
    
    try:
        logger.info("测试用户登录...")
        response = requests.post(
            f"{base_url}/api/user-auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        logger.info(f"登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info("✅ 用户登录成功！")
            logger.info(f"Token: {data.get('access_token', '')[:50]}...")
            logger.info(f"用户信息: {data.get('user_info', {})}")
        else:
            logger.error(f"❌ 用户登录失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        logger.warning("⚠️ 无法连接到服务器，请确保应用正在运行")
    except Exception as e:
        logger.error(f"❌ 测试登录失败: {str(e)}")

def main():
    """主函数"""
    try:
        print("🔧 开始修复用户登录问题...")
        
        # 1. 确保数据库表存在
        logger.info("1. 创建数据库表...")
        create_tables()
        
        # 2. 迁移用户表结构
        logger.info("2. 迁移用户表结构...")
        migrate_user_table()
        
        # 3. 创建测试用户
        logger.info("3. 创建测试用户...")
        create_test_users()
        
        # 4. 测试登录（可选）
        if len(sys.argv) > 1 and sys.argv[1] == "--test":
            logger.info("4. 测试登录功能...")
            test_login()
        
        print("\n✅ 用户登录问题修复完成！")
        print("\n可以使用以下账户登录:")
        print("  普通用户: user / 123456")
        print("  管理员: admin / admin123")
        print("\n登录地址:")
        print("  用户登录: http://localhost:8008/user/login")
        print("  管理员登录: http://localhost:8008/admin")
        
    except Exception as e:
        logger.error(f"❌ 修复失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
