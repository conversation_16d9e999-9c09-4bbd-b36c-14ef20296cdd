#!/usr/bin/env python3
"""
测试支付流程
"""

import requests
import json
import time

def test_payment_flow():
    """测试完整的支付流程"""
    base_url = "http://localhost:8008"
    
    print("🔍 测试完整支付流程...")
    
    # 1. 创建支付调试
    print("\n1. 创建支付调试...")
    create_data = {
        "debug_name": "流程测试支付",
        "payment_mode": "face_to_face",
        "user_id": "flow_test_user",
        "product_id": 1,
        "amount": 0.01,
        "subject": "流程测试订单",
        "body": "测试完整支付流程",
        "timeout_minutes": 30
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/payment-debug/debug/create",
            json=create_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                order_data = data.get("data", {})
                print("   ✅ 支付订单创建成功")
                print(f"   调试ID: {order_data.get('id')}")
                print(f"   订单号: {order_data.get('order_no')}")
                print(f"   支付金额: ¥{order_data.get('amount')}")
                print(f"   订单状态: {order_data.get('debug_status')}")
                
                if order_data.get('qr_code'):
                    print("   ✅ 二维码已生成")
                    print(f"   二维码内容: {order_data.get('qr_code')[:50]}...")
                else:
                    print("   ❌ 二维码未生成")
                
                # 2. 模拟查询支付状态
                debug_id = order_data.get('id')
                if debug_id:
                    print(f"\n2. 查询支付状态 (ID: {debug_id})...")
                    
                    for i in range(3):
                        time.sleep(1)
                        try:
                            query_response = requests.get(f"{base_url}/api/payment-debug/debug/{debug_id}/query")
                            if query_response.status_code == 200:
                                query_data = query_response.json()
                                if query_data.get("success"):
                                    status_data = query_data.get("data", {})
                                    print(f"   查询 {i+1}: 状态 = {status_data.get('debug_status')}")
                                    
                                    if status_data.get('debug_status') == 'paid':
                                        print("   ✅ 支付已完成！")
                                        break
                                else:
                                    print(f"   查询 {i+1}: 失败 - {query_data.get('error')}")
                            else:
                                print(f"   查询 {i+1}: HTTP错误 - {query_response.status_code}")
                        except Exception as e:
                            print(f"   查询 {i+1}: 异常 - {str(e)}")
                    
                    # 3. 获取最终的调试记录
                    print(f"\n3. 获取最终调试记录...")
                    try:
                        list_response = requests.get(f"{base_url}/api/payment-debug/debug/list")
                        if list_response.status_code == 200:
                            list_data = list_response.json()
                            if list_data.get("success"):
                                records = list_data.get("data", {}).get("records", [])
                                # 找到我们创建的记录
                                our_record = None
                                for record in records:
                                    if record.get('id') == debug_id:
                                        our_record = record
                                        break
                                
                                if our_record:
                                    print("   ✅ 找到调试记录")
                                    print(f"   最终状态: {our_record.get('debug_status')}")
                                    print(f"   订单号: {our_record.get('order_no')}")
                                    print(f"   创建时间: {our_record.get('created_at')}")
                                    print(f"   最后查询: {our_record.get('last_query_time')}")
                                else:
                                    print("   ❌ 未找到调试记录")
                            else:
                                print(f"   ❌ 获取记录失败: {list_data.get('error')}")
                        else:
                            print(f"   ❌ HTTP错误: {list_response.status_code}")
                    except Exception as e:
                        print(f"   ❌ 异常: {str(e)}")
                
            else:
                print(f"   ❌ 创建失败: {data.get('error', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.text}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")

if __name__ == "__main__":
    test_payment_flow()
