{% extends "user/base_new.html" %}

{% block title %}支付中心 - FocuSee 用户中心{% endblock %}
{% block page_title %}支付中心{% endblock %}

{% block data %}
    products: [],
    selectedProduct: null,
    paymentMethod: 'face_to_face',
    userForm: {
        user_id: '',
        auth_code: ''
    },
    paymentResult: null,
    userOrders: [],
    loading: false,
    paymentLoading: false,
    statusChecking: false,
    ordersLoading: false
{% endblock %}

{% block content %}
<!-- 产品选择区域 -->
<el-card class="mb-4">
    <template #header>
        <div class="card-header">
            <span>选择产品</span>
        </div>
    </template>
    
    <el-row :gutter="20">
        <el-col :span="24" v-if="loading">
            <el-skeleton :rows="3" animated />
        </el-col>
        <el-col :span="8" v-for="product in products" :key="product.id" v-else>
            <el-card 
                class="product-card" 
                :class="{ 'selected': selectedProduct && selectedProduct.id === product.id }"
                @click="selectProduct(product)"
                shadow="hover">
                <div class="product-info">
                    <h3>{{ "{{ product.name }}" }}</h3>
                    <p class="product-description">{{ "{{ product.description }}" }}</p>
                    <div class="product-price">
                        <span class="price">¥{{ "{{ product.price }}" }}</span>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
</el-card>

<!-- 支付方式选择 -->
<el-card class="mb-4" v-if="selectedProduct">
    <template #header>
        <div class="card-header">
            <span>选择支付方式</span>
        </div>
    </template>
    
    <el-radio-group v-model="paymentMethod" size="large">
        <el-radio-button label="face_to_face">
            <el-icon><QrCode /></el-icon>
            扫码支付
        </el-radio-button>
        <el-radio-button label="order_code">
            <el-icon><CreditCard /></el-icon>
            订单码支付
        </el-radio-button>
    </el-radio-group>
</el-card>

<!-- 用户信息输入 -->
<el-card class="mb-4" v-if="selectedProduct">
    <template #header>
        <div class="card-header">
            <span>用户信息</span>
        </div>
    </template>
    
    <el-form :model="userForm" label-width="100px">
        <el-form-item label="用户ID" required>
            <el-input v-model="userForm.user_id" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="订单码" v-if="paymentMethod === 'order_code'" required>
            <el-input v-model="userForm.auth_code" placeholder="请输入支付宝付款码" />
        </el-form-item>
    </el-form>
</el-card>

<!-- 支付操作区域 -->
<el-card v-if="selectedProduct">
    <template #header>
        <div class="card-header">
            <span>支付操作</span>
        </div>
    </template>
    
    <div class="payment-actions">
        <el-button 
            type="primary" 
            size="large" 
            @click="createPayment"
            :loading="paymentLoading"
            :disabled="!userForm.user_id || (paymentMethod === 'order_code' && !userForm.auth_code)">
            {{ "{{ paymentMethod === 'face_to_face' ? '生成支付二维码' : '立即支付' }}" }}
        </el-button>
    </div>
</el-card>

<!-- 支付结果显示 -->
<el-card v-if="paymentResult" class="mt-4">
    <template #header>
        <div class="card-header">
            <span>支付结果</span>
        </div>
    </template>
    
    <!-- 扫码支付结果 -->
    <div v-if="paymentMethod === 'face_to_face' && paymentResult.success" class="qr-payment-result">
        <el-row :gutter="20">
            <el-col :span="12">
                <div class="qr-code-container">
                    <img :src="paymentResult.qr_image" alt="支付二维码" class="qr-code-image" />
                    <p class="qr-code-tip">请使用支付宝扫描二维码完成支付</p>
                </div>
            </el-col>
            <el-col :span="12">
                <div class="payment-info">
                    <el-descriptions title="订单信息" :column="1" border>
                        <el-descriptions-item label="订单号">{{ "{{ paymentResult.order_no }}" }}</el-descriptions-item>
                        <el-descriptions-item label="产品名称">{{ "{{ paymentResult.product_name }}" }}</el-descriptions-item>
                        <el-descriptions-item label="支付金额">¥{{ "{{ paymentResult.amount }}" }}</el-descriptions-item>
                        <el-descriptions-item label="过期时间">{{ "{{ formatTime(paymentResult.expire_time) }}" }}</el-descriptions-item>
                    </el-descriptions>
                    
                    <div class="payment-status mt-4">
                        <el-button @click="checkPaymentStatus" :loading="statusChecking">
                            查询支付状态
                        </el-button>
                        <el-button @click="cancelPayment" type="danger" plain>
                            取消支付
                        </el-button>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
    
    <!-- 订单码支付结果 -->
    <div v-else-if="paymentMethod === 'order_code'" class="order-code-result">
        <el-result
            :icon="paymentResult.success ? 'success' : 'error'"
            :title="paymentResult.success ? '支付成功' : '支付失败'"
            :sub-title="paymentResult.success ? `订单号: ${paymentResult.order_no}` : paymentResult.error">
            
            <template #extra v-if="paymentResult.success">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="交易号">{{ "{{ paymentResult.trade_no }}" }}</el-descriptions-item>
                    <el-descriptions-item label="支付金额">¥{{ "{{ paymentResult.amount }}" }}</el-descriptions-item>
                    <el-descriptions-item label="支付时间">{{ "{{ formatTime(paymentResult.paid_at) }}" }}</el-descriptions-item>
                    <el-descriptions-item label="产品名称">{{ "{{ paymentResult.product_name }}" }}</el-descriptions-item>
                </el-descriptions>
            </template>
        </el-result>
    </div>
    
    <!-- 错误信息 -->
    <el-alert v-if="!paymentResult.success" :title="paymentResult.error" type="error" show-icon />
</el-card>

<!-- 我的订单 -->
<el-card class="mt-4">
    <template #header>
        <div class="card-header">
            <span>我的订单</span>
            <el-button @click="loadUserOrders" :loading="ordersLoading">刷新</el-button>
        </div>
    </template>
    
    <el-table :data="userOrders" v-loading="ordersLoading">
        <el-table-column prop="order_no" label="订单号" width="180" />
        <el-table-column prop="product_name" label="产品名称" />
        <el-table-column prop="amount" label="金额" width="100">
            <template #default="scope">
                ¥{{ "{{ scope.row.amount }}" }}
            </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                    {{ "{{ getStatusText(scope.row.status) }}" }}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="scope">
                {{ "{{ formatTime(scope.row.created_at) }}" }}
            </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
            <template #default="scope">
                <el-button size="small" @click="viewOrderDetail(scope.row)">详情</el-button>
                <el-button 
                    v-if="scope.row.status === 'pending'" 
                    size="small" 
                    type="danger" 
                    @click="cancelOrder(scope.row.order_no)">
                    取消
                </el-button>
            </template>
        </el-table-column>
    </el-table>
</el-card>
{% endblock %}

{% block methods %}
// 加载产品列表
async loadProducts() {
    try {
        this.loading = true;
        const response = await fetch('/api/payment/test/products');
        const data = await response.json();

        if (data.success) {
            this.products = data.products;
        } else {
            this.$message.error(data.error || '加载产品失败');
        }
    } catch (error) {
        this.$message.error('加载产品失败: ' + error.message);
    } finally {
        this.loading = false;
    }
},

// 选择产品
selectProduct(product) {
    this.selectedProduct = product;
    this.paymentResult = null;
},

// 创建支付
async createPayment() {
    if (!this.selectedProduct || !this.userForm.user_id) {
        this.$message.warning('请选择产品并填写用户信息');
        return;
    }

    if (this.paymentMethod === 'order_code' && !this.userForm.auth_code) {
        this.$message.warning('请输入支付宝付款码');
        return;
    }

    try {
        this.paymentLoading = true;

        const endpoint = this.paymentMethod === 'face_to_face'
            ? '/api/payment/face-to-face'
            : '/api/payment/order-code';

        const requestData = {
            user_id: this.userForm.user_id,
            product_id: this.selectedProduct.id
        };

        if (this.paymentMethod === 'order_code') {
            requestData.auth_code = this.userForm.auth_code;
        }

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const data = await response.json();
        this.paymentResult = data;

        if (data.success) {
            if (this.paymentMethod === 'face_to_face') {
                this.$message.success('支付二维码生成成功，请扫码支付');
            } else {
                this.$message.success('支付成功');
                this.loadUserOrders();
            }
        } else {
            this.$message.error(data.error || '支付失败');
        }

    } catch (error) {
        this.$message.error('支付失败: ' + error.message);
    } finally {
        this.paymentLoading = false;
    }
},

// 获取状态类型
getStatusType(status) {
    const statusMap = {
        'pending': 'warning',
        'paid': 'success',
        'cancelled': 'info',
        'expired': 'danger',
        'failed': 'danger'
    };
    return statusMap[status] || 'info';
},

// 获取状态文本
getStatusText(status) {
    const statusMap = {
        'pending': '待支付',
        'paid': '已支付',
        'cancelled': '已取消',
        'expired': '已过期',
        'failed': '支付失败'
    };
    return statusMap[status] || status;
},

// 查询支付状态
async queryPaymentStatus(orderNo) {
    try {
        const response = await fetch('/api/payment/query', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                order_no: orderNo
            })
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                this.$message.success('支付状态查询成功');
                // 刷新订单列表
                this.loadUserOrders();
                return data;
            } else {
                this.$message.error(data.error || '查询失败');
                return null;
            }
        } else {
            this.$message.error('查询请求失败');
            return null;
        }
    } catch (error) {
        this.$message.error('查询失败: ' + error.message);
        return null;
    }
},

// 取消订单
async cancelOrder(orderNo) {
    try {
        await this.$confirm('确定要取消这个订单吗？', '确认取消', {
            type: 'warning'
        });

        const response = await fetch(`/api/payment/cancel/${orderNo}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                this.$message.success('订单已取消');
                // 刷新订单列表
                this.loadUserOrders();
            } else {
                this.$message.error(data.message || '取消失败');
            }
        } else {
            this.$message.error('取消请求失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            this.$message.error('取消失败: ' + error.message);
        }
    }
},

// 查看订单详情
viewOrderDetail(order) {
    this.$alert(`
        <div>
            <p><strong>订单号:</strong> ${order.order_no}</p>
            <p><strong>产品:</strong> ${order.product_name}</p>
            <p><strong>金额:</strong> ¥${order.amount}</p>
            <p><strong>状态:</strong> ${this.getStatusText(order.status)}</p>
            <p><strong>创建时间:</strong> ${new Date(order.created_at).toLocaleString()}</p>
            ${order.paid_at ? `<p><strong>支付时间:</strong> ${new Date(order.paid_at).toLocaleString()}</p>` : ''}
        </div>
    `, '订单详情', {
        dangerouslyUseHTMLString: true
    });
},

// 检查支付状态（用于支付结果页面）
async checkPaymentStatus() {
    if (!this.paymentResult || !this.paymentResult.order_no) {
        this.$message.error('没有可查询的订单');
        return;
    }

    this.statusChecking = true;
    try {
        const result = await this.queryPaymentStatus(this.paymentResult.order_no);
        if (result && result.status === 'paid') {
            this.$message.success('支付已完成！');
            // 更新支付结果
            this.paymentResult.status = 'paid';
            this.paymentResult.paid_at = result.paid_at;
            // 刷新订单列表
            this.loadUserOrders();
        } else if (result) {
            this.$message.info(`当前状态: ${this.getStatusText(result.status)}`);
        }
    } catch (error) {
        this.$message.error('查询失败');
    } finally {
        this.statusChecking = false;
    }
},

// 取消支付（用于支付结果页面）
async cancelPayment() {
    if (!this.paymentResult || !this.paymentResult.order_no) {
        this.$message.error('没有可取消的订单');
        return;
    }

    await this.cancelOrder(this.paymentResult.order_no);
    // 清除支付结果显示
    this.paymentResult = null;
},

// 格式化时间
formatTime(timeStr) {
    if (!timeStr) return '';
    return new Date(timeStr).toLocaleString('zh-CN');
},

// 加载用户订单列表
async loadUserOrders() {
    if (!this.userForm.user_id) {
        // 如果没有用户ID，清空订单列表
        this.userOrders = [];
        return;
    }

    try {
        this.ordersLoading = true;
        const response = await fetch(`/api/payment/orders/${this.userForm.user_id}?skip=0&limit=20`);

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                this.userOrders = data.orders || [];
            } else {
                this.$message.error(data.error || '加载订单失败');
                this.userOrders = [];
            }
        } else {
            this.$message.error('加载订单请求失败');
            this.userOrders = [];
        }
    } catch (error) {
        this.$message.error('加载订单失败: ' + error.message);
        this.userOrders = [];
    } finally {
        this.ordersLoading = false;
    }
}
{% endblock %}

{% block watch %}
// 监听用户ID变化，自动加载订单
'userForm.user_id': function(newVal, oldVal) {
    if (newVal && newVal !== oldVal) {
        this.loadUserOrders();
    }
}
{% endblock %}

{% block mounted %}
this.loadProducts();
{% endblock %}
