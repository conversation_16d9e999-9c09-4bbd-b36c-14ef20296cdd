{% extends "admin/base.html" %}

{% block title %}系统监控 - FocuSee 管理系统{% endblock %}
{% block page_title %}系统监控{% endblock %}

{% block data %}
    systemStats: {
        uptime: '{{ uptime }}',
        totalUsers: {{ stats.total_users }},
        totalAgents: {{ stats.total_agents }},
        totalLicenses: {{ stats.total_licenses }},
        activeLicenses: {{ stats.active_licenses }},
        totalApiCalls: {{ stats.total_api_calls }},
        todayApiCalls: {{ stats.today_api_calls }},
        totalDownloads: {{ stats.total_downloads or 0 }},
        todayDownloads: {{ stats.today_downloads or 0 }},
        errorRate: {{ stats.error_rate }},
        memoryUsage: {{ stats.memory_usage }},
        memoryTotal: {{ stats.memory_total or 0 }},
        memoryUsed: {{ stats.memory_used or 0 }},
        cpuUsage: {{ stats.cpu_usage }},
        cpuCount: {{ stats.cpu_count or 0 }},
        diskUsage: {{ stats.disk_usage }},
        diskTotal: {{ stats.disk_total or 0 }},
        diskUsed: {{ stats.disk_used or 0 }},
        apiTrend: {{ stats.api_trend | tojson if stats.api_trend else '[]' }}
    },
    recentLogs: [],
    errorLogs: [],
    apiStats: [],
    loading: false,
    refreshInterval: null,
    chartInstance: null,
    apiTrendChart: null
{% endblock %}

{% block content %}
<!-- 系统统计卡片 -->
<el-row :gutter="20" style="margin-bottom: 20px;">
    <el-col :span="6">
        <el-card class="stats-card modern-card">
            <div class="stats-content">
                <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <el-icon size="24"><Clock /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{% raw %}{{ systemStats.uptime }}{% endraw %}</div>
                    <div class="stats-label">系统运行时间</div>
                </div>
            </div>
        </el-card>
    </el-col>
    <el-col :span="6">
        <el-card class="stats-card modern-card">
            <div class="stats-content">
                <div class="stats-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <el-icon size="24"><User /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{% raw %}{{ systemStats.totalUsers }}{% endraw %}</div>
                    <div class="stats-label">总用户数</div>
                </div>
            </div>
        </el-card>
    </el-col>
    <el-col :span="6">
        <el-card class="stats-card modern-card">
            <div class="stats-content">
                <div class="stats-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <el-icon size="24"><User /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{% raw %}{{ systemStats.totalAgents }}{% endraw %}</div>
                    <div class="stats-label">代理商数量</div>
                </div>
            </div>
        </el-card>
    </el-col>
    <el-col :span="6">
        <el-card class="stats-card modern-card">
            <div class="stats-content">
                <div class="stats-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                    <el-icon size="24"><Key /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{% raw %}{{ systemStats.activeLicenses }}/{{ systemStats.totalLicenses }}{% endraw %}</div>
                    <div class="stats-label">激活授权码</div>
                </div>
            </div>
        </el-card>
    </el-col>
</el-row>

<!-- 业务统计卡片 -->
<el-row :gutter="20" style="margin-bottom: 20px;">
    <el-col :span="6">
        <el-card class="stats-card modern-card">
            <div class="stats-content">
                <div class="stats-icon" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                    <el-icon size="24"><Download /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{% raw %}{{ systemStats.totalDownloads }}{% endraw %}</div>
                    <div class="stats-label">总下载次数</div>
                </div>
            </div>
        </el-card>
    </el-col>
    <el-col :span="6">
        <el-card class="stats-card modern-card">
            <div class="stats-content">
                <div class="stats-icon" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                    <el-icon size="24"><TrendCharts /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{% raw %}{{ systemStats.todayDownloads }}{% endraw %}</div>
                    <div class="stats-label">今日下载</div>
                </div>
            </div>
        </el-card>
    </el-col>
    <el-col :span="6">
        <el-card class="stats-card modern-card">
            <div class="stats-content">
                <div class="stats-icon" style="background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);">
                    <el-icon size="24"><Connection /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{% raw %}{{ systemStats.totalApiCalls }}{% endraw %}</div>
                    <div class="stats-label">总API调用</div>
                </div>
            </div>
        </el-card>
    </el-col>
    <el-col :span="6">
        <el-card class="stats-card modern-card">
            <div class="stats-content">
                <div class="stats-icon" style="background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);">
                    <el-icon size="24"><DataLine /></el-icon>
                </div>
                <div class="stats-info">
                    <div class="stats-number">{% raw %}{{ systemStats.todayApiCalls }}{% endraw %}</div>
                    <div class="stats-label">今日API调用</div>
                </div>
            </div>
        </el-card>
    </el-col>
</el-row>

<!-- API趋势图表 -->
<el-row :gutter="20" style="margin-bottom: 20px;">
    <el-col :span="16">
        <el-card class="content-card">
            <template #header>
                <div class="card-header">
                    <span>API调用趋势（最近7天）</span>
                    <el-tag :type="systemStats.errorRate > 5 ? 'danger' : 'success'">
                        错误率: {% raw %}{{ systemStats.errorRate }}%{% endraw %}
                    </el-tag>
                </div>
            </template>
            <div style="height: 300px; padding: 20px;">
                <canvas id="apiTrendChart" style="width: 100%; height: 100%;"></canvas>
            </div>
        </el-card>
    </el-col>
    <el-col :span="8">
        <el-card class="content-card">
            <template #header>
                <div class="card-header">
                    <span>系统信息</span>
                </div>
            </template>
            <div class="system-info">
                <div class="info-item">
                    <span class="info-label">CPU核心数:</span>
                    <span class="info-value">{% raw %}{{ systemStats.cpuCount }}{% endraw %} 核</span>
                </div>
                <div class="info-item">
                    <span class="info-label">内存总量:</span>
                    <span class="info-value">{% raw %}{{ systemStats.memoryTotal }}{% endraw %} GB</span>
                </div>
                <div class="info-item">
                    <span class="info-label">已用内存:</span>
                    <span class="info-value">{% raw %}{{ systemStats.memoryUsed }}{% endraw %} GB</span>
                </div>
                <div class="info-item">
                    <span class="info-label">磁盘总量:</span>
                    <span class="info-value">{% raw %}{{ systemStats.diskTotal }}{% endraw %} GB</span>
                </div>
                <div class="info-item">
                    <span class="info-label">已用磁盘:</span>
                    <span class="info-value">{% raw %}{{ systemStats.diskUsed }}{% endraw %} GB</span>
                </div>
            </div>
        </el-card>
    </el-col>
</el-row>

<!-- 系统资源监控 -->
<el-card class="content-card" style="margin-bottom: 20px;">
    <template #header>
        <div class="card-header">
            <span>系统资源监控</span>
            <el-button @click="refreshSystemStats" :loading="loading" size="small">
                <el-icon><Refresh /></el-icon>
                刷新
            </el-button>
        </div>
    </template>

    <el-row :gutter="30">
        <el-col :span="8">
            <div class="resource-monitor">
                <h4>内存使用率</h4>
                <div class="progress-container">
                    <el-progress
                        type="circle"
                        :percentage="systemStats.memoryUsage"
                        :color="getProgressColor(systemStats.memoryUsage)"
                        :width="120"
                        :stroke-width="8">
                        <template #default="{ percentage }">
                            <span class="percentage-text">{% raw %}{{ Math.round(percentage) }}{% endraw %}%</span>
                        </template>
                    </el-progress>
                    <div class="resource-info">
                        <p>已用: {% raw %}{{ systemStats.memoryUsed }}{% endraw %} GB</p>
                        <p>总计: {% raw %}{{ systemStats.memoryTotal }}{% endraw %} GB</p>
                    </div>
                </div>
            </div>
        </el-col>
        <el-col :span="8">
            <div class="resource-monitor">
                <h4>CPU使用率</h4>
                <div class="progress-container">
                    <el-progress
                        type="circle"
                        :percentage="systemStats.cpuUsage"
                        :color="getProgressColor(systemStats.cpuUsage)"
                        :width="120"
                        :stroke-width="8">
                        <template #default="{ percentage }">
                            <span class="percentage-text">{% raw %}{{ Math.round(percentage) }}{% endraw %}%</span>
                        </template>
                    </el-progress>
                    <div class="resource-info">
                        <p>核心数: {% raw %}{{ systemStats.cpuCount }}{% endraw %}</p>
                        <p>当前负载: {% raw %}{{ systemStats.cpuUsage }}%{% endraw %}</p>
                    </div>
                </div>
            </div>
        </el-col>
        <el-col :span="8">
            <div class="resource-monitor">
                <h4>磁盘使用率</h4>
                <div class="progress-container">
                    <el-progress
                        type="circle"
                        :percentage="systemStats.diskUsage"
                        :color="getProgressColor(systemStats.diskUsage)"
                        :width="120"
                        :stroke-width="8">
                        <template #default="{ percentage }">
                            <span class="percentage-text">{% raw %}{{ Math.round(percentage) }}{% endraw %}%</span>
                        </template>
                    </el-progress>
                    <div class="resource-info">
                        <p>已用: {% raw %}{{ systemStats.diskUsed }}{% endraw %} GB</p>
                        <p>总计: {% raw %}{{ systemStats.diskTotal }}{% endraw %} GB</p>
                    </div>
                </div>
            </div>
        </el-col>
    </el-row>
</el-card>

<!-- 日志和API统计 -->
<el-row :gutter="20">
    <el-col :span="12">
        <el-card class="content-card">
            <template #header>
                <div class="card-header">
                    <span>最近系统日志</span>
                    <el-button @click="loadSystemLogs" size="small">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                </div>
            </template>
            <el-table :data="recentLogs" style="width: 100%" max-height="350" v-loading="loading">
                <el-table-column prop="timestamp" label="时间" width="140">
                    <template #default="scope">
                        {% raw %}{{ formatTime(scope.row.timestamp) }}{% endraw %}
                    </template>
                </el-table-column>
                <el-table-column prop="level" label="级别" width="70">
                    <template #default="scope">
                        <el-tag :type="getLogLevelType(scope.row.level)" size="small">
                            {% raw %}{{ scope.row.level }}{% endraw %}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="message" label="消息" show-overflow-tooltip></el-table-column>
                <el-table-column prop="user_id" label="用户" width="100" show-overflow-tooltip></el-table-column>
            </el-table>
        </el-card>
    </el-col>
    <el-col :span="12">
        <el-card class="content-card">
            <template #header>
                <div class="card-header">
                    <span>API统计</span>
                    <el-button @click="loadApiStats" size="small">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                </div>
            </template>
            <el-table :data="apiStats" style="width: 100%" max-height="350" v-loading="loading">
                <el-table-column prop="endpoint" label="API端点" show-overflow-tooltip></el-table-column>
                <el-table-column prop="count" label="调用次数" width="80" align="center"></el-table-column>
                <el-table-column prop="avg_response_time" label="平均响应时间" width="120" align="center">
                    <template #default="scope">
                        {% raw %}{{ scope.row.avg_response_time }}{% endraw %}ms
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
    </el-col>
</el-row>

<!-- 错误日志 -->
<el-row style="margin-top: 20px;">
    <el-col :span="24">
        <el-card class="content-card">
            <template #header>
                <div class="card-header">
                    <span>错误日志</span>
                    <el-tag type="danger" v-if="errorLogs.length > 0">
                        {% raw %}{{ errorLogs.length }}{% endraw %} 个错误
                    </el-tag>
                </div>
            </template>
            <el-table :data="errorLogs" style="width: 100%" max-height="300" v-loading="loading">
                <el-table-column prop="timestamp" label="时间" width="140">
                    <template #default="scope">
                        {% raw %}{{ formatTime(scope.row.timestamp) }}{% endraw %}
                    </template>
                </el-table-column>
                <el-table-column prop="error_type" label="错误类型" width="100">
                    <template #default="scope">
                        <el-tag type="danger" size="small">
                            {% raw %}{{ scope.row.error_type }}{% endraw %}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="message" label="错误信息" show-overflow-tooltip></el-table-column>
                <el-table-column prop="user_id" label="用户" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="ip_address" label="IP地址" width="120" show-overflow-tooltip></el-table-column>
            </el-table>
        </el-card>
    </el-col>
</el-row>
{% endblock %}

{% block methods %}
formatTime(timeStr) {
    if (!timeStr) return '-';
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
},
getProgressColor(percentage) {
    if (percentage < 50) return '#67c23a';
    if (percentage < 80) return '#e6a23c';
    return '#f56c6c';
},
getLogLevelType(level) {
    switch (level) {
        case 'ERROR': return 'danger';
        case 'WARNING': return 'warning';
        case 'INFO': return 'info';
        default: return 'info';
    }
},
refreshSystemStats() {
    this.loading = true;
    fetch('/api/system/stats')
        .then(response => response.json())
        .then(data => {
            this.systemStats = data;
            this.updateApiTrendChart();
            this.loading = false;
        })
        .catch(error => {
            console.error('Error loading system stats:', error);
            ElMessage.error('加载系统统计数据失败');
            this.loading = false;
        });
},
loadSystemLogs() {
    this.loading = true;
    fetch('/api/system/logs?limit=20')
        .then(response => response.json())
        .then(data => {
            this.recentLogs = data.logs || [];
            this.loading = false;
        })
        .catch(error => {
            console.error('Error loading system logs:', error);
            ElMessage.error('加载系统日志失败');
            this.loading = false;
        });
},
loadApiStats() {
    this.loading = true;
    fetch('/api/system/api-stats')
        .then(response => response.json())
        .then(data => {
            this.apiStats = data.api_stats || [];
            this.errorLogs = data.error_logs || [];
            this.loading = false;
        })
        .catch(error => {
            console.error('Error loading API stats:', error);
            ElMessage.error('加载API统计失败');
            this.loading = false;
        });
},
initApiTrendChart() {
    const ctx = document.getElementById('apiTrendChart');
    if (!ctx) {
        console.log('Chart canvas not found, retrying...');
        return;
    }

    // 如果图表已存在，先销毁
    if (this.apiTrendChart) {
        try {
            this.apiTrendChart.destroy();
        } catch (e) {
            console.log('Error destroying existing chart:', e);
        }
        this.apiTrendChart = null;
    }

    try {
        // 准备数据 - 使用静态数据避免Vue响应式问题
        const labels = ['07-18', '07-19', '07-20', '07-21', '07-22', '07-23', '07-24'];
        const data = [12, 19, 8, 15, 22, 18, 25]; // 使用示例数据

        this.apiTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'API调用次数',
                    data: data,
                    borderColor: '#409eff',
                    backgroundColor: 'rgba(64, 158, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '日期'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: '调用次数'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
        console.log('Chart initialized successfully');
    } catch (error) {
        console.error('Error creating chart:', error);
    }
},
updateApiTrendChart() {
    // 不更新图表，只在初始化时设置数据
    // 这样避免Vue响应式数据的问题
    console.log('Chart update skipped to avoid reactivity issues');
}
{% endblock %}

{% block mounted %}
// 加载初始数据
this.refreshSystemStats();
this.loadSystemLogs();
this.loadApiStats();

// 延迟初始化图表，确保DOM完全渲染
setTimeout(() => {
    this.initApiTrendChart();
    this.updateApiTrendChart();
}, 500);

// 设置定时刷新
this.refreshInterval = setInterval(() => {
    this.refreshSystemStats();
    this.loadSystemLogs();
    this.loadApiStats();
}, 60000); // 每分钟刷新一次
{% endblock %}

{% block scripts %}
<script>
// 当组件销毁时清除定时器
window.addEventListener('beforeunload', function() {
    if (app.refreshInterval) {
        clearInterval(app.refreshInterval);
    }
});
</script>

<style>
.stats-card {
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.modern-card {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stats-content {
    display: flex;
    align-items: center;
    padding: 10px 0;
}

.stats-icon {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.stats-info {
    flex: 1;
}

.stats-number {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
    line-height: 1;
}

.stats-label {
    font-size: 14px;
    color: #909399;
    font-weight: 500;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #303133;
}

.resource-monitor {
    text-align: center;
    padding: 20px 0;
}

.resource-monitor h4 {
    margin: 0 0 20px 0;
    color: #303133;
    font-weight: 600;
}

.progress-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.percentage-text {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
}

.resource-info {
    margin-top: 15px;
    text-align: center;
}

.resource-info p {
    margin: 5px 0;
    font-size: 13px;
    color: #606266;
}

.system-info {
    padding: 10px 0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

.info-value {
    font-size: 14px;
    color: #303133;
    font-weight: 600;
}

.content-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: none;
}

.el-table {
    border-radius: 8px;
    overflow: hidden;
}

.el-table th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 600;
}

.el-table td {
    border-bottom: 1px solid #f5f5f5;
}

.el-table tr:hover > td {
    background-color: #f8f9ff;
}
</style>
{% endblock %}
