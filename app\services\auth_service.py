from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import json
import logging

from app.models.user import User
from app.models.download_log import DownloadLog
from app.models import (
    License, LicenseStatus, Order, OrderStatus, Product,
    AuthLog, AuthAction, AuthResult
)
from app.schemas.download import VerifyRequest, DownloadStatsRequest

logger = logging.getLogger(__name__)

class AuthService:
    """授权验证服务类"""

    def __init__(self, db: Session):
        self.db = db

    def _log_auth_action(
        self,
        action: AuthAction,
        result: AuthResult,
        license_id: Optional[int] = None,
        order_id: Optional[int] = None,
        product_id: Optional[int] = None,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_params: Optional[Dict] = None,
        response_data: Optional[Dict] = None,
        error_message: Optional[str] = None,
        quota_before: Optional[int] = None,
        quota_after: Optional[int] = None
    ):
        """记录授权操作日志"""
        try:
            auth_log = AuthLog(
                action=action,
                result=result,
                license_id=license_id,
                order_id=order_id,
                product_id=product_id,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_params=json.dumps(request_params) if request_params else None,
                response_data=json.dumps(response_data) if response_data else None,
                error_message=error_message,
                quota_before=quota_before,
                quota_after=quota_after
            )
            self.db.add(auth_log)
            self.db.commit()
        except Exception as e:
            logger.error(f"Failed to log auth action: {str(e)}")
            self.db.rollback()

    @staticmethod
    def verify_user(db: Session, request: VerifyRequest) -> bool:
        """验证用户身份"""
        try:
            user = db.query(User).filter(
                User.user_id == request.user_id,
                User.license_key == request.license_key,
                User.is_active == True
            ).first()
            
            if user:
                logger.info(f"User {request.user_id} verified successfully")
                return True
            else:
                logger.warning(f"User {request.user_id} verification failed")
                return False
                
        except Exception as e:
            logger.error(f"Error verifying user {request.user_id}: {str(e)}")
            return False
    
    @staticmethod
    def log_download_stats(db: Session, request: DownloadStatsRequest, user_agent: Optional[str] = None) -> bool:
        """记录下载统计"""
        try:
            download_log = DownloadLog(
                user_id=request.user_id,
                license_key=request.license_key,
                system_id=request.system_id,
                computer_name=request.computer_name,
                ip_address=request.ip_address,
                file_name=request.file,
                user_agent=user_agent
            )
            
            db.add(download_log)
            db.commit()
            db.refresh(download_log)
            
            logger.info(f"Download stats logged for user {request.user_id}, file {request.file}")
            return True
            
        except Exception as e:
            logger.error(f"Error logging download stats: {str(e)}")
            db.rollback()
            return False

    def verify_by_order_number(
        self,
        order_number: str,
        user_id: str,
        product_code: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """通过订单号验证授权"""
        request_params = {
            "order_number": order_number,
            "user_id": user_id,
            "product_code": product_code
        }

        try:
            # 查找订单
            order = self.db.query(Order).filter(Order.order_number == order_number).first()
            if not order:
                self._log_auth_action(
                    AuthAction.VERIFY_ORDER,
                    AuthResult.FAILED,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message="Order not found"
                )
                return False, {"error": "订单不存在"}

            # 检查订单状态
            if order.status != OrderStatus.COMPLETED:
                self._log_auth_action(
                    AuthAction.VERIFY_ORDER,
                    AuthResult.FAILED,
                    order_id=order.id,
                    product_id=order.product_id,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message=f"Order status is {order.status.value}"
                )
                return False, {"error": "订单状态无效"}

            # 检查产品类型（如果指定）
            if product_code and order.product.code != product_code:
                self._log_auth_action(
                    AuthAction.VERIFY_ORDER,
                    AuthResult.INVALID_PRODUCT,
                    order_id=order.id,
                    product_id=order.product_id,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message=f"Product mismatch: expected {product_code}, got {order.product.code}"
                )
                return False, {"error": "产品类型不匹配"}

            # 检查订单是否过期
            if order.expire_date and order.expire_date < datetime.now():
                self._log_auth_action(
                    AuthAction.VERIFY_ORDER,
                    AuthResult.EXPIRED,
                    order_id=order.id,
                    product_id=order.product_id,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message="Order expired"
                )
                return False, {"error": "订单已过期"}

            # 获取订单关联的授权码
            licenses = order.licenses
            active_licenses = [lic for lic in licenses if lic.status == LicenseStatus.ACTIVE]

            response_data = {
                "order_id": order.id,
                "order_number": order.order_number,
                "product": {
                    "id": order.product.id,
                    "name": order.product.name,
                    "code": order.product.code
                },
                "quantity": order.quantity,
                "expire_date": order.expire_date.isoformat() if order.expire_date else None,
                "licenses": [
                    {
                        "license_code": lic.license_code,
                        "status": lic.status.value,
                        "max_api_calls": lic.max_api_calls,
                        "used_api_calls": lic.used_api_calls,
                        "expire_date": lic.expire_date.isoformat() if lic.expire_date else None
                    }
                    for lic in active_licenses
                ]
            }

            self._log_auth_action(
                AuthAction.VERIFY_ORDER,
                AuthResult.SUCCESS,
                order_id=order.id,
                product_id=order.product_id,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_params=request_params,
                response_data=response_data
            )

            return True, response_data

        except Exception as e:
            logger.error(f"Error in verify_by_order_number: {str(e)}")
            self._log_auth_action(
                AuthAction.VERIFY_ORDER,
                AuthResult.FAILED,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_params=request_params,
                error_message=str(e)
            )
            return False, {"error": "验证过程中发生错误"}

    def verify_by_license_code(
        self,
        license_code: str,
        user_id: str,
        product_code: Optional[str] = None,
        consume_quota: bool = False,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """通过授权码验证授权"""
        request_params = {
            "license_code": license_code,
            "user_id": user_id,
            "product_code": product_code,
            "consume_quota": consume_quota
        }

        try:
            # 查找授权码
            license_obj = self.db.query(License).filter(License.license_code == license_code).first()
            if not license_obj:
                self._log_auth_action(
                    AuthAction.VERIFY_LICENSE,
                    AuthResult.INVALID_LICENSE,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message="License not found"
                )
                return False, {"error": "授权码不存在"}

            # 检查授权码状态
            if license_obj.status != LicenseStatus.ACTIVE:
                self._log_auth_action(
                    AuthAction.VERIFY_LICENSE,
                    AuthResult.INVALID_LICENSE,
                    license_id=license_obj.id,
                    product_id=license_obj.product_id,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message=f"License status is {license_obj.status.value}"
                )
                return False, {"error": "授权码状态无效"}

            # 检查用户绑定
            if license_obj.user_id and license_obj.user_id != user_id:
                self._log_auth_action(
                    AuthAction.VERIFY_LICENSE,
                    AuthResult.UNAUTHORIZED,
                    license_id=license_obj.id,
                    product_id=license_obj.product_id,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message=f"License bound to different user: {license_obj.user_id}"
                )
                return False, {"error": "授权码已绑定其他用户"}

            # 检查产品类型（如果指定）
            if product_code and license_obj.product.code != product_code:
                self._log_auth_action(
                    AuthAction.VERIFY_LICENSE,
                    AuthResult.INVALID_PRODUCT,
                    license_id=license_obj.id,
                    product_id=license_obj.product_id,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message=f"Product mismatch: expected {product_code}, got {license_obj.product.code}"
                )
                return False, {"error": "产品类型不匹配"}

            # 检查过期时间
            if license_obj.expire_date and license_obj.expire_date < datetime.now():
                self._log_auth_action(
                    AuthAction.VERIFY_LICENSE,
                    AuthResult.EXPIRED,
                    license_id=license_obj.id,
                    product_id=license_obj.product_id,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message="License expired"
                )
                return False, {"error": "授权码已过期"}

            # 检查API调用次数限制
            quota_before = license_obj.used_api_calls
            if license_obj.max_api_calls > 0 and license_obj.used_api_calls >= license_obj.max_api_calls:
                self._log_auth_action(
                    AuthAction.VERIFY_LICENSE,
                    AuthResult.QUOTA_EXCEEDED,
                    license_id=license_obj.id,
                    product_id=license_obj.product_id,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    quota_before=quota_before,
                    error_message="API quota exceeded"
                )
                return False, {"error": "API调用次数已用完"}

            # 消费配额（如果需要）
            quota_after = quota_before
            if consume_quota:
                license_obj.used_api_calls += 1
                quota_after = license_obj.used_api_calls

                # 如果没有绑定用户，则绑定当前用户
                if not license_obj.user_id:
                    license_obj.user_id = user_id

                self.db.commit()

            response_data = {
                "license_id": license_obj.id,
                "license_code": license_obj.license_code,
                "product": {
                    "id": license_obj.product.id,
                    "name": license_obj.product.name,
                    "code": license_obj.product.code
                },
                "user_id": license_obj.user_id,
                "max_api_calls": license_obj.max_api_calls,
                "used_api_calls": quota_after,
                "remaining_calls": license_obj.max_api_calls - quota_after if license_obj.max_api_calls > 0 else -1,
                "expire_date": license_obj.expire_date.isoformat() if license_obj.expire_date else None,
                "status": license_obj.status.value
            }

            action = AuthAction.API_CALL if consume_quota else AuthAction.VERIFY_LICENSE
            self._log_auth_action(
                action,
                AuthResult.SUCCESS,
                license_id=license_obj.id,
                product_id=license_obj.product_id,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_params=request_params,
                response_data=response_data,
                quota_before=quota_before,
                quota_after=quota_after
            )

            return True, response_data

        except Exception as e:
            logger.error(f"Error in verify_by_license_code: {str(e)}")
            self.db.rollback()
            self._log_auth_action(
                AuthAction.VERIFY_LICENSE,
                AuthResult.FAILED,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_params=request_params,
                error_message=str(e)
            )
            return False, {"error": "验证过程中发生错误"}

    def verify_by_product_type(
        self,
        user_id: str,
        product_code: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """验证用户对特定产品类型的授权"""
        request_params = {
            "user_id": user_id,
            "product_code": product_code
        }

        try:
            # 查找产品
            product = self.db.query(Product).filter(Product.code == product_code).first()
            if not product:
                self._log_auth_action(
                    AuthAction.VERIFY_PRODUCT,
                    AuthResult.INVALID_PRODUCT,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message="Product not found"
                )
                return False, {"error": "产品不存在"}

            # 查找用户的有效授权码
            valid_licenses = self.db.query(License).filter(
                License.user_id == user_id,
                License.product_id == product.id,
                License.status == LicenseStatus.ACTIVE
            ).all()

            # 过滤未过期的授权码
            active_licenses = []
            for license_obj in valid_licenses:
                if not license_obj.expire_date or license_obj.expire_date > datetime.now():
                    # 检查是否还有剩余调用次数
                    if license_obj.max_api_calls <= 0 or license_obj.used_api_calls < license_obj.max_api_calls:
                        active_licenses.append(license_obj)

            if not active_licenses:
                self._log_auth_action(
                    AuthAction.VERIFY_PRODUCT,
                    AuthResult.UNAUTHORIZED,
                    product_id=product.id,
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_params=request_params,
                    error_message="No valid licenses found for product"
                )
                return False, {"error": "没有有效的产品授权"}

            response_data = {
                "product": {
                    "id": product.id,
                    "name": product.name,
                    "code": product.code
                },
                "licenses": [
                    {
                        "license_id": lic.id,
                        "license_code": lic.license_code,
                        "max_api_calls": lic.max_api_calls,
                        "used_api_calls": lic.used_api_calls,
                        "remaining_calls": lic.max_api_calls - lic.used_api_calls if lic.max_api_calls > 0 else -1,
                        "expire_date": lic.expire_date.isoformat() if lic.expire_date else None
                    }
                    for lic in active_licenses
                ]
            }

            self._log_auth_action(
                AuthAction.VERIFY_PRODUCT,
                AuthResult.SUCCESS,
                product_id=product.id,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_params=request_params,
                response_data=response_data
            )

            return True, response_data

        except Exception as e:
            logger.error(f"Error in verify_by_product_type: {str(e)}")
            self._log_auth_action(
                AuthAction.VERIFY_PRODUCT,
                AuthResult.FAILED,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_params=request_params,
                error_message=str(e)
            )
            return False, {"error": "验证过程中发生错误"}

    def verify_mixed_auth(
        self,
        user_id: str,
        license_code: Optional[str] = None,
        order_number: Optional[str] = None,
        product_code: Optional[str] = None,
        consume_quota: bool = False,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """混合授权验证 - 支持多种验证方式的组合"""
        request_params = {
            "user_id": user_id,
            "license_code": license_code,
            "order_number": order_number,
            "product_code": product_code,
            "consume_quota": consume_quota
        }

        try:
            # 优先级1: 如果提供了授权码，先验证授权码
            if license_code:
                success, result = self.verify_by_license_code(
                    license_code=license_code,
                    user_id=user_id,
                    product_code=product_code,
                    consume_quota=consume_quota,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                if success:
                    result["auth_method"] = "license_code"
                    self._log_auth_action(
                        AuthAction.VERIFY_MIXED,
                        AuthResult.SUCCESS,
                        license_id=result.get("license_id"),
                        product_id=result.get("product", {}).get("id"),
                        user_id=user_id,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        request_params=request_params,
                        response_data=result
                    )
                    return True, result

            # 优先级2: 如果提供了订单号，验证订单
            if order_number:
                success, result = self.verify_by_order_number(
                    order_number=order_number,
                    user_id=user_id,
                    product_code=product_code,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                if success:
                    result["auth_method"] = "order_number"
                    self._log_auth_action(
                        AuthAction.VERIFY_MIXED,
                        AuthResult.SUCCESS,
                        order_id=result.get("order_id"),
                        product_id=result.get("product", {}).get("id"),
                        user_id=user_id,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        request_params=request_params,
                        response_data=result
                    )
                    return True, result

            # 优先级3: 如果提供了产品代码，验证产品授权
            if product_code:
                success, result = self.verify_by_product_type(
                    user_id=user_id,
                    product_code=product_code,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                if success:
                    result["auth_method"] = "product_type"
                    self._log_auth_action(
                        AuthAction.VERIFY_MIXED,
                        AuthResult.SUCCESS,
                        product_id=result.get("product", {}).get("id"),
                        user_id=user_id,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        request_params=request_params,
                        response_data=result
                    )
                    return True, result

            # 如果所有验证都失败
            self._log_auth_action(
                AuthAction.VERIFY_MIXED,
                AuthResult.FAILED,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_params=request_params,
                error_message="All verification methods failed"
            )
            return False, {"error": "所有验证方式都失败"}

        except Exception as e:
            logger.error(f"Error in verify_mixed_auth: {str(e)}")
            self._log_auth_action(
                AuthAction.VERIFY_MIXED,
                AuthResult.FAILED,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_params=request_params,
                error_message=str(e)
            )
            return False, {"error": "验证过程中发生错误"}
