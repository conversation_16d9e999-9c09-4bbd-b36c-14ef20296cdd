from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric
from sqlalchemy.sql import func
from app.database import Base

class Product(Base):
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="产品名称")
    code = Column(String(50), unique=True, nullable=False, comment="产品代码")
    description = Column(Text, comment="产品描述")
    version = Column(String(20), comment="产品版本")
    price = Column(Numeric(10, 2), comment="产品价格")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<Product(name='{self.name}', code='{self.code}', is_active={self.is_active})>"
