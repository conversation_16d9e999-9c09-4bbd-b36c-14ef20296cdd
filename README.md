# FocuSee Registration System

基于 FastAPI + MySQL + Element Plus 的 FocuSee 注册管理系统，提供用户验证、文件下载和管理界面功能。

## 功能特性

### 🔐 用户验证系统
- 用户ID和授权码验证
- 系统信息记录（计算机名、IP地址等）
- 活跃用户状态管理

### 📥 文件下载管理
- 安全的文件下载接口
- 下载权限验证
- 下载行为记录和统计

### 🎛️ 管理员界面
- 现代化的 Element Plus UI
- 用户管理（增删改查）
- 下载统计和数据可视化
- 实时数据监控

### 📊 统计分析
- 用户活跃度统计
- 下载趋势分析
- 文件热度排行
- 系统使用情况监控

## 技术栈

- **后端**: FastAPI (Python)
- **数据库**: MySQL
- **前端**: Element Plus + Vue 3
- **认证**: JWT Token
- **ORM**: SQLAlchemy
- **架构**: 单体应用

## 快速开始

### 1. 环境要求

- Python 3.8+
- MySQL 5.7+
- pip

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置数据库

编辑 `.env` 文件：

```env
# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/focusee_db

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
APP_NAME=FocuSee Registration System
APP_VERSION=1.0.0
DEBUG=True

# 文件存储配置
DOWNLOADS_DIR=./downloads
MAX_FILE_SIZE=100000000  # 100MB

# 管理员默认账户
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
```

### 4. 初始化数据库

```bash
python init_db.py
```

### 5. 启动应用

```bash
python run.py
```

或者使用 uvicorn：

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8008 --reload
```

### 6. 访问系统

- **管理界面**: http://localhost:8008
- **API文档**: http://localhost:8008/docs
- **默认管理员账户**: admin / admin123

## API 接口

### 用户验证

```http
POST /api/verify
Content-Type: application/json

{
  "user_id": "用户ID",
  "license_key": "授权码",
  "system_id": "系统序列号",
  "computer_name": "计算机名称",
  "ip_address": "IP地址"
}
```

### 下载统计

```http
POST /api/download_stats
Content-Type: application/json

{
  "user_id": "用户ID",
  "license_key": "授权码",
  "system_id": "系统序列号",
  "computer_name": "计算机名称",
  "ip_address": "IP地址",
  "file": "文件名"
}
```

### 文件下载

```http
GET /downloads/{filename}?user=用户ID&key=授权码
```

## 管理界面功能

### 仪表板
- 用户统计概览
- 下载趋势图表
- 最近下载记录
- 系统活跃度指标

### 用户管理
- 添加/编辑用户
- 启用/禁用用户
- 用户信息查看
- 批量操作

### 下载统计
- 下载记录查询
- 按用户/文件/时间筛选
- 数据导出功能
- 统计图表展示

## 部署说明

### 生产环境配置

1. **修改安全配置**
   ```env
   SECRET_KEY=生产环境密钥
   DEBUG=False
   ADMIN_PASSWORD=强密码
   ```

2. **配置 HTTPS**
   - 使用 Nginx 反向代理
   - 配置 SSL 证书

3. **数据库优化**
   - 配置连接池
   - 设置索引优化
   - 定期备份

### Docker 部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8008

CMD ["python", "run.py"]
```

## 目录结构

```
focusee-registration/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI 主应用
│   ├── config.py            # 配置文件
│   ├── database.py          # 数据库连接
│   ├── models/              # 数据模型
│   ├── schemas/             # 数据模式
│   ├── api/                 # API 路由
│   ├── services/            # 业务逻辑
│   └── templates/           # HTML 模板
├── downloads/               # 下载文件目录
├── requirements.txt         # 依赖包
├── .env                     # 环境变量
├── init_db.py              # 数据库初始化
├── run.py                  # 启动脚本
└── README.md               # 说明文档
```

## 许可证

MIT License

## 支持

如有问题，请联系开发团队或提交 Issue。
