from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.sql import func
from app.database import Base

class UserProfile(Base):
    __tablename__ = "user_profiles"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(100), unique=True, nullable=False, comment="用户ID")
    display_name = Column(String(100), comment="显示名称")
    email = Column(String(100), comment="邮箱")
    phone = Column(String(20), comment="电话")
    company = Column(String(100), comment="公司")
    department = Column(String(50), comment="部门")
    position = Column(String(50), comment="职位")
    address = Column(Text, comment="地址")
    preferences = Column(Text, comment="用户偏好设置（JSON格式）")
    avatar_url = Column(String(255), comment="头像URL")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<UserProfile(user_id='{self.user_id}', display_name='{self.display_name}')>"
