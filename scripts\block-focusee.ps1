# Block FocuSee Network Access Script
# This script automatically creates Windows Firewall rules to block FocuSee network connections

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Restarting..." -ForegroundColor Red
    Start-Process PowerShell -Verb RunAs -ArgumentList "-File `"$PSCommandPath`""
    exit
}

Write-Host "=== FocuSee Network Blocking Script ===" -ForegroundColor Green
Write-Host ""

# Common FocuSee installation paths
$commonPaths = @(
    "C:\Program Files\FocuSee\FocuSee.exe",
    "C:\Program Files (x86)\FocuSee\FocuSee.exe",
    "D:\Program Files\FocuSee\FocuSee.exe",
    "D:\Program Files (x86)\FocuSee\FocuSee.exe"
)

# Find FocuSee program path
$focuseeePath = $null
foreach ($path in $commonPaths) {
    if (Test-Path $path) {
        $focuseeePath = $path
        Write-Host "Found FocuSee program: $path" -ForegroundColor Green
        break
    }
}

# If not found, ask user for manual input
if (-not $focuseeePath) {
    Write-Host "FocuSee program not found in common locations." -ForegroundColor Yellow
    Write-Host "Please enter the full path to FocuSee.exe:"
    $focuseeePath = Read-Host
    
    if (-not (Test-Path $focuseeePath)) {
        Write-Host "Specified path does not exist: $focuseeePath" -ForegroundColor Red
        Write-Host "Press any key to exit..."
        Read-Host
        exit
    }
}

Write-Host ""
Write-Host "Will block program: $focuseeePath" -ForegroundColor Cyan

# Rule name
$ruleName = "Block FocuSee Network"

try {
    # Check if rule already exists
    $existingRule = Get-NetFirewallRule -DisplayName $ruleName -ErrorAction SilentlyContinue
    if ($existingRule) {
        Write-Host "Found existing rule, removing..." -ForegroundColor Yellow
        Remove-NetFirewallRule -DisplayName $ruleName
    }

    # Create outbound rule (block program from connecting to internet)
    Write-Host "Creating firewall outbound rule..." -ForegroundColor Yellow
    New-NetFirewallRule -DisplayName $ruleName -Direction Outbound -Program $focuseeePath -Action Block -Profile Domain,Private,Public -Description "Block FocuSee program network access"

    # Create inbound rule (block external connections to program)
    Write-Host "Creating firewall inbound rule..." -ForegroundColor Yellow
    New-NetFirewallRule -DisplayName "$ruleName (Inbound)" -Direction Inbound -Program $focuseeePath -Action Block -Profile Domain,Private,Public -Description "Block external network connections to FocuSee program"

    Write-Host ""
    Write-Host "Success! Firewall rules created successfully!" -ForegroundColor Green
    Write-Host "FocuSee program network connections have been blocked." -ForegroundColor Green
    
    # Display created rules
    Write-Host ""
    Write-Host "Created firewall rules:" -ForegroundColor Cyan
    Get-NetFirewallRule -DisplayName "*FocuSee*" | Select-Object DisplayName, Direction, Action, Enabled | Format-Table -AutoSize

} catch {
    Write-Host "Error creating firewall rules: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "To restore FocuSee network connections, run these commands:"
Write-Host "Remove-NetFirewallRule -DisplayName '$ruleName'" -ForegroundColor Yellow
Write-Host "Remove-NetFirewallRule -DisplayName '$ruleName (Inbound)'" -ForegroundColor Yellow

Write-Host ""
Write-Host "Press any key to exit..."
Read-Host
