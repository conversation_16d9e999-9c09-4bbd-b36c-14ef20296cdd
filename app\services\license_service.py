from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from app.models import License, Product, Agent, Order
from app.models.license import LicenseStatus
from app.schemas.license import LicenseCreate, LicenseUpdate, LicenseVerifyRequest, LicenseActivateRequest
import logging
import uuid
import secrets
from datetime import datetime

logger = logging.getLogger(__name__)

class LicenseService:
    """授权码管理服务"""
    
    @staticmethod
    def generate_license_code() -> str:
        """生成授权码"""
        # 生成一个32位的随机字符串
        return secrets.token_hex(16).upper()
    
    @staticmethod
    def create_license(db: Session, license_data: LicenseCreate) -> Optional[License]:
        """创建授权码"""
        try:
            # 验证产品是否存在
            product = db.query(Product).filter(Product.id == license_data.product_id).first()
            if not product:
                logger.error(f"Product {license_data.product_id} not found")
                return None
            
            # 验证订单是否存在（如果指定了订单）
            if license_data.order_id:
                order = db.query(Order).filter(Order.id == license_data.order_id).first()
                if not order:
                    logger.error(f"Order {license_data.order_id} not found")
                    return None
            
            # 验证代理商是否存在（如果指定了代理商）
            if license_data.agent_id:
                agent = db.query(Agent).filter(Agent.id == license_data.agent_id).first()
                if not agent:
                    logger.error(f"Agent {license_data.agent_id} not found")
                    return None
            
            # 生成授权码（如果没有提供）
            license_code = license_data.license_code or LicenseService.generate_license_code()
            
            # 检查授权码是否已存在
            existing_license = db.query(License).filter(License.license_code == license_code).first()
            if existing_license:
                logger.error(f"License code {license_code} already exists")
                return None

            license = License(
                license_code=license_code,
                order_id=license_data.order_id,
                agent_id=license_data.agent_id,
                product_id=license_data.product_id,
                user_id=license_data.user_id,
                max_api_calls=license_data.max_api_calls,
                expire_date=license_data.expire_date,
                status=license_data.status,
                notes=license_data.notes
            )
            
            db.add(license)
            db.commit()
            db.refresh(license)
            
            logger.info(f"License created: {license.license_code}")
            return license
            
        except Exception as e:
            logger.error(f"Error creating license: {str(e)}")
            db.rollback()
            return None
    
    @staticmethod
    def get_license_by_id(db: Session, license_id: int) -> Optional[License]:
        """根据ID获取授权码"""
        try:
            return db.query(License).filter(License.id == license_id).first()
        except Exception as e:
            logger.error(f"Error getting license by id {license_id}: {str(e)}")
            return None
    
    @staticmethod
    def get_license_by_code(db: Session, license_code: str) -> Optional[License]:
        """根据授权码获取授权"""
        try:
            return db.query(License).filter(License.license_code == license_code).first()
        except Exception as e:
            logger.error(f"Error getting license by code {license_code}: {str(e)}")
            return None
    
    @staticmethod
    def get_licenses(db: Session, skip: int = 0, limit: int = 100, 
                    agent_id: Optional[int] = None, 
                    product_id: Optional[int] = None,
                    status: Optional[LicenseStatus] = None,
                    user_id: Optional[str] = None) -> List[License]:
        """获取授权码列表"""
        try:
            query = db.query(License)

            # 默认过滤掉已撤销的授权码
            query = query.filter(License.status != LicenseStatus.REVOKED)

            if agent_id is not None:
                query = query.filter(License.agent_id == agent_id)

            if product_id is not None:
                query = query.filter(License.product_id == product_id)

            if status is not None:
                query = query.filter(License.status == status)

            if user_id is not None:
                query = query.filter(License.user_id == user_id)

            return query.order_by(License.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error getting licenses: {str(e)}")
            return []
    
    @staticmethod
    def update_license(db: Session, license_id: int, license_data: LicenseUpdate) -> Optional[License]:
        """更新授权码"""
        try:
            license = db.query(License).filter(License.id == license_id).first()
            if not license:
                logger.error(f"License {license_id} not found")
                return None
            
            # 验证产品是否存在（如果更新了产品）
            if license_data.product_id and license_data.product_id != license.product_id:
                product = db.query(Product).filter(Product.id == license_data.product_id).first()
                if not product:
                    logger.error(f"Product {license_data.product_id} not found")
                    return None
            
            # 更新字段
            update_data = license_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(license, field, value)
            
            db.commit()
            db.refresh(license)
            
            logger.info(f"License updated: {license.license_code}")
            return license
            
        except Exception as e:
            logger.error(f"Error updating license {license_id}: {str(e)}")
            db.rollback()
            return None
    
    @staticmethod
    def delete_license(db: Session, license_id: int) -> bool:
        """删除授权码（软删除：设置为已撤销）"""
        try:
            license = db.query(License).filter(License.id == license_id).first()
            if not license:
                logger.error(f"License {license_id} not found")
                return False
            
            license.status = LicenseStatus.REVOKED
            db.commit()
            
            logger.info(f"License revoked: {license.license_code}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting license {license_id}: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def verify_license(db: Session, verify_data: LicenseVerifyRequest) -> Optional[License]:
        """验证授权码"""
        try:
            license = db.query(License).filter(License.license_code == verify_data.license_code).first()
            if not license:
                logger.error(f"License {verify_data.license_code} not found")
                return None
            
            # 检查授权状态
            if license.status != LicenseStatus.ACTIVE:
                logger.error(f"License {verify_data.license_code} status is {license.status.value}")
                return None
            
            # 检查是否过期
            if license.expire_date and license.expire_date < datetime.now():
                logger.error(f"License {verify_data.license_code} has expired")
                # 自动设置为过期状态
                license.status = LicenseStatus.EXPIRED
                db.commit()
                return None
            
            # 检查API调用次数限制
            if license.max_api_calls > 0 and license.used_api_calls >= license.max_api_calls:
                logger.error(f"License {verify_data.license_code} has reached API call limit")
                return None
            
            # 检查用户绑定（如果授权码已绑定用户）
            if license.user_id and verify_data.user_id and license.user_id != verify_data.user_id:
                logger.error(f"License {verify_data.license_code} is bound to different user")
                return None
            
            logger.info(f"License verified: {verify_data.license_code}")
            return license
            
        except Exception as e:
            logger.error(f"Error verifying license {verify_data.license_code}: {str(e)}")
            return None
    
    @staticmethod
    def activate_license(db: Session, activate_data: LicenseActivateRequest) -> Optional[License]:
        """激活授权码"""
        try:
            license = db.query(License).filter(License.license_code == activate_data.license_code).first()
            if not license:
                logger.error(f"License {activate_data.license_code} not found")
                return None
            
            # 检查授权状态
            if license.status not in [LicenseStatus.INACTIVE, LicenseStatus.ACTIVE]:
                logger.error(f"License {activate_data.license_code} cannot be activated, status: {license.status.value}")
                return None
            
            # 检查是否过期
            if license.expire_date and license.expire_date < datetime.now():
                logger.error(f"License {activate_data.license_code} has expired")
                license.status = LicenseStatus.EXPIRED
                db.commit()
                return None
            
            # 绑定用户和设备信息
            license.user_id = activate_data.user_id
            license.device_info = activate_data.device_info
            license.status = LicenseStatus.ACTIVE
            license.activated_at = datetime.now()
            
            db.commit()
            db.refresh(license)
            
            logger.info(f"License activated: {activate_data.license_code} for user {activate_data.user_id}")
            return license
            
        except Exception as e:
            logger.error(f"Error activating license {activate_data.license_code}: {str(e)}")
            db.rollback()
            return None
    
    @staticmethod
    def increment_api_usage(db: Session, license_code: str) -> bool:
        """增加API使用次数"""
        try:
            license = db.query(License).filter(License.license_code == license_code).first()
            if not license:
                logger.error(f"License {license_code} not found")
                return False
            
            license.used_api_calls += 1
            db.commit()
            
            logger.debug(f"API usage incremented for license {license_code}: {license.used_api_calls}")
            return True
            
        except Exception as e:
            logger.error(f"Error incrementing API usage for license {license_code}: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def search_licenses(db: Session, keyword: str, skip: int = 0, limit: int = 100) -> List[License]:
        """搜索授权码"""
        try:
            query = db.query(License).filter(
                or_(
                    License.license_code.contains(keyword),
                    License.user_id.contains(keyword),
                    License.notes.contains(keyword)
                )
            )
            
            return query.order_by(License.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error searching licenses with keyword {keyword}: {str(e)}")
            return []
    
    @staticmethod
    def get_license_count(db: Session, agent_id: Optional[int] = None, 
                         product_id: Optional[int] = None,
                         status: Optional[LicenseStatus] = None,
                         user_id: Optional[str] = None) -> int:
        """获取授权码总数"""
        try:
            query = db.query(License)

            # 默认过滤掉已撤销的授权码
            query = query.filter(License.status != LicenseStatus.REVOKED)

            if agent_id is not None:
                query = query.filter(License.agent_id == agent_id)

            if product_id is not None:
                query = query.filter(License.product_id == product_id)

            if status is not None:
                query = query.filter(License.status == status)

            if user_id is not None:
                query = query.filter(License.user_id == user_id)

            return query.count()
            
        except Exception as e:
            logger.error(f"Error getting license count: {str(e)}")
            return 0
