from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class ActivationAlgorithmBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="算法名称")
    description: Optional[str] = Field(None, description="算法描述")
    algorithm_code: str = Field(..., min_length=1, description="算法代码")
    is_active: bool = Field(True, description="是否启用")
    is_default: bool = Field(False, description="是否为默认算法")
    version: str = Field("1.0", max_length=20, description="算法版本")
    author: Optional[str] = Field(None, max_length=100, description="算法作者")

class ActivationAlgorithmCreate(ActivationAlgorithmBase):
    pass

class ActivationAlgorithmUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="算法名称")
    description: Optional[str] = Field(None, description="算法描述")
    algorithm_code: Optional[str] = Field(None, min_length=1, description="算法代码")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_default: Optional[bool] = Field(None, description="是否为默认算法")
    version: Optional[str] = Field(None, max_length=20, description="算法版本")
    author: Optional[str] = Field(None, max_length=100, description="算法作者")

class ActivationAlgorithmResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    algorithm_code: str
    is_active: bool
    is_default: bool
    version: str
    author: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ActivationAlgorithmListResponse(BaseModel):
    items: list[ActivationAlgorithmResponse]
    total: int
    skip: int
    limit: int

class ActivationAlgorithmSimple(BaseModel):
    """简化的算法信息，用于选择器"""
    id: int
    name: str
    description: Optional[str]
    version: str
    is_active: bool
    is_default: bool
    
    class Config:
        from_attributes = True
