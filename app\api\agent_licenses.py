from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel
from app.database import get_db
from app.utils.auth import get_current_agent, AuthUser
from app.services.license_service import LicenseService
from app.services.agent_product_auth_service import AgentProductAuthService
from app.models.license import License, LicenseStatus
from app.models.agent_product_auth import AgentProductAuth
from app.schemas.license import LicenseCreate
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

class AgentLicenseCreate(BaseModel):
    product_id: int
    quantity: int = 1
    max_api_calls: int = -1  # -1表示无限制
    expire_date: Optional[datetime] = None
    notes: Optional[str] = None

class AgentLicenseResponse(BaseModel):
    id: int
    license_code: str
    product_id: int
    product_name: str
    max_api_calls: int
    used_api_calls: int
    expire_date: Optional[datetime]
    status: str
    created_at: datetime
    activated_at: Optional[datetime]
    
    class Config:
        from_attributes = True

@router.post("/generate", response_model=List[AgentLicenseResponse])
async def generate_licenses(
    license_data: AgentLicenseCreate,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
):
    """代理商生成授权码"""
    try:
        agent_id = current_agent.user_id
        
        # 验证代理商是否有该产品的授权
        auth = db.query(AgentProductAuth).filter(
            AgentProductAuth.agent_id == agent_id,
            AgentProductAuth.product_id == license_data.product_id,
            AgentProductAuth.is_active == True
        ).first()
        
        if not auth:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have authorization for this product"
            )
        
        # 检查授权是否过期
        if auth.expire_date and datetime.now() > auth.expire_date:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Your authorization for this product has expired"
            )
        
        # 检查剩余授权数量
        remaining_licenses = auth.max_licenses - auth.used_licenses
        if remaining_licenses < license_data.quantity:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient license quota. Remaining: {remaining_licenses}, Requested: {license_data.quantity}"
            )
        
        # 生成授权码
        generated_licenses = []
        for i in range(license_data.quantity):
            # 创建LicenseCreate对象
            license_create = LicenseCreate(
                license_code=LicenseService.generate_license_code(),  # 生成授权码
                product_id=license_data.product_id,
                agent_id=agent_id,
                max_api_calls=license_data.max_api_calls,
                expire_date=license_data.expire_date,
                notes=license_data.notes
            )

            license = LicenseService.create_license(db, license_create)
            
            if not license:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to generate license {i+1}"
                )
            
            generated_licenses.append(license)
        
        # 更新代理商已使用的授权数量
        auth.used_licenses += license_data.quantity
        db.commit()
        
        # 转换为响应格式
        result = []
        for license in generated_licenses:
            result.append(AgentLicenseResponse(
                id=license.id,
                license_code=license.license_code,
                product_id=license.product_id,
                product_name=license.product.name if license.product else "",
                max_api_calls=license.max_api_calls,
                used_api_calls=license.used_api_calls,
                expire_date=license.expire_date,
                status=license.status.value if license.status else "inactive",
                created_at=license.created_at,
                activated_at=license.activated_at
            ))
        
        logger.info(f"Agent {agent_id} generated {license_data.quantity} licenses for product {license_data.product_id}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating licenses: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate licenses"
        )

@router.get("/")
async def get_agent_licenses(
    skip: int = 0,
    limit: int = 100,
    product_id: Optional[int] = None,
    status_filter: Optional[str] = None,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
):
    """获取代理商的授权码列表"""
    try:
        agent_id = current_agent.user_id

        query = db.query(License).filter(License.agent_id == agent_id)

        # 产品过滤
        if product_id:
            query = query.filter(License.product_id == product_id)

        # 状态过滤
        if status_filter:
            try:
                status_enum = LicenseStatus(status_filter)
                query = query.filter(License.status == status_enum)
            except ValueError:
                pass

        # 获取总数
        total = query.count()

        # 获取分页数据
        licenses = query.order_by(License.created_at.desc()).offset(skip).limit(limit).all()

        result = []
        for license in licenses:
            result.append({
                "id": license.id,
                "license_code": license.license_code,
                "product_id": license.product_id,
                "product_name": license.product.name if license.product else "",
                "max_api_calls": license.max_api_calls,
                "used_api_calls": license.used_api_calls,
                "expire_date": license.expire_date.isoformat() if license.expire_date else None,
                "status": license.status.value if license.status else "inactive",
                "created_at": license.created_at.isoformat() if license.created_at else None,
                "activated_at": license.activated_at.isoformat() if license.activated_at else None
            })

        return {
            "items": result,
            "total": total,
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Error getting agent licenses: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent licenses"
        )

@router.get("/{license_id}", response_model=AgentLicenseResponse)
async def get_license_detail(
    license_id: int,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
):
    """获取授权码详情"""
    try:
        agent_id = current_agent.user_id
        
        license = db.query(License).filter(
            License.id == license_id,
            License.agent_id == agent_id
        ).first()
        
        if not license:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="License not found"
            )
        
        return AgentLicenseResponse(
            id=license.id,
            license_code=license.license_code,
            product_id=license.product_id,
            product_name=license.product.name if license.product else "",
            max_api_calls=license.max_api_calls,
            used_api_calls=license.used_api_calls,
            expire_date=license.expire_date,
            status=license.status.value if license.status else "inactive",
            created_at=license.created_at,
            activated_at=license.activated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting license detail: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get license detail"
        )

@router.put("/{license_id}")
async def update_license(
    license_id: int,
    license_data: dict,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
):
    """更新授权码"""
    try:
        agent_id = current_agent.user_id

        license = db.query(License).filter(
            License.id == license_id,
            License.agent_id == agent_id
        ).first()

        if not license:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="License not found"
            )

        # 更新允许的字段
        if 'max_api_calls' in license_data:
            license.max_api_calls = license_data['max_api_calls']

        if 'expire_date' in license_data:
            license.expire_date = license_data['expire_date']

        if 'notes' in license_data:
            license.notes = license_data['notes']

        db.commit()
        db.refresh(license)

        return AgentLicenseResponse(
            id=license.id,
            license_code=license.license_code,
            product_id=license.product_id,
            product_name=license.product.name if license.product else "",
            max_api_calls=license.max_api_calls,
            used_api_calls=license.used_api_calls,
            expire_date=license.expire_date,
            status=license.status.value if license.status else "inactive",
            created_at=license.created_at,
            activated_at=license.activated_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating license: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update license"
        )

@router.post("/{license_id}/suspend")
async def suspend_license(
    license_id: int,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
):
    """暂停授权码"""
    try:
        agent_id = current_agent.user_id

        license = db.query(License).filter(
            License.id == license_id,
            License.agent_id == agent_id
        ).first()

        if not license:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="License not found"
            )

        if license.status != LicenseStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Can only suspend active licenses"
            )

        license.status = LicenseStatus.SUSPENDED
        db.commit()

        return {"message": "License suspended successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error suspending license: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to suspend license"
        )

@router.post("/{license_id}/activate")
async def activate_license(
    license_id: int,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
):
    """恢复授权码"""
    try:
        agent_id = current_agent.user_id

        license = db.query(License).filter(
            License.id == license_id,
            License.agent_id == agent_id
        ).first()

        if not license:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="License not found"
            )

        if license.status != LicenseStatus.SUSPENDED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Can only activate suspended licenses"
            )

        license.status = LicenseStatus.ACTIVE
        db.commit()

        return {"message": "License activated successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error activating license: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate license"
        )

@router.post("/{license_id}/extend")
async def extend_license(
    license_id: int,
    extend_data: dict,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
):
    """延长授权码有效期"""
    try:
        agent_id = current_agent.user_id

        license = db.query(License).filter(
            License.id == license_id,
            License.agent_id == agent_id
        ).first()

        if not license:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="License not found"
            )

        if 'expire_date' not in extend_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="expire_date is required"
            )

        license.expire_date = extend_data['expire_date']
        db.commit()

        return {"message": "License extended successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extending license: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to extend license"
        )

@router.delete("/{license_id}")
async def deactivate_license(
    license_id: int,
    current_agent: AuthUser = Depends(get_current_agent),
    db: Session = Depends(get_db)
):
    """删除授权码（代理商只能删除未激活的授权码）"""
    try:
        agent_id = current_agent.user_id

        license = db.query(License).filter(
            License.id == license_id,
            License.agent_id == agent_id
        ).first()

        if not license:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="License not found"
            )

        # 只能删除未激活的授权码
        if license.status != LicenseStatus.INACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Can only delete inactive licenses"
            )
        
        # 更新状态为已停用
        license.status = LicenseStatus.EXPIRED
        db.commit()
        
        # 更新代理商已使用的授权数量（减少1）
        auth = db.query(AgentProductAuth).filter(
            AgentProductAuth.agent_id == agent_id,
            AgentProductAuth.product_id == license.product_id
        ).first()
        
        if auth and auth.used_licenses > 0:
            auth.used_licenses -= 1
            db.commit()
        
        logger.info(f"Agent {agent_id} deactivated license {license_id}")
        return {"message": "License deactivated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deactivating license: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate license"
        )
