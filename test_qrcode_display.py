#!/usr/bin/env python3
"""
测试二维码显示
"""

import requests
import json

def test_qrcode_display():
    """测试二维码显示功能"""
    base_url = "http://localhost:8008"
    
    print("🔍 测试二维码显示功能...")
    
    # 1. 创建支付调试
    print("\n1. 创建支付调试...")
    create_data = {
        "debug_name": "二维码测试",
        "payment_mode": "face_to_face",
        "user_id": "qr_test_user",
        "product_id": 1,
        "amount": 0.01,
        "subject": "二维码测试订单",
        "body": "测试二维码显示功能",
        "timeout_minutes": 30
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/payment-debug/debug/create",
            json=create_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                order_data = data.get("data", {})
                print("   ✅ 支付订单创建成功")
                print(f"   调试ID: {order_data.get('id')}")
                print(f"   订单号: {order_data.get('order_no')}")
                
                # 检查二维码数据
                qr_code = order_data.get('qr_code')
                qr_image = order_data.get('qr_image')
                
                if qr_code:
                    print(f"   ✅ qr_code: {qr_code}")
                else:
                    print("   ❌ 没有qr_code")
                
                if qr_image:
                    print(f"   ✅ qr_image: {qr_image[:50]}... (base64)")
                    print(f"   qr_image长度: {len(qr_image)} 字符")
                    
                    # 验证base64格式
                    if qr_image.startswith('data:image/'):
                        print("   ✅ qr_image格式正确 (data:image/...)")
                    else:
                        print("   ❌ qr_image格式不正确")
                else:
                    print("   ❌ 没有qr_image")
                
                return order_data.get('id')
            else:
                print(f"   ❌ 创建失败: {data.get('error', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.text}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
    
    return None

def test_debug_list():
    """测试调试记录列表"""
    base_url = "http://localhost:8008"
    
    print("\n2. 测试调试记录列表...")
    try:
        response = requests.get(f"{base_url}/api/payment-debug/debug/list")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                records = data.get("data", {}).get("records", [])
                print(f"   ✅ 获取到 {len(records)} 条记录")
                
                # 检查最新记录的二维码数据
                if records:
                    latest = records[0]  # 假设按时间倒序
                    print(f"   最新记录ID: {latest.get('id')}")
                    print(f"   订单号: {latest.get('order_no')}")
                    
                    if latest.get('qr_code'):
                        print(f"   ✅ 有qr_code: {latest.get('qr_code')}")
                    else:
                        print("   ❌ 没有qr_code")
                    
                    if latest.get('qr_image'):
                        print(f"   ✅ 有qr_image: {latest.get('qr_image')[:50]}...")
                    else:
                        print("   ❌ 没有qr_image")
            else:
                print(f"   ❌ 获取失败: {data.get('error')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")

if __name__ == "__main__":
    # 创建新的调试记录
    debug_id = test_qrcode_display()
    
    # 检查记录列表
    test_debug_list()
    
    print("\n📋 测试总结:")
    print("1. 检查页面是否显示二维码图片")
    print("2. 优先显示base64格式的qr_image")
    print("3. 备用显示qr_code生成的二维码")
    print("4. 访问: http://localhost:8008/admin/payment-debug")
