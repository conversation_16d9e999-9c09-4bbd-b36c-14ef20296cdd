@echo off
chcp 65001 >nul
setlocal EnableDelayedExpansion

echo.
echo === 🔒 FocuSee Network Blocking Tool ===
echo This script creates Windows Firewall rules to block FocuSee network connections
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ This script requires Administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo ✅ Running with Administrator privileges
echo.

:: 常见的FocuSee安装路径
set "FOCUSEE_PATHS="C:\Program Files\FocuSee\FocuSee.exe" "C:\Program Files (x86)\FocuSee\FocuSee.exe" "D:\Program Files\FocuSee\FocuSee.exe" "D:\Program Files (x86)\FocuSee\FocuSee.exe""

:: 查找FocuSee程序路径
set "FOCUSEE_PATH="
echo 🔍 Searching for FocuSee program...

for %%p in (%FOCUSEE_PATHS%) do (
    if exist %%p (
        set "FOCUSEE_PATH=%%~p"
        echo 📍 Found FocuSee program: %%~p
        goto :found_focusee
    )
)

:: 如果没找到，检查AppData中的路径
for %%d in ("Gemoo" "iMobie") do (
    if exist "%APPDATA%\%%~d\FocuSee\FocuSee.exe" (
        set "FOCUSEE_PATH=%APPDATA%\%%~d\FocuSee\FocuSee.exe"
        echo 📍 Found FocuSee program: %APPDATA%\%%~d\FocuSee\FocuSee.exe
        goto :found_focusee
    )
)

:: 如果仍未找到，要求用户手动输入
echo ⚠️ FocuSee program not found in common locations.
echo Please enter the full path to FocuSee.exe:
set /p "FOCUSEE_PATH=Path: "

if not exist "%FOCUSEE_PATH%" (
    echo ❌ Specified path does not exist: %FOCUSEE_PATH%
    echo.
    pause
    exit /b 1
)

:found_focusee
echo.
echo 🎯 Will block network access for: %FOCUSEE_PATH%
echo.

:: 防火墙规则名称
set "RULE_NAME=Block FocuSee Network"

:: 删除已存在的规则
echo 🔄 Checking for existing firewall rules...
netsh advfirewall firewall show rule name="%RULE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo 🗑️ Removing existing outbound rule...
    netsh advfirewall firewall delete rule name="%RULE_NAME%" >nul 2>&1
)

netsh advfirewall firewall show rule name="%RULE_NAME% (Inbound)" >nul 2>&1
if %errorLevel% equ 0 (
    echo 🗑️ Removing existing inbound rule...
    netsh advfirewall firewall delete rule name="%RULE_NAME% (Inbound)" >nul 2>&1
)

:: 创建出站规则（阻止程序连接到互联网）
echo 🚫 Creating outbound firewall rule...
netsh advfirewall firewall add rule name="%RULE_NAME%" dir=out action=block program="%FOCUSEE_PATH%" description="Block FocuSee program network access" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Outbound rule created successfully
) else (
    echo ❌ Failed to create outbound rule
    goto :error_exit
)

:: 创建入站规则（阻止外部连接到程序）
echo 🚫 Creating inbound firewall rule...
netsh advfirewall firewall add rule name="%RULE_NAME% (Inbound)" dir=in action=block program="%FOCUSEE_PATH%" description="Block external network connections to FocuSee program" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Inbound rule created successfully
) else (
    echo ❌ Failed to create inbound rule
    goto :error_exit
)

echo.
echo 🎉 Success! Firewall rules created successfully!
echo 🔒 FocuSee program network connections have been blocked.
echo.

:: 显示创建的规则
echo 📋 Created firewall rules:
netsh advfirewall firewall show rule name="%RULE_NAME%" | findstr /C:"Rule Name" /C:"Direction" /C:"Action" /C:"Program"
echo.
netsh advfirewall firewall show rule name="%RULE_NAME% (Inbound)" | findstr /C:"Rule Name" /C:"Direction" /C:"Action" /C:"Program"

echo.
echo 💡 To restore FocuSee network connections, run these commands as Administrator:
echo    netsh advfirewall firewall delete rule name="%RULE_NAME%"
echo    netsh advfirewall firewall delete rule name="%RULE_NAME% (Inbound)"
echo.
echo Press any key to exit...
pause >nul
exit /b 0

:error_exit
echo.
echo ❌ Error creating firewall rules!
echo Please check if you have sufficient privileges and try again.
echo.
pause
exit /b 1
