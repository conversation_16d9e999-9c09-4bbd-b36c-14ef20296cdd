#!/usr/bin/env python3
"""
测试支付宝回调处理
"""

import requests
import json

def test_callback_processing():
    """测试回调处理"""
    print("🔔 测试支付宝回调处理")
    print("=" * 60)
    
    # 1. 先创建一个订单
    print("\n1. 创建测试订单...")
    response = requests.post(
        "http://localhost:8008/api/payment/face-to-face",
        json={
            "user_id": "test_callback_user",
            "product_id": 1,
            "timeout_minutes": 30
        }
    )
    
    if response.status_code == 200:
        data = response.json()
        if data.get("success"):
            order_no = data.get("order_no")
            print(f"✅ 订单创建成功: {order_no}")
        else:
            print(f"❌ 订单创建失败: {data.get('error')}")
            return False
    else:
        print(f"❌ 创建订单请求失败: {response.status_code}")
        return False
    
    # 2. 模拟支付宝回调（使用真实的回调数据格式）
    print(f"\n2. 模拟支付宝回调...")
    
    # 使用从日志中看到的真实回调数据格式
    callback_data = {
        'gmt_create': '2025-08-07 14:26:58',
        'charset': 'utf-8',
        'seller_email': '<EMAIL>',
        'subject': '购买FocuSee Pro',
        'sign': 'test_signature_for_development',  # 测试签名
        'buyer_id': '****************',
        'body': f'用户test_callback_user购买FocuSee Pro',
        'invoice_amount': '98.99',
        'notify_id': '2025080701222142714188780506968603',
        'fund_bill_list': '[{"amount":"98.99","fundChannel":"ALIPAYACCOUNT"}]',
        'notify_type': 'trade_status_sync',
        'trade_status': 'TRADE_SUCCESS',
        'receipt_amount': '98.99',
        'buyer_pay_amount': '98.99',
        'app_id': '****************',
        'sign_type': 'RSA2',
        'seller_id': '****************',
        'gmt_payment': '2025-08-07 14:27:13',
        'notify_time': '2025-08-07 14:27:15',
        'version': '1.0',
        'out_trade_no': order_no,  # 使用刚创建的订单号
        'total_amount': '98.99',
        'trade_no': '2025080722001488780508296896',
        'auth_app_id': '****************',
        'buyer_logon_id': '<EMAIL>',
        'point_amount': '0.00'
    }
    
    # 发送回调请求
    try:
        response = requests.post(
            "http://localhost:8008/api/payment/alipay/notify",
            data=callback_data,
            timeout=10
        )
        
        print(f"回调响应状态码: {response.status_code}")
        print(f"回调响应内容: {response.text}")
        
        if response.status_code == 200 and response.text == "success":
            print("✅ 回调处理成功")
        else:
            print("❌ 回调处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 回调请求异常: {str(e)}")
        return False
    
    # 3. 验证订单状态是否更新
    print(f"\n3. 验证订单状态更新...")
    
    try:
        response = requests.post(
            "http://localhost:8008/api/payment/query",
            json={"order_no": order_no}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                status = data.get("status")
                print(f"订单状态: {status}")
                
                if status == "paid":
                    print("✅ 订单状态已更新为已支付")
                    return True
                else:
                    print(f"❌ 订单状态未更新: {status}")
                    return False
            else:
                print(f"❌ 查询失败: {data.get('error')}")
                return False
        else:
            print(f"❌ 查询请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 查询异常: {str(e)}")
        return False

def test_signature_verification():
    """测试签名验证功能"""
    print(f"\n" + "=" * 60)
    print("🔐 测试签名验证功能")
    
    try:
        from app.services.alipay_service import AlipayService
        
        service = AlipayService()
        
        # 测试数据
        test_data = {
            'out_trade_no': 'TEST_ORDER_123',
            'trade_status': 'TRADE_SUCCESS',
            'total_amount': '98.99',
            'app_id': '****************',
            'sign': 'test_signature',
            'sign_type': 'RSA2'
        }
        
        print("测试签名验证...")
        result = service.verify_notify(test_data)
        
        if result:
            print("✅ 签名验证通过（开发模式）")
        else:
            print("❌ 签名验证失败")
            
        return result
        
    except Exception as e:
        print(f"❌ 签名验证测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 支付宝回调处理测试")
    print("此测试将验证回调处理和签名验证功能")
    
    # 测试结果
    results = []
    
    # 1. 测试回调处理
    results.append(("回调处理", test_callback_processing()))
    
    # 2. 测试签名验证
    results.append(("签名验证", test_signature_verification()))
    
    # 显示测试结果
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("-" * 40)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} - {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    # 给出建议
    print(f"\n💡 测试总结:")
    if success_count == len(results):
        print("🎉 回调处理功能正常！")
        print("\n✨ 修复效果:")
        print("- ✅ 签名验证错误已修复")
        print("- ✅ 回调处理逻辑正常")
        print("- ✅ 订单状态正确更新")
        print("- ✅ 开发模式签名验证通过")
        
        print(f"\n🎯 现在可以:")
        print("1. 正常接收支付宝回调通知")
        print("2. 自动更新订单状态")
        print("3. 记录支付信息")
        print("4. 生成相应授权码")
        
    else:
        print("⚠️  部分测试失败，请检查:")
        print("- 确保服务器正在运行")
        print("- 检查回调处理逻辑")
        print("- 查看详细错误日志")
    
    print(f"\n📋 注意事项:")
    print("- 当前使用开发模式的签名验证")
    print("- 生产环境需要完善签名验证逻辑")
    print("- 建议查看服务器日志确认处理过程")
    
    return success_count == len(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
