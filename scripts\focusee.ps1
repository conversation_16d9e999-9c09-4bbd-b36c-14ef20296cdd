# FocuSee Update Tool PowerShell Script
# 设置GUI应用程序
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing
[System.Threading.Thread]::CurrentThread.CurrentUICulture = [System.Globalization.CultureInfo]::GetCultureInfo('zh-CN')
[System.Threading.Thread]::CurrentThread.CurrentCulture = [System.Globalization.CultureInfo]::GetCultureInfo('zh-CN')

function Show-InputForm {
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName System.Drawing

    $form = New-Object System.Windows.Forms.Form
    $form.Text = "FocuSee激活工具"
    $form.Size = New-Object System.Drawing.Size(500, 400)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.MinimizeBox = $false
    $form.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 9)

    # 标题标签
    $titleLabel = New-Object System.Windows.Forms.Label
    $titleLabel.Location = New-Object System.Drawing.Point(10, 20)
    $titleLabel.Size = New-Object System.Drawing.Size(480, 30)
    $titleLabel.Text = "FocuSee激活工具"
    $titleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
    $titleLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 14, [System.Drawing.FontStyle]::Bold)
    $form.Controls.Add($titleLabel)

    # 用户ID标签
    $userIdLabel = New-Object System.Windows.Forms.Label
    $userIdLabel.Location = New-Object System.Drawing.Point(20, 70)
    $userIdLabel.Size = New-Object System.Drawing.Size(100, 20)
    $userIdLabel.Text = "用户ID:"
    $form.Controls.Add($userIdLabel)

    # 用户ID输入框
    $userIdTextBox = New-Object System.Windows.Forms.TextBox
    $userIdTextBox.Location = New-Object System.Drawing.Point(150, 70)
    $userIdTextBox.Size = New-Object System.Drawing.Size(300, 20)
    $form.Controls.Add($userIdTextBox)

    # 订单号标签
    $orderNumberLabel = New-Object System.Windows.Forms.Label
    $orderNumberLabel.Location = New-Object System.Drawing.Point(20, 110)
    $orderNumberLabel.Size = New-Object System.Drawing.Size(100, 20)
    $orderNumberLabel.Text = "订单号:"
    $form.Controls.Add($orderNumberLabel)

    # 订单号输入框
    $orderNumberTextBox = New-Object System.Windows.Forms.TextBox
    $orderNumberTextBox.Location = New-Object System.Drawing.Point(150, 110)
    $orderNumberTextBox.Size = New-Object System.Drawing.Size(300, 20)
    $form.Controls.Add($orderNumberTextBox)
    
    # FocuSee路径标签
    $focuseePathLabel = New-Object System.Windows.Forms.Label
    $focuseePathLabel.Location = New-Object System.Drawing.Point(20, 150)
    $focuseePathLabel.Size = New-Object System.Drawing.Size(130, 20)
    $focuseePathLabel.Text = "FocuSee程序路径:"
    $form.Controls.Add($focuseePathLabel)
    
    # FocuSee路径输入框
    $focuseePathTextBox = New-Object System.Windows.Forms.TextBox
    $focuseePathTextBox.Location = New-Object System.Drawing.Point(150, 150)
    $focuseePathTextBox.Size = New-Object System.Drawing.Size(250, 20)
    $focuseePathTextBox.ReadOnly = $true
    $form.Controls.Add($focuseePathTextBox)
    
    # 浏览按钮
    $browseButton = New-Object System.Windows.Forms.Button
    $browseButton.Location = New-Object System.Drawing.Point(410, 150)
    $browseButton.Size = New-Object System.Drawing.Size(40, 23)
    $browseButton.Text = "..."
    $browseButton.Add_Click({
        $openFileDialog = New-Object System.Windows.Forms.OpenFileDialog
        $openFileDialog.Title = "请选择FocuSee.exe程序文件"
        $openFileDialog.Filter = "可执行文件 (*.exe)|*.exe"
        $openFileDialog.InitialDirectory = "C:\"
        
        if ($openFileDialog.ShowDialog($form) -eq [System.Windows.Forms.DialogResult]::OK) {
            $focuseePathTextBox.Text = $openFileDialog.FileName
        }
    })
    $form.Controls.Add($browseButton)
    
    # 自动检测按钮
    $detectButton = New-Object System.Windows.Forms.Button
    $detectButton.Location = New-Object System.Drawing.Point(150, 180)
    $detectButton.Size = New-Object System.Drawing.Size(100, 30)
    $detectButton.Text = "自动检测"
    $detectButton.Add_Click({
        # 常见的FocuSee安装路径
        $focuseePaths = @(
            "C:\Program Files\FocuSee\FocuSee.exe",
            "C:\Program Files (x86)\FocuSee\FocuSee.exe",
            "D:\Program Files\FocuSee\FocuSee.exe",
            "D:\Program Files (x86)\FocuSee\FocuSee.exe"
        )

        # 查找FocuSee程序路径
        $focuseePath = $null
        foreach ($path in $focuseePaths) {
            if (Test-Path $path) {
                $focuseePath = $path
                break
            }
        }

        # 如果没找到，检查AppData中的路径
        if (-not $focuseePath) {
            foreach ($dir in @("Gemoo", "iMobie")) {
                $path = "$env:APPDATA\$dir\FocuSee\FocuSee.exe"
                if (Test-Path $path) {
                    $focuseePath = $path
                    break
                }
            }
        }
        
        if ($focuseePath) {
            $focuseePathTextBox.Text = $focuseePath
            [System.Windows.Forms.MessageBox]::Show("已找到FocuSee程序: $focuseePath", "自动检测", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
        } else {
            [System.Windows.Forms.MessageBox]::Show("未找到FocuSee程序，请手动选择", "自动检测", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Warning)
        }
    })
    $form.Controls.Add($detectButton)
    
    # 路径说明标签
    $pathInfoLabel = New-Object System.Windows.Forms.Label
    $pathInfoLabel.Location = New-Object System.Drawing.Point(20, 220)
    $pathInfoLabel.Size = New-Object System.Drawing.Size(430, 40)
    $pathInfoLabel.ForeColor = [System.Drawing.Color]::DarkBlue
    $form.Controls.Add($pathInfoLabel)

    # 进度条
    $progressBar = New-Object System.Windows.Forms.ProgressBar
    $progressBar.Location = New-Object System.Drawing.Point(20, 270)
    $progressBar.Size = New-Object System.Drawing.Size(430, 20)
    $progressBar.Style = "Continuous"
    $progressBar.Minimum = 0
    $progressBar.Maximum = 100
    $progressBar.Value = 0
    $progressBar.Visible = $false
    $form.Controls.Add($progressBar)

    # 状态标签
    $statusLabel = New-Object System.Windows.Forms.Label
    $statusLabel.Location = New-Object System.Drawing.Point(20, 300)
    $statusLabel.Size = New-Object System.Drawing.Size(430, 20)
    $statusLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
    $statusLabel.Visible = $false
    $form.Controls.Add($statusLabel)

    # 确定按钮
    $okButton = New-Object System.Windows.Forms.Button
    $okButton.Location = New-Object System.Drawing.Point(120, 330)
    $okButton.Size = New-Object System.Drawing.Size(100, 30)
    $okButton.Text = "开始激活"
    $okButton.DialogResult = [System.Windows.Forms.DialogResult]::OK
    $form.AcceptButton = $okButton
    $form.Controls.Add($okButton)

    # 取消按钮
    $cancelButton = New-Object System.Windows.Forms.Button
    $cancelButton.Location = New-Object System.Drawing.Point(250, 330)
    $cancelButton.Size = New-Object System.Drawing.Size(100, 30)
    $cancelButton.Text = "取消"
    $cancelButton.DialogResult = [System.Windows.Forms.DialogResult]::Cancel
    $form.CancelButton = $cancelButton
    $form.Controls.Add($cancelButton)

    # 显示表单
    $result = $form.ShowDialog()

    if ($result -eq [System.Windows.Forms.DialogResult]::OK) {
        $userId = $userIdTextBox.Text
        $orderNumber = $orderNumberTextBox.Text
        $focuseePath = $focuseePathTextBox.Text
        
        if ([string]::IsNullOrEmpty($userId) -or [string]::IsNullOrEmpty($orderNumber)) {
            [System.Windows.Forms.MessageBox]::Show("用户ID和订单号不能为空!", "输入错误", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
            return $null
        }
        
        return @{
            UserId = $userId
            OrderNumber = $orderNumber
            FocuseePath = $focuseePath
            ProgressBar = $progressBar
            StatusLabel = $statusLabel
            Form = $form
        }
    } else {
        return $null
    }
}

function Show-MessageBox {
    param (
        [string]$Message,
        [string]$Title = "FocuSee激活工具",
        [System.Windows.Forms.MessageBoxButtons]$Buttons = [System.Windows.Forms.MessageBoxButtons]::OK,
        [System.Windows.Forms.MessageBoxIcon]$Icon = [System.Windows.Forms.MessageBoxIcon]::Information
    )

    Add-Type -AssemblyName System.Windows.Forms
    [System.Windows.Forms.MessageBox]::Show($Message, $Title, $Buttons, $Icon)
}

function Update-Progress {
    param (
        [System.Windows.Forms.ProgressBar]$ProgressBar,
        [System.Windows.Forms.Label]$StatusLabel,
        [int]$Value,
        [string]$Status
    )

    if ($null -ne $ProgressBar -and $null -ne $StatusLabel) {
        $ProgressBar.Visible = $true
        $StatusLabel.Visible = $true
        $ProgressBar.Value = $Value
        $StatusLabel.Text = $Status
        [System.Windows.Forms.Application]::DoEvents()
    }
}

function Block-FocuSeeNetwork {
    param (
        [System.Windows.Forms.ProgressBar]$ProgressBar,
        [System.Windows.Forms.Label]$StatusLabel,
        [string]$FocuseePath
    )

    Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 40 -Status "正在配置防火墙规则..."

    # 检查管理员权限
    $isAdmin = ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    if (-not $isAdmin) {
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 45 -Status "需要管理员权限来配置防火墙..."
        return
    }

    # 如果没有提供FocuSee路径或路径不存在，则跳过防火墙配置
    if ([string]::IsNullOrEmpty($FocuseePath) -or -not (Test-Path $FocuseePath)) {
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 50 -Status "未提供有效的FocuSee程序路径，跳过网络阻断..."
        return
    }

    Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 55 -Status "正在阻断FocuSee网络连接..."
    Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 56 -Status "使用路径: $FocuseePath"

    # 防火墙规则名称
    $ruleName = "Block FocuSee Network"

    # 删除已存在的规则
    try {
        $existingRule = Get-NetFirewallRule -DisplayName $ruleName -ErrorAction SilentlyContinue
        if ($existingRule) {
            Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 57 -Status "正在删除已存在的防火墙规则..."
            Remove-NetFirewallRule -DisplayName $ruleName -ErrorAction SilentlyContinue
            Remove-NetFirewallRule -DisplayName "$ruleName (Inbound)" -ErrorAction SilentlyContinue
        }
    } catch {
        # 忽略错误，继续创建新规则
    }

    # 创建出站规则（阻止程序连接到互联网）
    try {
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 60 -Status "正在创建出站防火墙规则..."
        New-NetFirewallRule -DisplayName $ruleName -Direction Outbound -Program $FocuseePath -Action Block -Profile Domain,Private,Public -Description "Block FocuSee program network access" | Out-Null
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 65 -Status "已创建出站防火墙规则"
    } catch {
        $errorMessage = $_.Exception.Message
        try {
            $errorMessage = [System.Text.Encoding]::UTF8.GetString([System.Text.Encoding]::GetEncoding("ISO-8859-1").GetBytes($errorMessage))
        } catch {}
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 60 -Status "创建出站防火墙规则失败: $errorMessage"
        Show-MessageBox -Message "创建出站防火墙规则失败: $errorMessage" -Title "防火墙错误" -Icon Error
    }

    # 创建入站规则（阻止外部连接到程序）
    try {
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 70 -Status "正在创建入站防火墙规则..."
        New-NetFirewallRule -DisplayName "$ruleName (Inbound)" -Direction Inbound -Program $FocuseePath -Action Block -Profile Domain,Private,Public -Description "Block external network connections to FocuSee program" | Out-Null
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 75 -Status "已创建入站防火墙规则"
    } catch {
        $errorMessage = $_.Exception.Message
        try {
            $errorMessage = [System.Text.Encoding]::UTF8.GetString([System.Text.Encoding]::GetEncoding("ISO-8859-1").GetBytes($errorMessage))
        } catch {}
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 70 -Status "创建入站防火墙规则失败: $errorMessage"
        Show-MessageBox -Message "创建入站防火墙规则失败: $errorMessage" -Title "防火墙错误" -Icon Error
    }
    
    # 验证规则是否创建成功
    try {
        $createdRules = Get-NetFirewallRule -DisplayName "*FocuSee*" -ErrorAction SilentlyContinue
        if ($createdRules) {
            Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 80 -Status "防火墙规则创建成功！"
        } else {
            Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value 80 -Status "无法验证防火墙规则是否创建成功"
        }
    } catch {
        # 忽略验证错误
    }
}

function Process-ZipFile {
    param (
        [string]$ZipPath,
        [string]$TopFolder,
        [System.Windows.Forms.ProgressBar]$ProgressBar,
        [System.Windows.Forms.Label]$StatusLabel,
        [int]$ProgressStart,
        [int]$ProgressEnd
    )

    $targetDir = "$env:APPDATA\$TopFolder"
    $focusDir = "$targetDir\FocuSee"

    Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value $ProgressStart -Status "正在处理 $(Split-Path $ZipPath -Leaf)..."

    # 检查 ZIP 文件是否存在
    if (-not (Test-Path $ZipPath)) {
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value $ProgressStart -Status "未找到 $(Split-Path $ZipPath -Leaf)，跳过..."
        return
    }

    # 删除旧的 FocuSee 文件夹
    if (Test-Path $focusDir) {
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value ($ProgressStart + 2) -Status "正在删除旧的FocuSee文件夹..."
        Remove-Item -Path $focusDir -Recurse -Force
    }

    # 创建目标目录（如果不存在）
    if (-not (Test-Path $targetDir)) {
        New-Item -Path $targetDir -ItemType Directory -Force | Out-Null
    }

    # 解压缩
    Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value ($ProgressStart + 5) -Status "正在解压缩 $(Split-Path $ZipPath -Leaf)..."
    try {
        Expand-Archive -Path $ZipPath -DestinationPath $targetDir -Force
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value $ProgressEnd -Status "已完成 $(Split-Path $ZipPath -Leaf) 处理"
    } catch {
        Update-Progress -ProgressBar $ProgressBar -StatusLabel $StatusLabel -Value $ProgressEnd -Status "解压缩 $(Split-Path $ZipPath -Leaf) 失败"
    }
}

# 主程序
function Main {
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName System.Drawing
    
    # 显示输入表单
    $inputData = Show-InputForm
    
    if ($null -eq $inputData) {
        # 用户取消了操作
        return
    }
    
    # 获取表单数据
    $userId = $inputData.UserId
    $orderNumber = $inputData.OrderNumber
    $focuseePath = $inputData.FocuseePath
    $progressBar = $inputData.ProgressBar
    $statusLabel = $inputData.StatusLabel
    $form = $inputData.Form
    
    # 显示进度条和状态
    $progressBar.Visible = $true
    $statusLabel.Visible = $true
    
    # 设置云服务器URL
    $serverUrl = "http://*************:8008/api"
    $downloadUrl = "http://*************:8008/downloads"

    # 获取系统信息用于统计
    Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 5 -Status "正在收集系统信息..."
    try {
        $systemId = (Get-WmiObject -Class Win32_OperatingSystem).SerialNumber
        $computerName = $env:COMPUTERNAME
        $localIp = (Test-Connection -ComputerName $env:COMPUTERNAME -Count 1).IPV4Address.IPAddressToString
    } catch {
        $systemId = "Unknown"
        $computerName = "Unknown"
        $localIp = "Unknown"
    }

    # 验证用户并记录下载
    Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 10 -Status "正在验证用户身份..."
    $authData = @{
        user_id = $userId
        order_number = $orderNumber
        system_id = $systemId
        computer_name = $computerName
        ip_address = $localIp
    } | ConvertTo-Json -Depth 5 -Compress

    try {
        # 使用UTF-8编码处理API请求和响应
        $headers = @{
            "Content-Type" = "application/json; charset=utf-8"
            "Accept" = "application/json; charset=utf-8"
        }
        
        $response = Invoke-RestMethod -Uri "$serverUrl/auth/verify-order" -Method Post -Body $authData -ContentType 'application/json; charset=utf-8' -Headers $headers
        
        if ($response.success -eq $true) {
            Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 20 -Status "用户验证成功!"
        } else {
            # 确保错误消息正确显示中文
            $errorMessage = [System.Text.Encoding]::UTF8.GetString([System.Text.Encoding]::GetEncoding("ISO-8859-1").GetBytes($response.message))
            Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 100 -Status "验证失败: $errorMessage"
            Show-MessageBox -Message "验证失败! 错误信息: $errorMessage" -Title "验证错误" -Icon Error
            return
        }
    } catch {
        $errorMessage = $_.Exception.Message
        # 尝试转换可能的编码问题
        try {
            $errorMessage = [System.Text.Encoding]::UTF8.GetString([System.Text.Encoding]::GetEncoding("ISO-8859-1").GetBytes($errorMessage))
        } catch {
            # 如果转换失败，使用原始错误信息
        }
        
        Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 100 -Status "验证失败: $errorMessage"
        Show-MessageBox -Message "验证失败! 错误: $errorMessage" -Title "验证错误" -Icon Error
        return
    }

    # 创建防火墙规则阻断FocuSee网络连接
    Block-FocuSeeNetwork -ProgressBar $progressBar -StatusLabel $statusLabel -FocuseePath $focuseePath

    # 创建临时目录
    Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 30 -Status "正在创建临时目录..."
    $tempDir = "$env:TEMP\FocuSee_Update_$(Get-Random)"
    New-Item -Path $tempDir -ItemType Directory -Force | Out-Null

    # 记录下载统计
    Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 35 -Status "正在记录下载统计..."
    try {
        $statData = $authData | ConvertFrom-Json
        $statData | Add-Member -NotePropertyName 'file' -NotePropertyValue 'Gemoo-FocuSee.zip'
        $statJson = $statData | ConvertTo-Json -Depth 5 -Compress
        Invoke-RestMethod -Uri "$serverUrl/download_stats" -Method Post -Body $statJson -ContentType 'application/json; charset=utf-8' -Headers $headers | Out-Null
    } catch {
        # 忽略统计错误
    }

    # 下载第一个文件
    Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 40 -Status "正在下载 Gemoo-FocuSee.zip..."
    try {
        Invoke-WebRequest -Uri "$downloadUrl/Gemoo-FocuSee.zip?user=$userId&key=$orderNumber" -OutFile "$tempDir\Gemoo-FocuSee.zip" -Headers $headers
        Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 50 -Status "Gemoo-FocuSee.zip 下载成功!"
    } catch {
        $errorMessage = $_.Exception.Message
        try {
            $errorMessage = [System.Text.Encoding]::UTF8.GetString([System.Text.Encoding]::GetEncoding("ISO-8859-1").GetBytes($errorMessage))
        } catch {}
        Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 50 -Status "下载 Gemoo-FocuSee.zip 失败! $errorMessage"
    }

    # 记录第二个文件的下载统计
    try {
        $statData = $authData | ConvertFrom-Json
        $statData | Add-Member -NotePropertyName 'file' -NotePropertyValue 'iMobie-FocuSee.zip'
        $statJson = $statData | ConvertTo-Json -Depth 5 -Compress
        Invoke-RestMethod -Uri "$serverUrl/download_stats" -Method Post -Body $statJson -ContentType 'application/json; charset=utf-8' -Headers $headers | Out-Null
    } catch {
        # 忽略统计错误
    }

    # 下载第二个文件
    Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 55 -Status "正在下载 iMobie-FocuSee.zip..."
    try {
        Invoke-WebRequest -Uri "$downloadUrl/iMobie-FocuSee.zip?user=$userId&key=$orderNumber" -OutFile "$tempDir\iMobie-FocuSee.zip" -Headers $headers
        Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 65 -Status "iMobie-FocuSee.zip 下载成功!"
    } catch {
        $errorMessage = $_.Exception.Message
        try {
            $errorMessage = [System.Text.Encoding]::UTF8.GetString([System.Text.Encoding]::GetEncoding("ISO-8859-1").GetBytes($errorMessage))
        } catch {}
        Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 65 -Status "下载 iMobie-FocuSee.zip 失败! $errorMessage"
    }

    # 处理下载的文件
    Process-ZipFile -ZipPath "$tempDir\Gemoo-FocuSee.zip" -TopFolder "Gemoo" -ProgressBar $progressBar -StatusLabel $statusLabel -ProgressStart 70 -ProgressEnd 80
    Process-ZipFile -ZipPath "$tempDir\iMobie-FocuSee.zip" -TopFolder "iMobie" -ProgressBar $progressBar -StatusLabel $statusLabel -ProgressStart 80 -ProgressEnd 90

    # 清理临时文件
    Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 95 -Status "正在清理临时文件..."
    Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue

    Update-Progress -ProgressBar $progressBar -StatusLabel $statusLabel -Value 100 -Status "完成! FocuSee文件已更新"
    Show-MessageBox -Message "完成! FocuSee文件已成功更新。" -Title "FocuSee更新完成"
    
    # 关闭表单
    $form.Close()
}

# 执行主程序
Main